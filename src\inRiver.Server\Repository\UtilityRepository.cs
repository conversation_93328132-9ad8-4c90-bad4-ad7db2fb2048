namespace inRiver.Server.Repository
{
    using System.Collections.Generic;
    using System.Globalization;
    using System.Threading.Tasks;
    using inRiver.Remoting.Security;
    using inRiver.Server.DataAccess;
    using inRiver.Server.Error;
    using inRiver.Server.Request;
    using SendGrid;
    using SendGrid.Helpers.Mail;

    public class UtilityRepository
    {
        private readonly RequestContext context;

        public UtilityRepository(RequestContext context)
        {
            if (!context.UserHasPermission(UserPermission.View))
            {
                throw ErrorUtility.GetSecurityException("User does not have permission to view data in inRiver");
            }

            this.context = context;
        }

        public async Task SendMail(string subject, string body, string toAddress, bool sendAsHtml)
        {
            var client = new SendGridClient(this.context.SendGridApiKey);
            var from = new EmailAddress(this.context.SmtpSendUser, this.context.SmtpSendUserName);
            var to = new EmailAddress(toAddress);

            var msg = new SendGridMessage()
            {
                From = from,
                Subject = subject
            };
            if (sendAsHtml)
            {
                msg.HtmlContent = body;
            }
            else
            {
                msg.PlainTextContent = body;
            }
            msg.AddTo(to);

            await client.SendEmailAsync(msg);
        }

        public List<CultureInfo> GetAllLanguages() => this.context.DataPersistance.GetAllLanguages();

        public string GetServerSetting(string name) => this.context.DataPersistance.GetServerSetting(name);
    }
}
