namespace LongRunningJobActor
{
    using System;
    using System.Collections.Concurrent;
    using System.Collections.Generic;
    using System.Diagnostics;
    using System.Globalization;
    using System.Linq;
    using System.Linq.Expressions;
    using System.Threading;
    using System.Threading.Tasks;
    using Abstraction;
    using Code;
    using inriver.Expressions.Client;
    using inriver.Expressions.Client.Constants;
    using inRiver.Core.Constants.LongRunningJobs;
    using inRiver.Core.Enum;
    using inRiver.Core.EnvironmentSettings;
    using inRiver.Core.Models;
    using inRiver.Core.Models.inRiver.ApplyExpressions;
    using inRiver.Core.Models.inRiver.DeleteAllLinksForLinkType;
    using inRiver.Core.Models.inRiver.ExcelExport;
    using inRiver.Core.Models.inRiver.ExcelImport;
    using inRiver.Core.Models.inRiver.MassExcludeTypes;
    using inRiver.Core.Models.inRiver.MassUpdate;
    using inRiver.Core.Models.inRiver.Workflow;
    using inRiver.Core.Persistance;
    using inRiver.Core.Persistance.ThirdDataLayer;
    using inRiver.Core.Repository;
    using inRiver.Core.Util;
    using inRiver.Remoting.Dto;
    using inRiver.Remoting.Objects;
    using inRiver.Server.DataAccess.ExpressionUtil;
    using inRiver.Server.EventPublishing;
    using inRiver.Server.MassUpdate;
    using inRiver.Server.Repository;
    using inRiver.Server.Request;
    using inRiver.Server.Syndication;
    using inRiver.Server.Syndication.Service;
    using inRiver.Server.Syndication.Service.Interfaces;
    using inRiver.Server.Util.Workflow;
    using Inriver.StackEssentials;
    using Inriver.StackEssentials.Config;
    using Inriver.StackEssentials.Redis;
    using Interfaces;
    using LongRunningJob.Core.Cache;
    using LongRunningJob.Core.Constants;
    using LongRunningJob.Core.Models;
    using Microsoft.ApplicationInsights;
    using Microsoft.ApplicationInsights.Metrics;
    using Microsoft.Extensions.Logging;
    using Microsoft.ServiceFabric.Actors;
    using Microsoft.ServiceFabric.Actors.Runtime;
    using Newtonsoft.Json;
    using Newtonsoft.Json.Linq;
    using Serilog;
    using Serilog.Context;
    using Services;
    using StackExchange.Redis;
    using Telemetry.Constants;
    using CompletenessRepository = inRiver.Server.Repository.CompletenessRepository;
    using LoadLevel = inRiver.Remoting.Objects.LoadLevel;
    using LogLevel = inRiver.Log.LogLevel;

    [StatePersistence(StatePersistence.Persisted)]
    internal class LongRunningJobActor : Actor, ILongRunningJobActor
    {
        private const int MBtoBytes = 1024 * 1024;
        private readonly TimeSpan FiveSecondsDelay = TimeSpan.FromSeconds(5);
        private readonly MetricConfiguration metricConfiguration;
        private readonly TelemetryClient telemetryClient;
        private readonly CancellationTokenSource tokenSource;
        private readonly LongRunningJobCache longRunningJobCache;
        private readonly ISyndicationService syndicationService;
        private readonly ISyndicationJobResultService syndicationJobResultService;
        private LongRunningJobActorHelper jobActorHelper;

        private Stopwatch stopWatch;

        private static readonly MetricIdentifier metricIdentifier = new MetricIdentifier(
            nameof(LongRunningJobActor),
            MetricName.RequestDuration,
            Dimension.Environment,
            Dimension.Method,
            Dimension.Success,
            Dimension.JobId);

        private string customer;
        private string environment;
        private ActorMethodState methodState = ActorMethodState.Failed;

        private int currentLongRunningJobId;

        private JobRepository jobRepository;
        private object jobRepositoryInitLock = new object();
        private CancellationTokenSource jobCancellationProcessTokenSource = new CancellationTokenSource();

        public LongRunningJobActor(ActorService actorService, ActorId actorId, TelemetryClient telemetryClient)
            : base(actorService, actorId)
        {
            StackEssentialsInitializer.Init(Util.KeyVaultBaseUrl, Util.StackConfigSecretName);
            DataApiInitializer.Init(Util.DataApiUrl, Util.DataJobServiceUrl, Util.KeyVaultBaseUrl, Util.StackConfigSecretName, Constants.CloudRoleName);
            ExpressionClient.Init(Util.ExpressionWorkerServiceUrl, Constants.CloudRoleName);
            MessagingServiceClient.Init(Util.MessagingServiceUrl);

            ConfigureAppSettings();
            this.telemetryClient = telemetryClient;
            this.metricConfiguration = new MetricConfiguration(10000, 10000, new MetricSeriesConfigurationForMeasurement(false));

            this.tokenSource = new CancellationTokenSource();
            this.longRunningJobCache = new LongRunningJobCache();
            this.syndicationJobResultService = new SyndicationJobResultService();
            this.syndicationService = new SyndicationService(this.longRunningJobCache, this.tokenSource, new ServiceFabricSyndicationConfigService(), this.syndicationJobResultService);
            this.InitializeJobCancellationProcess();
        }

        private static void ConfigureAppSettings()
        {
            AppSettings.KeyVaultBaseUrl = new Uri(Util.KeyVaultBaseUrl);
            AppSettings.KeyVaultServerSettingsEncryptionKeyName = Util.GetKeyVaultServerSettingsEncryptionKeyName();
        }

        private void SetDimensions(string customerSafeName, string environmentSafeName, int longRunningJobId)
        {
            this.customer = customerSafeName;
            this.environment = environmentSafeName;
            this.currentLongRunningJobId = longRunningJobId;
        }

        private void DeleteJobCancelledState()
        {
            try
            {
                this.longRunningJobCache.DeleteJobCancelledState(this.Id.GetStringId());
            }
            catch (Exception ex)
            {
                Log.Error(
                    ex,
                    "Error occurred when trying to delete job cancelled state for {customer}/{environment}",
                    this.customer,
                    this.environment);
            }
        }

        protected override Task OnPreActorMethodAsync(ActorMethodContext actorMethodContext)
        {
            this.stopWatch = Stopwatch.StartNew();
            return base.OnPreActorMethodAsync(actorMethodContext);
        }

        protected override Task OnPostActorMethodAsync(ActorMethodContext actorMethodContext)
        {
            this.DeleteJobCancelledState();
            this.jobCancellationProcessTokenSource.Cancel();
            this.stopWatch.Stop();

            if (!this.CheckIfReviewJob(this.currentLongRunningJobId, actorMethodContext.MethodName))
            {
                var telemetryCapReached = !this.telemetryClient
                    .GetMetric(metricIdentifier, this.metricConfiguration)
                    .TrackValue(this.stopWatch.ElapsedMilliseconds,
                        $"{this.customer}/{this.environment}",
                        actorMethodContext.MethodName,
                        this.methodState.ToString(),
                        this.currentLongRunningJobId.ToString());
                if (telemetryCapReached)
                {
                    Log.Error($"Data series per metric cap was reached for {this.customer}/{this.environment}");
                }
            }

            return base.OnPostActorMethodAsync(actorMethodContext);
        }

        protected override Task OnActivateAsync()
        {
            ActorEventSource.Current.ActorMessage(this, "Actor activated.");

            return base.OnActivateAsync();
        }

        public async Task ExecuteCalculateCompletenessAsync(EnvironmentContextData environmentContextData, int longRunningJobId, string entitytypeid)
        {
            using (LogContext.PushProperty(Dimension.JobId, longRunningJobId))
            using (LogContext.PushProperty(Dimension.Method, nameof(ExecuteCalculateCompletenessAsync)))
            using (LogContext.PushProperty(Dimension.Environment, $"{environmentContextData?.CustomerSafeName}/{environmentContextData?.EnvironmentSafeName}"))
            using (var proc = Process.GetCurrentProcess())
            {
                Log.Information($"Starting ExecuteCalculateCompleteness for {entitytypeid}");
                var onStartMemoryUsed = this.LogMemoryInMB("On start memory used {MemoryUsed} MB.", proc.PrivateMemorySize64);

                await this.ExecuteCalculateCompletenessInternalAsync(environmentContextData, longRunningJobId, entitytypeid);

                proc.Refresh();
                var onEndMemoryUsed = this.LogMemoryInMB("On finish memory used {MemoryUsed} MB.", proc.PrivateMemorySize64);
                _ = this.LogMemoryInMB("Memory delta {MemoryDelta} MB", onEndMemoryUsed - onStartMemoryUsed);
            }
        }

        private async Task ExecuteCalculateCompletenessInternalAsync(EnvironmentContextData environmentContextData, int longRunningJobId, string entitytypeid)
        {
            var requestContext = this.GetRequestContextForEnvironment(environmentContextData);

            try
            {
                this.SetDimensions(requestContext.CustomerSafeName, requestContext.EnvironmentSafeName, longRunningJobId);
                this.CreateJobRepository(environmentContextData, requestContext.Username);
                this.jobActorHelper.UpdateLongRunningJobState(this.Id.GetStringId(), longRunningJobId, LongRunningJobsStatus.Running);
                this.tokenSource.Token.ThrowIfCancellationRequested();

                var completenessRepository = new CompletenessRepository(requestContext);
                var definitionId = completenessRepository.GetCompletenessDefinitionIdForEntityType(entitytypeid);
                if (definitionId != null)
                {
                    var result = await completenessRepository.ReCalculateCompletenessForDefinitionAsync(definitionId.Value, this.tokenSource.Token);

                    if (result.FailedToProcessEntityIds.Any())
                    {
                        var entityIdsToDisplay = string.Join(", ", result.FailedToProcessEntityIds.Take(20));
                        entityIdsToDisplay += result.FailedToProcessEntityIds.Count() > 20 ? "..." : ".";

                        var errorMessage = $"Calculate completeness failed for entity type {entitytypeid}, entity Ids: {entityIdsToDisplay}";
                        requestContext.Logging.Error(errorMessage, new Exception(errorMessage), requestContext.Module, requestContext.Username);
                        this.jobActorHelper?.UpdateLongRunningJobState(this.Id.GetStringId(), longRunningJobId, LongRunningJobsStatus.Error);
                        return;
                    }
                }

                this.FullLogInformation(requestContext, $"Calculate completeness finished for entity type {entitytypeid}.");
                this.jobActorHelper.UpdateLongRunningJobState(this.Id.GetStringId(), longRunningJobId, LongRunningJobsStatus.Finished);
                this.methodState = ActorMethodState.Success;
            }
            catch (OperationCanceledException)
            {
                this.LogJobIsCancelled(requestContext, longRunningJobId, LongRunningJobsJobType.CalculateCompleteness);
                return;
            }
            catch (Exception e)
            {
                requestContext.Log((inRiver.Remoting.Log.LogLevel)LogLevel.Error, $"Calculate completeness failed for entity type {entitytypeid}.", e);
                Log.Error(e, "Error occurred in ExecuteCalculateCompleteness for EntityTypeId: {entitytypeid}, Customer: {customerSafeName}/{environmentSafeName}.", entitytypeid, environmentContextData?.CustomerSafeName, environmentContextData?.EnvironmentSafeName);
                this.jobActorHelper?.UpdateLongRunningJobState(this.Id.GetStringId(), longRunningJobId, LongRunningJobsStatus.Error);
            }
        }

        public Task ExecuteRebuildChannel(EnvironmentContextData environmentContextData, int longRunningJobId, int channelId)
        {
            using (LogContext.PushProperty(Dimension.JobId, longRunningJobId))
            using (LogContext.PushProperty(Dimension.Method, nameof(ExecuteRebuildChannel)))
            using (LogContext.PushProperty(Dimension.Environment, $"{environmentContextData?.CustomerSafeName}/{environmentContextData?.EnvironmentSafeName}"))
            using (var proc = Process.GetCurrentProcess())
            {
                var onStartMemoryUsed = this.LogMemoryInMB("On start memory used {MemoryUsed} MB.", proc.PrivateMemorySize64);

                this.ExecuteRebuildChannelInternal(environmentContextData, longRunningJobId, channelId);

                proc.Refresh();
                var onEndMemoryUsed = this.LogMemoryInMB("On finish memory used {MemoryUsed} MB.", proc.PrivateMemorySize64);
                _ = this.LogMemoryInMB("Memory delta {MemoryDelta} MB", onEndMemoryUsed - onStartMemoryUsed);
            }

            return Task.FromResult(true);
        }

        private void ExecuteRebuildChannelInternal(EnvironmentContextData environmentContextData, int longRunningJobId, int channelId)
        {
            Log.Information($"Starting rebuild channel for {environmentContextData.EnvironmentFullName} and channelId {channelId}");
            var requestContext = this.GetRequestContextForEnvironment(environmentContextData);
            try
            {
                this.SetDimensions(requestContext.CustomerSafeName, requestContext.EnvironmentSafeName, longRunningJobId);
                this.CreateJobRepository(environmentContextData, requestContext.Username);

                this.jobActorHelper.UpdateLongRunningJobState(this.Id.GetStringId(), longRunningJobId, LongRunningJobsStatus.Running);
                this.tokenSource.Token.ThrowIfCancellationRequested();

                var channelRepository = new inRiver.Server.Repository.ChannelRepository(requestContext);
                channelRepository.ReloadChannel(channelId, this.tokenSource.Token);

                this.FullLogInformation(requestContext, "Rebuild channel finished.");
                this.methodState = ActorMethodState.Success;
                this.jobActorHelper.UpdateLongRunningJobState(this.Id.GetStringId(), longRunningJobId, LongRunningJobsStatus.Finished);
            }
            catch (OperationCanceledException)
            {
                this.LogJobIsCancelled(requestContext, longRunningJobId, LongRunningJobsJobType.RebuildChannel);
                return;
            }
            catch (Exception e)
            {
                Log.Error(e, "Error occurred in ExecuteRebuildChannel for Customer: {customerSafeName}/{environmentSafeName}.", environmentContextData?.CustomerSafeName, environmentContextData?.EnvironmentSafeName);
                this.jobActorHelper?.UpdateLongRunningJobState(this.Id.GetStringId(), longRunningJobId, LongRunningJobsStatus.Error);
            }
        }

        public Task ExecuteSynchronizeChannel(EnvironmentContextData environmentContextData, int longRunningJobId, int channelId)
        {
            using (LogContext.PushProperty(Dimension.JobId, longRunningJobId))
            using (LogContext.PushProperty(Dimension.Method, nameof(ExecuteSynchronizeChannel)))
            using (LogContext.PushProperty(Dimension.Environment, $"{environmentContextData?.CustomerSafeName}/{environmentContextData?.EnvironmentSafeName}"))
            using (var proc = Process.GetCurrentProcess())
            {
                Log.Information($"Starting SynchronizeChannel for {channelId}");
                var onStartMemoryUsed = this.LogMemoryInMB("On start memory used {MemoryUsed} MB.", proc.PrivateMemorySize64);

                this.ExecuteSynchronizeChannelInternal(environmentContextData, longRunningJobId, channelId);

                proc.Refresh();
                var onEndMemoryUsed = this.LogMemoryInMB("On finish memory used {MemoryUsed} MB.", proc.PrivateMemorySize64);
                _ = this.LogMemoryInMB("Memory delta {MemoryDelta} MB", onEndMemoryUsed - onStartMemoryUsed);
            }

            return Task.FromResult(true);
        }

        private void ExecuteSynchronizeChannelInternal(EnvironmentContextData environmentContextData, int longRunningJobId, int channelId)
        {
            var requestContext = this.GetRequestContextForEnvironment(environmentContextData);

            try
            {
                this.SetDimensions(requestContext.CustomerSafeName, requestContext.EnvironmentSafeName, longRunningJobId);
                this.CreateJobRepository(environmentContextData, requestContext.Username);

                this.jobActorHelper.UpdateLongRunningJobState(this.Id.GetStringId(), longRunningJobId, LongRunningJobsStatus.Running);
                this.tokenSource.Token.ThrowIfCancellationRequested();

                var channelRepository = new inRiver.Server.Repository.ChannelRepository(requestContext);
                channelRepository.SynchronizeChannel(channelId, this.tokenSource.Token);

                this.FullLogInformation(requestContext, "Synchronize channel finished.");
                this.methodState = ActorMethodState.Success;
                this.jobActorHelper.UpdateLongRunningJobState(this.Id.GetStringId(), longRunningJobId, LongRunningJobsStatus.Finished);
            }
            catch (OperationCanceledException)
            {
                this.LogJobIsCancelled(requestContext, longRunningJobId, LongRunningJobsJobType.SynchronizeChannel);
                return;
            }
            catch (Exception e)
            {
                Log.Error(e, "Error occurred in ExecuteSynchronizeChannel for Customer: {customerSafeName}/{environmentSafeName}.", environmentContextData?.CustomerSafeName, environmentContextData?.EnvironmentSafeName);
                this.jobActorHelper?.UpdateLongRunningJobState(this.Id.GetStringId(), longRunningJobId, LongRunningJobsStatus.Error);
            }
        }

        public async Task ExcelExportAsync(EnvironmentContextData environmentContextData, int longRunningJobId, string username, ExcelExportModel excelExportModel)
        {
            using (LogContext.PushProperty(Dimension.JobId, longRunningJobId))
            using (LogContext.PushProperty(Dimension.Method, nameof(ExcelExportAsync)))
            using (LogContext.PushProperty(Dimension.Username, username))
            using (LogContext.PushProperty(Dimension.Environment, $"{environmentContextData?.CustomerSafeName}/{environmentContextData?.EnvironmentSafeName}"))
            using (var proc = Process.GetCurrentProcess())
            {
                Log.Information($"Starting ExcelExport with {excelExportModel?.EntityIds?.Count} entities");
                var onStartMemoryUsed = this.LogMemoryInMB("On start memory used {MemoryUsed} MB.", proc.PrivateMemorySize64);

                await this.ExcelExportInternalAsync(environmentContextData, longRunningJobId, username, excelExportModel);

                proc.Refresh();
                var onEndMemoryUsed = this.LogMemoryInMB("On finish memory used {MemoryUsed} MB.", proc.PrivateMemorySize64);
                _ = this.LogMemoryInMB("Memory delta {MemoryDelta} MB", onEndMemoryUsed - onStartMemoryUsed);
            }
        }

        private async Task ExcelExportInternalAsync(EnvironmentContextData environmentContextData, int longRunningJobId, string username, ExcelExportModel excelExportModel)
        {
            var requestContext = this.GetRequestContextForEnvironment(environmentContextData);
            requestContext.DataLanguage = new CultureInfo(new UserRepository(requestContext).GetAllUserSettings(username).GetValueOrDefault("DataLanguage", "en"));

            try
            {
                this.SetDimensions(requestContext.CustomerSafeName, requestContext.EnvironmentSafeName, longRunningJobId);
                this.CreateJobRepository(environmentContextData, username);

                PopulateRequestContextForUserAccess(requestContext, username);

                var excelExportRepository = new ExcelExportRepository(requestContext);
                var entityType = excelExportRepository.GetExportMainEntityType(excelExportModel.EntityTypeModels);
                var fileName = $"{username}_{entityType.Id}_{DateTime.UtcNow:yyyy_MM_dd_HH_mm_ss}.xlsx";
                excelExportModel.Name = fileName;
                var entityTypeId = entityType.Id;

                var metadata = JsonConvert.SerializeObject(new { entityType = entityTypeId });
                this.jobActorHelper.UpdateLongRunningJobStateAndMetadata(this.Id.GetStringId(), longRunningJobId, LongRunningJobsStatus.Running, metadata);
                this.tokenSource.Token.ThrowIfCancellationRequested();

                Log.Information($"Executing export for {excelExportModel.EntityIds.Count} entities and {excelExportModel.EntityTypeModels.Count} types.");

                var isContentSegmentationEnabled = GetContentSegmentation(requestContext);
                var excelExportBatchSize = GetExcelExportBatchSize(requestContext);

                if (!isContentSegmentationEnabled)
                {
                    excelExportModel.SegmentationOption = inRiver.iPMC.Persistance.ContentSegmentationEnum.None;
                }
                else if (isContentSegmentationEnabled && excelExportModel.SegmentationOption == inRiver.iPMC.Persistance.ContentSegmentationEnum.None)
                {
                    excelExportModel.SegmentationOption = inRiver.iPMC.Persistance.ContentSegmentationEnum.ID;
                }

                var exportHasErrors = await excelExportRepository.BatchExportExcelAsync(excelExportModel, excelExportBatchSize, this.tokenSource.Token, this.UpdateJobProgressAsync);

                var jobStatus = exportHasErrors ? LongRunningJobsStatus.FinishedWithErrors : LongRunningJobsStatus.Finished;
                metadata = JsonConvert.SerializeObject(new { entityType = entityTypeId, fileName });

                this.FullLogInformation(requestContext, "Excel export finished");
                this.methodState = ActorMethodState.Success;
                this.jobActorHelper.UpdateLongRunningJobStateAndMetadata(this.Id.GetStringId(), longRunningJobId, jobStatus, metadata);
            }
            catch (OperationCanceledException)
            {
                this.LogJobIsCancelled(requestContext, longRunningJobId, LongRunningJobsJobType.ExcelExport);
                this.jobActorHelper?.UpdateLongRunningJobState(this.Id.GetStringId(), longRunningJobId, LongRunningJobsStatus.Cancelled);
            }
            catch (Exception e)
            {
                Log.Error(e, "Error occurred in ExcelExport for Customer: {customerSafeName}/{environmentSafeName}.", environmentContextData?.CustomerSafeName, environmentContextData?.EnvironmentSafeName);
                this.jobActorHelper?.UpdateLongRunningJobState(this.Id.GetStringId(), longRunningJobId, LongRunningJobsStatus.Error);
            }
        }

        public async Task ExcelImport(EnvironmentContextData environmentContextData, int longRunningJobId, string username, ExcelImportModel excelImportModel)
        {
            using (LogContext.PushProperty(Dimension.JobId, longRunningJobId))
            using (LogContext.PushProperty(Dimension.Method, nameof(ExcelImport)))
            using (LogContext.PushProperty(Dimension.Username, username))
            using (LogContext.PushProperty(Dimension.Environment, $"{environmentContextData?.CustomerSafeName}/{environmentContextData?.EnvironmentSafeName}"))
            using (var proc = Process.GetCurrentProcess())
            {
                Log.Information($"Starting ExcelImport");
                var onStartMemoryUsed = this.LogMemoryInMB("On start memory used {MemoryUsed} MB.", proc.PrivateMemorySize64);

                await this.ExcelImportInternal(environmentContextData, longRunningJobId, username, excelImportModel);

                proc.Refresh();
                var onEndMemoryUsed = this.LogMemoryInMB("On finish memory used {MemoryUsed} MB.", proc.PrivateMemorySize64);
                _ = this.LogMemoryInMB("Memory delta {MemoryDelta} MB", onEndMemoryUsed - onStartMemoryUsed);
            }
        }

        private async Task ExcelImportInternal(EnvironmentContextData environmentContextData, int longRunningJobId, string username, ExcelImportModel excelImportModel)
        {
            var requestContext = this.GetRequestContextForEnvironment(environmentContextData);

            try
            {
                this.SetDimensions(requestContext.CustomerSafeName, requestContext.EnvironmentSafeName, longRunningJobId);
                this.CreateJobRepository(environmentContextData, username);

                this.FullLogInformation(requestContext, $"Excel import batch {excelImportModel.BatchId} started by {username}");

                var metadata = JsonConvert.SerializeObject(excelImportModel);
                var configurationJsonData = requestContext.DataPersistance.GetExcelModel(username, excelImportModel.BatchId, true);
                var configurationData = JsonConvert.DeserializeObject<FileImportConfigurationModel>(configurationJsonData);

                this.FullLogInformation(requestContext, $"Excel import batch {excelImportModel.BatchId} config: " +
                            $"RunValidation-{configurationData.RunValidation}, RunServerExtensions:{configurationData.RunServerExtensions && excelImportModel.RunImport}, " +
                            $"RunImport-{configurationData.RunImport && excelImportModel.RunImport}, RunPostProcessing-{configurationData.RunPostProcessing && excelImportModel.RunImport}, " +
                            $"RunEntityListeners-{configurationData.RunEntityListeners && excelImportModel.RunImport}" +
                            $"RunChannelListeners-{configurationData.RunChannelListeners && excelImportModel.RunImport}");

                this.jobActorHelper.UpdateLongRunningJobState(this.Id.GetStringId(), longRunningJobId, LongRunningJobsStatus.Running);
                this.tokenSource.Token.ThrowIfCancellationRequested();

                var excelImportRepository = new ExcelImportRepository(requestContext);
                var environmentSettingsRepository = RepositoryFactory.GetEnvironmentSettingsRepository("LongRunningJob", requestContext.Username, requestContext);
                var importQueueSizeSetting = environmentSettingsRepository.GetEnvironmentSetting("ExcelImportJobQueueSize", requestContext.EnvironmentId);

                var queueSize = 100;

                if (importQueueSizeSetting != null)
                {
                    _ = int.TryParse(importQueueSizeSetting.Value, out queueSize);
                }

                var localQueue = new BlockingCollection<ImportedEntity>(queueSize);

                this.tokenSource.Token.ThrowIfCancellationRequested();

                var anyErrors = Task.Run(() => excelImportRepository.ProcessImport(
                    ref localQueue,
                    username,
                    excelImportModel.BatchId,
                    this.tokenSource.Token,
                    this.UpdateJobProgressAsync
                    ),
                    this.tokenSource.Token);

                if (configurationData.RunImport && (configurationData.RunEntityListeners || configurationData.RunChannelListeners || configurationData.RunPostProcessing))
                {
                    await Task.Run(
                        () => excelImportRepository.PostProcessImport(ref localQueue, username, excelImportModel.BatchId),
                        this.tokenSource.Token);
                }

                var sysIds = requestContext.DataPersistance.GetSysIdsForImportJob(longRunningJobId.ToString());
                this.FullLogInformation(requestContext, $"Excel import batch {excelImportModel.BatchId} started by {username} finished");

                this.FullLogInformation(
                    requestContext,
                    $"Excel import batch {excelImportModel.BatchId} imported the following entities without errors: " +
                    string.Join(",", sysIds));
                this.methodState = ActorMethodState.Success;
                this.jobActorHelper.UpdateLongRunningJobStateAndMetadata(
                    this.Id.GetStringId(),
                    longRunningJobId,
                    await anyErrors ? LongRunningJobsStatus.FinishedWithErrors : LongRunningJobsStatus.Finished,
                    metadata);
            }
            catch (OperationCanceledException)
            {
                this.LogJobIsCancelled(requestContext, longRunningJobId, LongRunningJobsJobType.ExcelImport);
                this.jobActorHelper?.UpdateLongRunningJobStateAndMetadata(this.Id.GetStringId(), longRunningJobId, LongRunningJobsStatus.Cancelled, null);
                return;
            }
            catch (Exception e)
            {
                Log.Error(e, "Error occurred in ExcelImport for Customer: {customerSafeName}/{environmentSafeName}.", environmentContextData?.CustomerSafeName, environmentContextData?.EnvironmentSafeName);
                this.jobActorHelper.UpdateLongRunningJobStateAndMetadata(this.Id.GetStringId(), longRunningJobId, LongRunningJobsStatus.Error, null);
            }
        }

        public Task ExcelExportHistory(EnvironmentContextData environmentContextData, int longRunningJobId, string username, ExcelExportHistoryModel excelExportHistoryModel)
        {
            using (LogContext.PushProperty(Dimension.JobId, longRunningJobId))
            using (LogContext.PushProperty(Dimension.Method, nameof(ExcelExportHistory)))
            using (LogContext.PushProperty(Dimension.Environment, $"{environmentContextData?.CustomerSafeName}/{environmentContextData?.EnvironmentSafeName}"))
            using (var proc = Process.GetCurrentProcess())
            {
                Log.Information("Starting ExcelExportHistory");
                var onStartMemoryUsed = this.LogMemoryInMB("On start memory used {MemoryUsed} MB.", proc.PrivateMemorySize64);

                this.ExcelExportHistoryInternal(environmentContextData, longRunningJobId, username, excelExportHistoryModel);

                proc.Refresh();
                var onEndMemoryUsed = this.LogMemoryInMB("On finish memory used {MemoryUsed} MB.", proc.PrivateMemorySize64);
                _ = this.LogMemoryInMB("Memory delta {MemoryDelta} MB", onEndMemoryUsed - onStartMemoryUsed);
            }

            return Task.FromResult(true);
        }

        private void ExcelExportHistoryInternal(EnvironmentContextData environmentContextData, int longRunningJobId, string username, ExcelExportHistoryModel excelExportHistoryModel)
        {
            var requestContext = this.GetRequestContextForEnvironment(environmentContextData);

            try
            {
                this.SetDimensions(requestContext.CustomerSafeName, requestContext.EnvironmentSafeName, longRunningJobId);
                this.CreateJobRepository(environmentContextData, username);

                requestContext.Log((inRiver.Remoting.Log.LogLevel)LogLevel.Information, "Excel export history started");
                Log.Information("Excel export history started for Customer: {customerSafeName}/{environmentSafeName}.", environmentContextData?.CustomerSafeName, environmentContextData?.EnvironmentSafeName);

                this.jobActorHelper.UpdateLongRunningJobState(this.Id.GetStringId(), longRunningJobId, LongRunningJobsStatus.Running);
                this.tokenSource.Token.ThrowIfCancellationRequested();

                var fileName = $"{username}_Onboarding_History_{DateTime.UtcNow:yyyy_MM_dd_HH_mm_ss}.xlsx";
                var excelExportRepository = new ExcelExportRepository(requestContext);
                excelExportRepository.ExcelExportHistory(excelExportHistoryModel, fileName, this.tokenSource.Token);

                requestContext.Log((inRiver.Remoting.Log.LogLevel)LogLevel.Information, "Excel export history finished");
                Log.Information("Excel export history finished for Customer: {customerSafeName}/{environmentSafeName}.", environmentContextData?.CustomerSafeName, environmentContextData?.EnvironmentSafeName);
                this.methodState = ActorMethodState.Success;
                var metadata = JsonConvert.SerializeObject(new { fileName });
                this.jobActorHelper.UpdateLongRunningJobStateAndMetadata(this.Id.GetStringId(), longRunningJobId, LongRunningJobsStatus.Finished, metadata);
            }
            catch (OperationCanceledException)
            {
                this.LogJobIsCancelled(requestContext, longRunningJobId, LongRunningJobsJobType.ExcelExportHistory);
                this.jobActorHelper?.UpdateLongRunningJobStateAndMetadata(this.Id.GetStringId(), longRunningJobId, LongRunningJobsStatus.Cancelled, null);
                return;
            }
            catch (Exception e)
            {
                Log.Error(e, "Error occurred in ExcelExportHistory for Customer: {customerSafeName}/{environmentSafeName}.", environmentContextData?.CustomerSafeName, environmentContextData?.EnvironmentSafeName);
                this.jobActorHelper?.UpdateLongRunningJobStateAndMetadata(this.Id.GetStringId(), longRunningJobId, LongRunningJobsStatus.Error, null);
            }
        }

        public async Task RunSyndicateAsync(EnvironmentContextData environmentContextData, int longRunningJobId, SyndicationModel syndicationModel, string username)
        {
            using (LogContext.PushProperty(Dimension.JobId, longRunningJobId))
            using (LogContext.PushProperty(Dimension.Method, nameof(this.RunSyndicateAsync)))
            using (LogContext.PushProperty(Dimension.Environment, $"{environmentContextData?.CustomerSafeName}/{environmentContextData?.EnvironmentSafeName}"))
            using (var proc = Process.GetCurrentProcess())
            {
                this.telemetryClient.TrackEvent(EventName.SyndicationStarted, new Dictionary<string, string> {
                    { Dimension.JobId,  longRunningJobId.ToString() },
                    { Dimension.Method,  nameof(this.RunSyndicateAsync) },
                    { Dimension.Environment,  $"{environmentContextData?.CustomerSafeName}/{environmentContextData?.EnvironmentSafeName}" },
                    { Dimension.ExtensionId, syndicationModel.ExtensionId }
                });
                var onStartMemoryUsed = this.LogMemoryInMB("On start memory used {MemoryUsed} MB.", proc.PrivateMemorySize64);

                var actorId = this.Id.GetStringId();
                this.SetDimensions(environmentContextData?.CustomerSafeName, environmentContextData?.EnvironmentSafeName, longRunningJobId);
                await this.syndicationService.RunSyndicateInternalAsync(environmentContextData, longRunningJobId, syndicationModel, actorId, Util.GetStackGroup(), username);
                this.methodState = this.syndicationJobResultService.GetActorMethodState();

                proc.Refresh();
                var onEndMemoryUsed = this.LogMemoryInMB("On finish memory used {MemoryUsed} MB.", proc.PrivateMemorySize64);
                _ = this.LogMemoryInMB("Memory delta {MemoryDelta} MB", onEndMemoryUsed - onStartMemoryUsed);
                this.telemetryClient.TrackEvent(EventName.SyndicationEnded, new Dictionary<string, string>(this.syndicationJobResultService.GetResultMetrics(this.stopWatch)) {
                    { Dimension.JobId,  longRunningJobId.ToString() },
                    { Dimension.Method,  nameof(this.RunSyndicateAsync) },
                    { Dimension.Environment,  $"{environmentContextData?.CustomerSafeName}/{environmentContextData?.EnvironmentSafeName}" },
                    { Dimension.ExtensionId, syndicationModel.ExtensionId },
                });
            }
        }

        public async Task RunReviewAsync(EnvironmentContextData environmentContextData, SyndicationModel syndicationModel, Guid reviewId)
        {
            using (LogContext.PushProperty(Dimension.ReviewId, reviewId))
            using (LogContext.PushProperty(Dimension.Method, nameof(this.RunReviewAsync)))
            using (LogContext.PushProperty(Dimension.Environment, $"{environmentContextData?.CustomerSafeName}/{environmentContextData?.EnvironmentSafeName}"))
            {
                this.telemetryClient.TrackEvent(EventName.ReviewStarted, new Dictionary<string, string> {
                    { Dimension.ReviewId,  reviewId.ToString() },
                    { Dimension.Method,  nameof(this.RunReviewAsync) },
                    { Dimension.Environment,  $"{environmentContextData?.CustomerSafeName}/{environmentContextData?.EnvironmentSafeName}" }
                });

                await this.syndicationService.RunReviewInternalAsync(environmentContextData, syndicationModel, Util.GetStackGroup(), reviewId);

                this.telemetryClient.TrackEvent(EventName.ReviewEnded, new Dictionary<string, string>(this.syndicationJobResultService.GetResultMetrics(this.stopWatch))
                {
                    { Dimension.ReviewId,  reviewId.ToString() },
                    { Dimension.Method,  nameof(this.RunReviewAsync) },
                    { Dimension.Environment,  $"{environmentContextData?.CustomerSafeName}/{environmentContextData?.EnvironmentSafeName}" },
                });
            }
        }

        private bool CheckIfReviewJob(int currentLongRunningJobId, string methodName) => currentLongRunningJobId == 0 && methodName == nameof(this.RunReviewAsync);

        private int GetExcelExportBatchSize(RequestContext requestContext)
        {
            var environmentSettingsRepository = RepositoryFactory.GetEnvironmentSettingsRepository("LongRunningJob", requestContext.Username, requestContext);
            var excelExportBatchSizeSetting = environmentSettingsRepository.GetEnvironmentSetting("EXCEL_EXPORT_BATCH_SIZE", requestContext.EnvironmentId);

            if (excelExportBatchSizeSetting != null && int.TryParse(excelExportBatchSizeSetting.Value, out var excelExportBatchSize))
            {
                return excelExportBatchSize;
            }

            return ExcelExportRepository.DefaultExcelExportBatchSize;
        }

        private bool GetContentSegmentation(RequestContext requestContext)
        {
            var environmentSettingsRepository = RepositoryFactory.GetEnvironmentSettingsRepository("LongRunningJob", requestContext.Username, requestContext);
            var contentSegmentationSetting = environmentSettingsRepository.GetEnvironmentSetting("CONTENT_SEGMENTATION_ENABLED", requestContext.EnvironmentId);

            if (contentSegmentationSetting != null && contentSegmentationSetting.EnvironmentId == requestContext.EnvironmentId && bool.TryParse(contentSegmentationSetting.Value, out var contentSegmentation))
            {
                return contentSegmentation;
            }

            return false;
        }

        #region ContentStore

        public Task ContentStoreMassExcludeTypesAsync(EnvironmentContextData environmentContextData, int longRunningJobId, MassExcludeTypeModel massExcludeTypeModel)
        {
            using (LogContext.PushProperty(Dimension.JobId, longRunningJobId))
            using (LogContext.PushProperty(Dimension.Method, nameof(this.ContentStoreMassExcludeTypesAsync)))
            using (LogContext.PushProperty(Dimension.Environment, $"{environmentContextData?.CustomerSafeName}/{environmentContextData?.EnvironmentSafeName}"))
            using (var proc = Process.GetCurrentProcess())
            {

                switch (massExcludeTypeModel.ExcludeType)
                {
                    case ExcludeType.FieldType:
                        Log.Information($"Starting content store mass exclude update for {massExcludeTypeModel.ExcludeFieldTypeModel.FieldTypeId} field type in {massExcludeTypeModel.ExcludeFieldTypeModel.EntityTypeId}");
                        break;

                    case ExcludeType.EntityType:
                        Log.Information($"Starting content store mass exclude update for {massExcludeTypeModel.EntityTypeId} entity type");
                        break;

                    case ExcludeType.LinkType:
                        Log.Information($"Starting content store mass exclude update for {massExcludeTypeModel.LinkTypeId} link type");
                        break;

                    default:
                        return Task.FromResult(true);
                }

                var onStartMemoryUsed = this.LogMemoryInMB("On start memory used {MemoryUsed} MB.", proc.PrivateMemorySize64);
                this.ContentStoreMassExcludeTypesInternal(environmentContextData, longRunningJobId, massExcludeTypeModel);
                proc.Refresh();
                var onEndMemoryUsed = this.LogMemoryInMB("On finish memory used {MemoryUsed} MB.", proc.PrivateMemorySize64);
                _ = this.LogMemoryInMB("Memory delta {MemoryDelta} MB", onEndMemoryUsed - onStartMemoryUsed);
            }

            return Task.FromResult(true);
        }

        private void ContentStoreMassExcludeTypesInternal(EnvironmentContextData environmentContextData, int longRunningJobId, MassExcludeTypeModel massExcludeTypeModel)
        {
            var requestContext = this.GetRequestContextForEnvironment(environmentContextData);
            const string logInfoFinished = "ContentStore mass exclude types update has been finished";

            try
            {
                this.SetDimensions(requestContext.CustomerSafeName, requestContext.EnvironmentSafeName, longRunningJobId);
                this.CreateJobRepository(environmentContextData, requestContext.Username);

                // Set status to Running
                this.jobActorHelper.UpdateLongRunningJobState(this.Id.GetStringId(), longRunningJobId, LongRunningJobsStatus.Running);
                this.tokenSource.Token.ThrowIfCancellationRequested();

                // Start mass update for content store exclude types
                var result = this.DoContentStoreMassExcludeTypes(requestContext, massExcludeTypeModel);

                // Set status to Finished and store result as metadata
                requestContext.Log((inRiver.Remoting.Log.LogLevel)LogLevel.Information, logInfoFinished);
                Log.Information("ContentStore mass exclude types update for Customer: {customerSafeName}/{environmentSafeName}.", environmentContextData?.CustomerSafeName, environmentContextData?.EnvironmentSafeName);
                this.methodState = ActorMethodState.Success;
                var metadata = JsonConvert.SerializeObject(result);
                this.jobActorHelper.UpdateLongRunningJobStateAndMetadata(this.Id.GetStringId(), longRunningJobId, LongRunningJobsStatus.Finished, metadata);
            }
            catch (OperationCanceledException)
            {
                this.LogJobIsCancelled(requestContext, longRunningJobId, LongRunningJobsJobType.MassUpdate);
                this.jobActorHelper?.UpdateLongRunningJobStateAndMetadata(this.Id.GetStringId(), longRunningJobId, LongRunningJobsStatus.Cancelled, null);
                return;
            }
            catch (Exception e)
            {
                Log.Error(e, "Error occurred in MassUpdate for Customer: {customerSafeName}/{environmentSafeName}.", environmentContextData?.CustomerSafeName, environmentContextData?.EnvironmentSafeName);
                this.jobActorHelper?.UpdateLongRunningJobStateAndMetadata(this.Id.GetStringId(), longRunningJobId, LongRunningJobsStatus.Error, null);
            }
        }

        private object DoContentStoreMassExcludeTypes(RequestContext context, MassExcludeTypeModel massExcludeTypeModel)
        {
            const string logInfoFieldTypes = "Mass update for exclude field types has started";
            const string logInfoEntityTypes = "Mass update for exclude entity types has started";
            const string logInfoLinkTypes = "Mass update for exclude link types has started";

            switch (massExcludeTypeModel.ExcludeType)
            {
                case ExcludeType.FieldType:
                    this.FullLogInformation(context, logInfoFieldTypes);
                    this.DoContentStoreMassExcludeFieldTypes(context, massExcludeTypeModel.ExcludeFieldTypeModel);
                    return new { excludeFieldTypeModel = massExcludeTypeModel.ExcludeFieldTypeModel };

                case ExcludeType.EntityType:
                    this.FullLogInformation(context, logInfoEntityTypes);
                    this.DoContentStoreMassExcludeEntityTypes(context, massExcludeTypeModel.EntityTypeId);
                    return new { entityTypeId = massExcludeTypeModel.EntityTypeId };

                case ExcludeType.LinkType:
                    this.FullLogInformation(context, logInfoLinkTypes);
                    this.DoContentStoreMassExcludeLinkTypes(context, massExcludeTypeModel.LinkTypeId);
                    return new { linkTypeId = massExcludeTypeModel.LinkTypeId };

                default:
                    return null;
            }
        }

        private void DoContentStoreMassExcludeFieldTypes(RequestContext context, MassExcludeFieldTypeModel excludeFieldType)
        {
            var stores = context.DataPersistance.GetAllExcludeFieldTypes();

            foreach (var store in stores)
            {
                try
                {
                    var fieldTypes = string.IsNullOrEmpty(store.Value) ?
                        new Dictionary<string, List<string>>() :
                        JsonConvert.DeserializeObject<Dictionary<string, List<string>>>(store.Value);

                    if (fieldTypes.ContainsKey(excludeFieldType.EntityTypeId) && !fieldTypes[excludeFieldType.EntityTypeId].Contains(excludeFieldType.FieldTypeId))
                    {
                        fieldTypes[excludeFieldType.EntityTypeId].Add(excludeFieldType.FieldTypeId);
                    }

                    if (!fieldTypes.ContainsKey(excludeFieldType.EntityTypeId))
                    {
                        fieldTypes.Add(excludeFieldType.EntityTypeId, new List<string>
                        {
                            excludeFieldType.FieldTypeId
                        });
                    }

                    var newExcludedFieldTypes = JsonConvert.SerializeObject(fieldTypes);
                    context.DataPersistance.ExcludeFieldTypes(store.Key, newExcludedFieldTypes);
                }
                catch (Exception e)
                {
                    Log.Error(e, "Error occurred in DoContentStoreMassExcludeFieldTypes for Store: {store}", store);
                }
            }
        }

        private void DoContentStoreMassExcludeEntityTypes(RequestContext context, string entityTypeId)
        {
            var stores = context.DataPersistance.GetAllExcludeEntityTypes();

            foreach (var store in stores)
            {
                try
                {
                    var entityTypes = string.IsNullOrEmpty(store.Value) ?
                        new List<string>() :
                        System.Text.Json.JsonSerializer.Deserialize<List<string>>(store.Value);

                    if (!entityTypes.Contains(entityTypeId))
                    {
                        entityTypes.Add(entityTypeId);
                    }

                    var newExcludedEntityTypes = JsonConvert.SerializeObject(entityTypes);
                    context.DataPersistance.ExcludeEntityTypes(store.Key, newExcludedEntityTypes);
                }
                catch (Exception e)
                {
                    Log.Error(e, "Error occurred in DoContentStoreMassExcludeEntityTypes for Store: {store}", store);
                }
            }
        }

        private void DoContentStoreMassExcludeLinkTypes(RequestContext context, string linkTypeId)
        {
            var stores = context.DataPersistance.GetAllExcludeLinkTypes();

            foreach (var store in stores)
            {
                try
                {
                    var linkTypes = string.IsNullOrEmpty(store.Value) ?
                        new List<string>() :
                        System.Text.Json.JsonSerializer.Deserialize<List<string>>(store.Value);

                    if (!linkTypes.Contains(linkTypeId))
                    {
                        linkTypes.Add(linkTypeId);
                    }

                    var newExcludedLinkTypes = JsonConvert.SerializeObject(linkTypes);
                    context.DataPersistance.ExcludeLinkTypes(store.Key, newExcludedLinkTypes);
                }
                catch (Exception e)
                {
                    Log.Error(e, "Error occurred in DoContentStoreMassExcludeLinkTypes for Store: {store}", store);
                }
            }
        }

        #endregion

        public Task MassUpdate(EnvironmentContextData environmentContextData, string username, int longRunningJobId, MassUpdateModel massUpdateModel)
        {
            using (LogContext.PushProperty(Dimension.JobId, longRunningJobId))
            using (LogContext.PushProperty(Dimension.Method, nameof(MassUpdate)))
            using (LogContext.PushProperty(Dimension.Environment, $"{environmentContextData?.CustomerSafeName}/{environmentContextData?.EnvironmentSafeName}"))
            using (var proc = Process.GetCurrentProcess())
            {
                Log.Information($"Starting mass update for {massUpdateModel?.EntityIds?.Count} entities");
                var onStartMemoryUsed = this.LogMemoryInMB("On start memory used {MemoryUsed} MB.", proc.PrivateMemorySize64);

                this.MassUpdateInternal(environmentContextData, username, longRunningJobId, massUpdateModel);

                proc.Refresh();
                var onEndMemoryUsed = this.LogMemoryInMB("On finish memory used {MemoryUsed} MB.", proc.PrivateMemorySize64);
                _ = this.LogMemoryInMB("Memory delta {MemoryDelta} MB", onEndMemoryUsed - onStartMemoryUsed);
            }

            return Task.FromResult(true);
        }

        public Task DeleteAllLinksForLinkType(EnvironmentContextData environmentContextData, string username, int longRunningJobId, DeleteAllLinksForLinkTypeModel deleteAllLinksForLinkTypeModel)
        {
            using (LogContext.PushProperty(Dimension.JobId, longRunningJobId))
            using (LogContext.PushProperty(Dimension.Method, nameof(this.DeleteAllLinksForLinkType)))
            using (LogContext.PushProperty(Dimension.Environment, $"{environmentContextData?.CustomerSafeName}/{environmentContextData?.EnvironmentSafeName}"))
            using (var proc = Process.GetCurrentProcess())
            {
                Log.Information($"Starting deletion for all links with LinkType: {deleteAllLinksForLinkTypeModel.LinkTypeId} and EntityId: {deleteAllLinksForLinkTypeModel.EntityId}");
                var onStartMemoryUsed = this.LogMemoryInMB("On start memory used {MemoryUsed} MB.", proc.PrivateMemorySize64);

                this.DeleteAllLinksForLinkTypeInternal(environmentContextData, username, longRunningJobId, deleteAllLinksForLinkTypeModel);

                proc.Refresh();
                var onEndMemoryUsed = this.LogMemoryInMB("On finish memory used {MemoryUsed} MB.", proc.PrivateMemorySize64);
                _ = this.LogMemoryInMB("Memory delta {MemoryDelta} MB", onEndMemoryUsed - onStartMemoryUsed);
            }

            return Task.FromResult(true);
        }

        public Task ApplyExpressions(EnvironmentContextData environmentContextData, string username, int longRunningJobId, ApplyExpressionsModel applyExpressionsModel)
        {
            using (LogContext.PushProperty(Dimension.JobId, longRunningJobId))
            using (LogContext.PushProperty(Dimension.Method, nameof(ApplyExpressions)))
            using (LogContext.PushProperty(Dimension.Environment, $"{environmentContextData?.CustomerSafeName}/{environmentContextData?.EnvironmentSafeName}"))
            using (var proc = Process.GetCurrentProcess())
            {
                var target = string.IsNullOrEmpty(applyExpressionsModel.FieldTypeId) ?
                    string.IsNullOrEmpty(applyExpressionsModel.FieldSetId) ?
                        string.Empty
                        : $"FieldSetId: {applyExpressionsModel.FieldSetId}"
                    : $"FieldTypeId: {applyExpressionsModel.FieldTypeId}";

                Log.Information($"Starting ApplyExpressions for {target}");
                var onStartMemoryUsed = this.LogMemoryInMB("On start memory used {MemoryUsed} MB.", proc.PrivateMemorySize64);

                this.ApplyExpressionsInternal(environmentContextData, username, longRunningJobId, applyExpressionsModel);

                proc.Refresh();
                var onEndMemoryUsed = this.LogMemoryInMB("On finish memory used {MemoryUsed} MB.", proc.PrivateMemorySize64);
                _ = this.LogMemoryInMB("Memory delta {MemoryDelta} MB", onEndMemoryUsed - onStartMemoryUsed);
            }

            return Task.FromResult(true);
        }

        public async Task WorkflowInspireAsync(EnvironmentContextData environmentContextData, string username, int longRunningJobId, WorkflowInspireModel model)
        {
            using (LogContext.PushProperty(Dimension.JobId, longRunningJobId))
            using (LogContext.PushProperty(Dimension.Method, nameof(WorkflowInspireAsync)))
            using (LogContext.PushProperty(Dimension.Environment, $"{environmentContextData?.CustomerSafeName}/{environmentContextData?.EnvironmentSafeName}"))
            using (var proc = Process.GetCurrentProcess())
            {
                Log.Information($"Starting WorkflowInspire");
                var onStartMemoryUsed = this.LogMemoryInMB("On start memory used {MemoryUsed} MB.", proc.PrivateMemorySize64);

                await this.WorkflowInspireInternalAsync(environmentContextData, username, longRunningJobId, model);

                proc.Refresh();
                var onEndMemoryUsed = this.LogMemoryInMB("On finish memory used {MemoryUsed} MB.", proc.PrivateMemorySize64);
                _ = this.LogMemoryInMB("Memory delta {MemoryDelta} MB", onEndMemoryUsed - onStartMemoryUsed);
            }
        }

        private void MassUpdateInternal(EnvironmentContextData environmentContextData, string username, int longRunningJobId, MassUpdateModel massUpdateModel)
        {
            var requestContext = this.GetRequestContextForEnvironment(environmentContextData);

            try
            {
                this.SetDimensions(requestContext.CustomerSafeName, requestContext.EnvironmentSafeName, longRunningJobId);
                this.CreateJobRepository(environmentContextData, username);

                // Set status to Running
                this.jobActorHelper.UpdateLongRunningJobState(this.Id.GetStringId(), longRunningJobId, LongRunningJobsStatus.Running);
                this.tokenSource.Token.ThrowIfCancellationRequested();

                this.FullLogInformation(requestContext, "Mass update started");

                var user = requestContext.DataPersistance.GetUserByUsername(username);
                var dictionary = new Dictionary<int, List<string>>();
                user.GetSegments().ForEach(s => dictionary.Add(s.Id, user.GetPermissionsForSegmentId(s.Id).Select(p => p.Name).ToList()));
                requestContext.ContentSegmentIdToPermissions = dictionary;
                requestContext.Username = username;
                requestContext.Roles = user.Roles.Select(r => r.Name).ToList();
                requestContext.Permissions = user.Permissions.Select(p => p.Name).ToList();

                // Do the mass update
                ChildCVLFieldsResetHelper.ResetCacheValues();
                var result = this.DoMassUpdate(requestContext, massUpdateModel, user, this.tokenSource.Token);

                // Set status to Finished and store result as metadata
                requestContext.Log((inRiver.Remoting.Log.LogLevel)LogLevel.Information, "Mass update finished");
                Log.Information("Mass update finished for Customer: {customerSafeName}/{environmentSafeName}.", environmentContextData?.CustomerSafeName, environmentContextData?.EnvironmentSafeName);
                this.methodState = ActorMethodState.Success;
                var metadata = JsonConvert.SerializeObject(result);
                this.jobActorHelper.UpdateLongRunningJobStateAndMetadata(this.Id.GetStringId(), longRunningJobId, LongRunningJobsStatus.Finished, metadata);
            }
            catch (OperationCanceledException)
            {
                this.LogJobIsCancelled(requestContext, longRunningJobId, LongRunningJobsJobType.MassUpdate);
                this.jobActorHelper?.UpdateLongRunningJobStateAndMetadata(this.Id.GetStringId(), longRunningJobId, LongRunningJobsStatus.Cancelled, null);
                return;
            }
            catch (Exception e)
            {
                Log.Error(e, "Error occurred in MassUpdate for Customer: {customerSafeName}/{environmentSafeName}.", environmentContextData?.CustomerSafeName, environmentContextData?.EnvironmentSafeName);
                this.jobActorHelper?.UpdateLongRunningJobStateAndMetadata(this.Id.GetStringId(), longRunningJobId, LongRunningJobsStatus.Error, null);
            }
        }

        private void DeleteAllLinksForLinkTypeInternal(EnvironmentContextData environmentContextData, string username, int longRunningJobId, DeleteAllLinksForLinkTypeModel deleteAllLinksForLinkTypeModel)
        {
            var requestContext = this.GetRequestContextForEnvironment(environmentContextData);

            try
            {
                this.SetDimensions(requestContext.CustomerSafeName, requestContext.EnvironmentSafeName, longRunningJobId);
                this.CreateJobRepository(environmentContextData, username);

                var metadata = JsonConvert.SerializeObject(new { linkTypeId = deleteAllLinksForLinkTypeModel.LinkTypeId });
                this.jobActorHelper.UpdateLongRunningJobStateAndMetadata(this.Id.GetStringId(), longRunningJobId, LongRunningJobsStatus.Running, metadata);
                this.tokenSource.Token.ThrowIfCancellationRequested();

                this.FullLogInformation(requestContext, "Delete all links for link type started");

                var dataRepository = new DataRepository(requestContext);
                dataRepository.DeleteAllLinksForLinkType(deleteAllLinksForLinkTypeModel.EntityId, deleteAllLinksForLinkTypeModel.LinkTypeId, deleteAllLinksForLinkTypeModel.Direction, this.UpdateJobProgressAsync);

                this.FullLogInformation(requestContext, "Delete all links for link type finished.");
                this.methodState = ActorMethodState.Success;
                this.jobActorHelper.UpdateLongRunningJobState(this.Id.GetStringId(), longRunningJobId, LongRunningJobsStatus.Finished);
            }
            catch (OperationCanceledException)
            {
                this.LogJobIsCancelled(requestContext, longRunningJobId, LongRunningJobsJobType.MassUpdate);
                this.jobActorHelper?.UpdateLongRunningJobState(this.Id.GetStringId(), longRunningJobId, LongRunningJobsStatus.Cancelled);
            }
            catch (Exception e)
            {
                Log.Error(e, $"Error occurred in delete all links for link type for Customer: {environmentContextData?.CustomerSafeName}/{environmentContextData?.EnvironmentSafeName}.");
                this.jobActorHelper?.UpdateLongRunningJobState(this.Id.GetStringId(), longRunningJobId, LongRunningJobsStatus.Error);
            }
        }

        private void ApplyExpressionsInternal(EnvironmentContextData environmentContextData, string username, int longRunningJobId, ApplyExpressionsModel applyExpressionsModel)
        {
            var requestContext = this.GetRequestContextForEnvironment(environmentContextData);

            try
            {
                this.SetDimensions(requestContext.CustomerSafeName, requestContext.EnvironmentSafeName, longRunningJobId);
                this.CreateJobRepository(environmentContextData, username);

                // Set status to Running
                this.jobActorHelper.UpdateLongRunningJobState(this.Id.GetStringId(), longRunningJobId, LongRunningJobsStatus.Running);
                this.tokenSource.Token.ThrowIfCancellationRequested();

                var user = requestContext.DataPersistance.GetUserByUsername(username);
                if (user == null)
                {
                    this.FullLogInformation(requestContext, $"ApplyExpressions failed to start because the user {username} is not a user in this iPMC environment");
                    throw new ArgumentException($"Invalid user: {username}");
                }

                this.FullLogInformation(requestContext, "ApplyExpressions started");

                var dictionary = new Dictionary<int, List<string>>();
                user.GetSegments().ForEach(s => dictionary.Add(s.Id, user.GetPermissionsForSegmentId(s.Id).Select(p => p.Name).ToList()));
                requestContext.ContentSegmentIdToPermissions = dictionary;
                requestContext.Username = username;
                requestContext.Roles = user.Roles.Select(r => r.Name).ToList();
                requestContext.Permissions = user.Permissions.Select(p => p.Name).ToList();

                var result = this.DoApplyExpressions(requestContext, applyExpressionsModel, this.tokenSource.Token);

                // Set status to Finished and store result as metadata
                requestContext.Log((inRiver.Remoting.Log.LogLevel)LogLevel.Information, "ApplyExpressions finished");
                Log.ForContext("environment", $"{environmentContextData.CustomerSafeName}/{environmentContextData.EnvironmentSafeName}").Information("ApplyExpressions finished");
                this.methodState = ActorMethodState.Success;
                var metadata = JsonConvert.SerializeObject(result);
                this.jobActorHelper.UpdateLongRunningJobStateAndMetadata(this.Id.GetStringId(), longRunningJobId, LongRunningJobsStatus.Finished, metadata);
            }
            catch (OperationCanceledException)
            {
                try
                {
                    this.jobActorHelper.UpdateLongRunningJobStateAndMetadata(this.Id.GetStringId(), longRunningJobId, LongRunningJobsStatus.Cancelled, null);
                }
                catch { }
                this.LogJobIsCancelled(requestContext, longRunningJobId, LongRunningJobsJobType.ApplyExpressions);
            }
            catch (Exception e)
            {
                try
                {
                    this.jobActorHelper.UpdateLongRunningJobStateAndMetadata(this.Id.GetStringId(), longRunningJobId, LongRunningJobsStatus.Error, null);
                }
                catch { }
                Log.ForContext("environment", $"{environmentContextData.CustomerSafeName}/{environmentContextData.EnvironmentSafeName}").Error(e, "Error occurred in ApplyExpressions");
            }
        }

        private async Task WorkflowInspireInternalAsync(
            EnvironmentContextData environmentContextData,
            string username,
            int longRunningJobId,
            WorkflowInspireModel model)
        {
            var requestContext = this.GetRequestContextForEnvironment(environmentContextData);

            try
            {
                this.SetDimensions(requestContext.CustomerSafeName, requestContext.EnvironmentSafeName, longRunningJobId);
                this.CreateJobRepository(environmentContextData, username);

                // Set status to Running
                this.jobActorHelper.UpdateLongRunningJobState(this.Id.GetStringId(), longRunningJobId, LongRunningJobsStatus.Running);
                this.tokenSource.Token.ThrowIfCancellationRequested();

                this.FullLogInformation(requestContext, "WorkflowInspire started");

                var result = await this.DoWorkflowInspireAsync(requestContext, model, this.tokenSource.Token);

                // Set status to Finished and store result as metadata
                requestContext.Log((inRiver.Remoting.Log.LogLevel)LogLevel.Information, "WorkflowInspire finished");
                Log.ForContext("environment", $"{environmentContextData.CustomerSafeName}/{environmentContextData.EnvironmentSafeName}").Information("WorkflowInspire finished");
                this.methodState = ActorMethodState.Success;
                var metadata = JsonConvert.SerializeObject(result);
                this.jobActorHelper.UpdateLongRunningJobStateAndMetadata(this.Id.GetStringId(), longRunningJobId, LongRunningJobsStatus.Finished, metadata);
            }
            catch (OperationCanceledException)
            {
                try
                {
                    this.jobActorHelper.UpdateLongRunningJobStateAndMetadata(this.Id.GetStringId(), longRunningJobId, LongRunningJobsStatus.Cancelled, null);
                }
                catch { }
                this.LogJobIsCancelled(requestContext, longRunningJobId, LongRunningJobsJobType.WorkflowInspire);
            }
            catch (Exception e)
            {
                try
                {
                    this.jobActorHelper.UpdateLongRunningJobStateAndMetadata(this.Id.GetStringId(), longRunningJobId, LongRunningJobsStatus.Error, null);
                }
                catch { }
                Log.ForContext("environment", $"{environmentContextData.CustomerSafeName}/{environmentContextData.EnvironmentSafeName}").Error(e, "Error occurred in WorkflowInspire");
            }
            finally
            {
                RedisCacheManager.Instance.Invalidate(
                        requestContext.EnvironmentId,
                        $"WorkflowInspire-{model.InspireAgentId}-{model.StepGateId}-{model.EntityId}");

                var msg = $"{{\"event\":\"WorkflowInspireJobCompleted\",\"username\":\"{username}\",\"data\":{{\"agentId\": \"{model.InspireAgentId}\", \"entityId\":{model.EntityId}}}}}";
                _ = await ((RedisCacheManager)RedisCacheManager.Instance).RedisConnectionMultiplexer
                    .GetDatabase()
                    .ListLeftPushAsync($"{requestContext.EnvironmentId}-WorkflowQueue-v2", msg);
                _ = await ((RedisCacheManager)RedisCacheManager.Instance).RedisConnectionMultiplexer
                    .GetSubscriber()
                    .PublishAsync(
                        new RedisChannel($"{requestContext.EnvironmentId}-WorkflowQueueChannel", RedisChannel.PatternMode.Literal),
                        string.Empty);
            }
        }

        private async Task<object> DoWorkflowInspireAsync(RequestContext context, WorkflowInspireModel model, CancellationToken cancellationToken)
        {
            if (model.InspireAgentId == "InspireGenerateAgent")
            {
                await this.ProcessInspireGenerateRuleAsync(context, model.EntityId, model.GenerateTextModel, cancellationToken);
            }
            else if (model.InspireAgentId == "InspireTranslateAgent")
            {
                await this.ProcessInspireTranslateRuleAsync(context, model.EntityId, model.TranslateModel, cancellationToken);
            }
            else
            {
                Log.ForContext(
                    "environment",
                    $"{context.CustomerSafeName}/{context.EnvironmentSafeName}")
                    .Error($"Unknown Inspire agent {model.InspireAgentId}");
            }

            return null;
        }

        private async Task ProcessInspireTranslateRuleAsync(
            RequestContext context,
            int entityId,
            WorkflowInspireTranslateModel translateModel,
            CancellationToken cancellationToken)
        {
            var dataRepository = new DataRepository(context);
            try
            {
                // TODO: Check if there is enough tokens

                // Get values of fields to inspire
                var inspireHelper = new InspireHelper(context, Util.InspireBackendUrl);
                var entityDataWithLinks = inspireHelper.GetFullEntityWithLinks(entityId);

                var inspireEntityData = inspireHelper.EntityToInspireEntityModel(entityDataWithLinks);
                var inspireLanguages = inspireHelper.GetAllLanguages();
                cancellationToken.ThrowIfCancellationRequested();

                // Store data for reporting purposes
                var successful = new List<(Field Field, CultureInfo Cultureinfo)>();
                var failed = new List<(Field Field, CultureInfo Cultureinfo)>();
                var skipped = new List<(Field Field, CultureInfo Cultureinfo)>();

                // Filter out entity type definitions. If not including context, only current entity's type needs to be passed
                var relevantEntityTypes = new List<string>() { entityDataWithLinks.EntityType.Id };
                relevantEntityTypes.AddRange(inspireHelper.GetLinkedEntityTypes(inspireEntityData));
                var inspireEntityTypeDefinitions = inspireHelper.GetAllEntityTypes()
                    .Where(e => translateModel.IncludeContext ? relevantEntityTypes.Contains(e.Id) : e.Id == inspireEntityData.Summary.EntityTypeId)
                    .ToList();

                var localeStringFieldsToInspire = entityDataWithLinks.Fields
                    .Where(f => f.FieldType.DataType == DataType.LocaleString && translateModel.InspireFieldTypeIds.Any(fti => fti == f.FieldType.Id))
                    .ToList();

                var inspireFieldOptions = new InspireTranslateFieldOptionsModel()
                {
                    SourceLanguage = translateModel.InspireSourceLanguage,
                    ToLanguages = translateModel.InspireTargetLanguages,
                    EntityData = new List<InspireEntityModel> { inspireEntityData },
                    PimEntityTypeDefinitions = inspireEntityTypeDefinitions,
                    PimLanguage = inspireLanguages,
                    IncludeContext = translateModel.IncludeContext,
                };

                var entityTypeId = inspireEntityData.Summary.EntityTypeId;
                var segmentId = inspireEntityData.Summary.SegmentId;

                // Generate Inspire backend token
                var token = inspireHelper.GetInspireToken();

                var generatedData = new Dictionary<string, Field>();

                // TODO: Use Polly to handle retries

                if (!translateModel.Overwrite)
                {
                    var fieldTypeIds = new List<string>();
                    foreach (var field in localeStringFieldsToInspire)
                    {
                        var localeStringData = field.Data as LocaleString;
                        if (localeStringData == null ||
                            translateModel.InspireTargetLanguages.Any(l => string.IsNullOrEmpty(localeStringData[new CultureInfo(l)])))
                        {
                            // We can't inspire if source language is blank
                            if (localeStringData == null || string.IsNullOrEmpty(localeStringData[new CultureInfo(translateModel.InspireSourceLanguage)]))
                            {
                                foreach (var lang in translateModel.InspireTargetLanguages)
                                {
                                    var ci = new CultureInfo(lang);
                                    failed.Add((field, ci));
                                }
                            }
                            else
                            {
                                fieldTypeIds.Add(field.FieldType.Id);
                            }
                        }
                        else
                        {
                            foreach (var lang in translateModel.InspireTargetLanguages)
                            {
                                var ci = new CultureInfo(lang);
                                skipped.Add((field, ci));
                            }
                        }
                    }

                    inspireFieldOptions.FieldTypeIds = fieldTypeIds;

                    if (inspireFieldOptions.FieldTypeIds.Count != localeStringFieldsToInspire.Count)
                    {
                        if (skipped.Any())
                        {
                            Log.ForContext(
                                "environment",
                                $"{context.CustomerSafeName}/{context.EnvironmentSafeName}")
                                .Information($"Inspire: Skipping translation for some fields because they already have values. Entity [{entityId}].");
                        }

                        if (failed.Any())
                        {
                            Log.ForContext(
                                "environment",
                                $"{context.CustomerSafeName}/{context.EnvironmentSafeName}")
                                .Information($"Inspire: Skipping translation for some fields because their source is blank. Entity [{entityId}].");
                        }
                    }
                }
                else
                {
                    var fieldTypeIds = new List<string>();
                    foreach (var field in localeStringFieldsToInspire)
                    {
                        var localeStringData = field.Data as LocaleString;
                        // We can't inspire if source language is blank
                        if (localeStringData == null || string.IsNullOrEmpty(localeStringData[new CultureInfo(translateModel.InspireSourceLanguage)]))
                        {
                            foreach (var lang in translateModel.InspireTargetLanguages)
                            {
                                var ci = new CultureInfo(lang);
                                failed.Add((field, ci));
                            }
                        }
                        else
                        {
                            fieldTypeIds.Add(field.FieldType.Id);
                        }
                    }

                    inspireFieldOptions.FieldTypeIds = fieldTypeIds;
                    if (inspireFieldOptions.FieldTypeIds.Count != localeStringFieldsToInspire.Count)
                    {
                        Log.ForContext(
                            "environment",
                            $"{context.CustomerSafeName}/{context.EnvironmentSafeName}")
                            .Information($"Inspire: Skipping translation for some fields because their source is blank. Entity [{entityId}].");
                    }
                }

                if (localeStringFieldsToInspire.Count > 0)
                {
                    cancellationToken.ThrowIfCancellationRequested();
                    var glossaryConfig = await inspireHelper.GetGlossaryConfigAsync();
                    var matchingGlossaryConfig = glossaryConfig
                        ?.FirstOrDefault(g => g.Segment == segmentId);
                    var glossaryId = matchingGlossaryConfig?.GlossaryId;

                    if (glossaryId.HasValue)
                    {
                        inspireFieldOptions.Context = new List<StatelessContext>();
                        foreach (var field in localeStringFieldsToInspire)
                        {
                            var localeStringData = field.Data as LocaleString;
                            // We can't inspire if source language is blank
                            var textToTranslate = localeStringData[new CultureInfo(translateModel.InspireSourceLanguage)];
                            if (localeStringData != null && !string.IsNullOrEmpty(textToTranslate))
                            {
                                foreach (var toLang in inspireFieldOptions.ToLanguages)
                                {
                                    // Search glossary terms for the text to translate
                                    var glossaryTerms = await inspireHelper.SearchGlossaryTermsAsync(
                                        textToTranslate,
                                        translateModel.InspireSourceLanguage,
                                        toLang,
                                        glossaryId.Value);

                                    var statelessContext = new StatelessContext
                                    {
                                        FieldTypeId = field.FieldType.Id,
                                        Language = toLang,
                                        Glossary = glossaryTerms.Select(term => new Dictionary<string, object>
                                        {
                                            { translateModel.InspireSourceLanguage, term.SourceTerm },
                                            { toLang, term.TargetTerm },
                                            { "context", term.Context },
                                            { "relevanceScore", term.RelevanceScore }
                                        }).ToList()
                                    };

                                    inspireFieldOptions.Context.Add(statelessContext);
                                }
                            }
                        }
                    }

                    var inspireResponse = await inspireHelper.TranslateFieldsAsync(token, entityTypeId, inspireFieldOptions);

                    foreach (var ir in inspireResponse)
                    {
                        var fieldTypeId = ir.FieldTypeId;
                        var field = localeStringFieldsToInspire.Find(f => f.FieldType.Id == fieldTypeId);
                        if (field == null)
                        {
                            Log.ForContext(
                                "environment",
                                $"{context.CustomerSafeName}/{context.EnvironmentSafeName}")
                                .Error($"Translated field [{fieldTypeId}] not found in entity data of entity [{entityId}].");
                            failed.Add((new Field { FieldType = new FieldType { Id = fieldTypeId } }, null));
                            continue;
                        }

                        if (ir.Result == null || ir.Result.Results.Count() == 0)
                        {
                            Log.ForContext(
                                "environment",
                                $"{context.CustomerSafeName}/{context.EnvironmentSafeName}")
                                .Error($"No data translated for [{fieldTypeId}] of entity [{entityId}].");
                            foreach (var lang in translateModel.InspireTargetLanguages)
                            {
                                var ci = new CultureInfo(lang);
                                failed.Add((field, ci));
                            }

                            continue;
                        }

                        var fieldData = (field.Data as LocaleString) ?? new LocaleString();

                        foreach (var result in ir.Result.Results)
                        {
                            var ci = new CultureInfo(result.Language);
                            if (!translateModel.Overwrite && !string.IsNullOrEmpty(fieldData[ci]))
                            {
                                skipped.Add((field, ci));
                                continue;
                            }

                            if (!generatedData.ContainsKey(fieldTypeId))
                            {
                                generatedData[fieldTypeId] = field;
                            }

                            (generatedData[fieldTypeId].Data as LocaleString)[ci] = result.Translation;
                            successful.Add((field, ci));
                        }
                    }
                }

                var updatedFields = generatedData.Values.ToList();

                // Save new values
                if (updatedFields.Count > 0)
                {
                    // Create a new context with a different name
                    var inspireContext = new RequestContext()
                    {
                        Username = "Inspire",
                        DataLanguage = new CultureInfo("en"),
                        ModelLanguage = new CultureInfo("en"),
                        Roles = new List<string>(),
                        Permissions = new List<string>(),
                        EnvironmentId = context.EnvironmentId,
                        Module = "WorkflowInspireJob",
                        EnvironmentSafeName = context.EnvironmentSafeName,
                        CustomerId = context.CustomerId,
                        CustomerSafeName = context.CustomerSafeName,
                        ConnectionString = context.ConnectionString,
                        LogConnectionString = context.LogConnectionString,
                        LogTable = context.LogTable,
                        ConfigurationConnectionString = context.ConfigurationConnectionString,
                        ReadOnlyConfigDatabaseConnectionString = context.ReadOnlyConfigDatabaseConnectionString,
                        RequiredHttps = context.RequiredHttps,
                        AssetServiceUrl = context.AssetServiceUrl,
                        TokenServiceUrl = context.TokenServiceUrl,
                        EntityModel = context.EntityModel,
                    };

                    cancellationToken.ThrowIfCancellationRequested();
                    var inspireDataRepository = new DataRepository(inspireContext);
                    _ = inspireDataRepository.UpdateFieldsForEntity(updatedFields);

                    var comment = inspireHelper.CreateCommentForInspireTranslate(
                        successful,
                        failed,
                        skipped,
                        inspireEntityData.Summary.Id);
                    if (comment != null)
                    {
                        _ = dataRepository.AddCommentWithResult(comment);
                    }
                }
                else
                {
                    Log.ForContext(
                        "environment",
                        $"{context.CustomerSafeName}/{context.EnvironmentSafeName}")
                        .Information($"Inspire: No fields to translate for entity [{entityId}].");
                    var comment = inspireHelper.CreateCommentForInspireTranslate(
                        successful,
                        failed,
                        skipped,
                        inspireEntityData.Summary.Id);
                    if (comment != null)
                    {
                        _ = dataRepository.AddCommentWithResult(comment);
                    }
                }
            }
            catch (Exception e)
            {
                Log.ForContext(
                    "environment",
                    $"{context.CustomerSafeName}/{context.EnvironmentSafeName}")
                    .Error($"Unexpected error encountered while processing Inspire generate rule for entity [{entityId}].");
                var comment = new Comment
                {
                    EntityId = entityId,
                    Text = "An unexpected error was encountered while trying to translate text for this entity.",
                    Author = "Inspire"
                };
                _ = dataRepository.AddCommentWithResult(comment);
                throw;
            }
        }

        private async Task ProcessInspireGenerateRuleAsync(
            RequestContext context,
            int entityId,
            WorkflowInspireGenerateTextModel generateTextModel,
            CancellationToken cancellationToken)
        {
            var dataRepository = new DataRepository(context);
            try
            {
                // TODO: Check if there is enough tokens

                // Get values of fields to inspire
                var inspireHelper = new InspireHelper(context, Util.InspireBackendUrl);
                var entityDataWithLinks = inspireHelper.GetFullEntityWithLinks(entityId);

                var inspireEntityData = inspireHelper.EntityToInspireEntityModel(entityDataWithLinks);

                cancellationToken.ThrowIfCancellationRequested();
                
                // TODO: Add warning log if entity has a fieldSetId but no fieldset names in summary data

                // Fetch only relevant entity types
                var relevantEntityTypes = new List<string>() { entityDataWithLinks.EntityType.Id };
                relevantEntityTypes.AddRange(inspireHelper.GetLinkedEntityTypes(inspireEntityData));
                var inspireEntityTypeDefinitions = inspireHelper.GetAllEntityTypes()
                                                .Where(e => relevantEntityTypes.Contains(e.Id))
                                                .ToList();

                var inspireLanguages = inspireHelper.GetAllLanguages();

                // Store data for reporting purposes
                var successful = new List<(Field Field, CultureInfo Cultureinfo)>();
                var failed = new List<(Field Field, CultureInfo Cultureinfo)>();
                var skipped = new List<(Field Field, CultureInfo Cultureinfo)>();

                // Separate localestring fields and non-localestring fields so we can generate them in batch
                var localeStringFieldsToInspire = entityDataWithLinks.Fields
                    .Where(f => f.FieldType.DataType == DataType.LocaleString && generateTextModel.InspireFieldTypeIds.Any(fti => fti == f.FieldType.Id))
                    .ToList();

                var otherFieldsToInspire = entityDataWithLinks.Fields
                    .Where(f => f.FieldType.DataType != DataType.LocaleString && generateTextModel.InspireFieldTypeIds.Any(fti => fti == f.FieldType.Id))
                    .ToList();

                // Get filtered list of CVL data. If there is a parent key, it will only include the relevant values based on parent CVL field value
                var inspireCvls = inspireHelper.GetInspireCvlParamsFromEntity(entityDataWithLinks, otherFieldsToInspire);

                // Inspire returns the display name, we need to look up the key when saving
                var fieldToCvlValueToKeyMapping = inspireHelper.BuildFieldToCvlValueToKeyMapping(inspireCvls);

                var inspireFieldOptions = new InspireGenerateFieldOptionsModel()
                {
                    EntityData = new List<InspireEntityModel> { inspireEntityData },
                    PimEntityTypeDefinitions = inspireEntityTypeDefinitions,
                    PimLanguage = inspireLanguages,
                    CvlValues = inspireCvls,
                };

                var entityTypeId = inspireEntityData.Summary.EntityTypeId;

                // Generate Inspire backend token
                var token = inspireHelper.GetInspireToken();

                var generatedData = new Dictionary<string, Field>();
                var fieldsToUpdateOnSave = new Dictionary<string, Field>();

                // TODO: Use Polly to handle retries

                // Generate localestring fields. Must be done per language as this is an Inspire backend limitation
                foreach (var lang in generateTextModel.InspireTargetLanguages)
                {
                    // Create localized version of inspire options
                    var localizedInspireHelper = new InspireHelper(context, Util.InspireBackendUrl, lang);
                    var localizedInspireEntityData = localizedInspireHelper.EntityToInspireEntityModel(entityDataWithLinks);
                    var localizedInspireEntityTypeDefinitions = localizedInspireHelper.GetAllEntityTypes()
                        .Where(e => e.Id == localizedInspireEntityData.Summary.EntityTypeId)
                        .ToList();
                    var localizedInspireCvls = localizedInspireHelper.GetInspireCvlParamsFromEntity(entityDataWithLinks, localeStringFieldsToInspire);
                    var localizedInspireFieldOptions = new InspireGenerateFieldOptionsModel()
                    {
                        EntityData = new List<InspireEntityModel> { localizedInspireEntityData },
                        PimEntityTypeDefinitions = localizedInspireEntityTypeDefinitions,
                        PimLanguage = inspireLanguages,
                        CvlValues = localizedInspireCvls,
                        FieldTypeIds = localeStringFieldsToInspire
                                .Select(f => f.FieldType.Id)
                                .ToList()
                    };
                    var ci = new CultureInfo(lang);
                    if (!generateTextModel.Overwrite)
                    {
                        localizedInspireFieldOptions.FieldTypeIds = localeStringFieldsToInspire
                            .Where(f => f.Data == null || string.IsNullOrEmpty((f.Data as LocaleString)[ci]))
                            .Select(f => f.FieldType.Id)
                            .ToList();

                        if (localizedInspireFieldOptions.FieldTypeIds.Count != localeStringFieldsToInspire.Count())
                        {
                            Log.ForContext(
                                "environment",
                                $"{context.CustomerSafeName}/{context.EnvironmentSafeName}")
                                .Information($"Inspire: Skipping generation for some fields because they already have values. EntityId: [{entityId}]");

                            skipped.AddRange(localeStringFieldsToInspire
                                .Where(f => f.Data != null && !String.IsNullOrEmpty((f.Data as LocaleString)[ci]))
                                .Select(f => (f, ci)));
                        }
                    }

                    if (localizedInspireFieldOptions.FieldTypeIds.Count() == 0)
                    {
                        continue;
                    }

                    localizedInspireFieldOptions.Language = lang;
                    var localeStringResponses = await inspireHelper.GenerateFieldsAsync(token, entityTypeId, localizedInspireFieldOptions);
                    foreach (var lsr in localeStringResponses)
                    {
                        var fieldTypeId = lsr.FieldTypeId;
                        var field = localeStringFieldsToInspire.Find(f => f.FieldType.Id == fieldTypeId);
                        if (field == null)
                        {
                            Log.ForContext(
                                "environment",
                                $"{context.CustomerSafeName}/{context.EnvironmentSafeName}")
                                .Error($"Generated field {fieldTypeId} not found in entity data of entity [{entityId}]");
                            failed.Add((new Field { FieldType = new FieldType { Id = fieldTypeId } }, ci));
                            continue;
                        }

                        if (lsr.Result == null || lsr.Result.Results.Count == 0)
                        {
                            Log.ForContext(
                                "environment",
                                $"{context.CustomerSafeName}/{context.EnvironmentSafeName}")
                                .Error($"No data generated for [{fieldTypeId}] of entity [{entityId}]");
                            failed.Add((field, ci));
                            continue;
                        }

                        if (!generatedData.ContainsKey(fieldTypeId))
                        {
                            generatedData[fieldTypeId] = field;
                        }

                        var newData = (generatedData[fieldTypeId].Data as LocaleString) ?? new LocaleString();
                        newData[ci] = lsr.Result.Results[0].GeneratedText;
                        generatedData[fieldTypeId].Data = newData;
                        successful.Add((field, ci));
                    }
                }

                // Generate the other fields
                if (otherFieldsToInspire.Count() > 0)
                {
                    if (!generateTextModel.Overwrite)
                    {
                        inspireFieldOptions.FieldTypeIds = otherFieldsToInspire
                            .Where(f => f.Data == null || string.IsNullOrEmpty(f.Data.ToString()))
                            .Select(f => f.FieldType.Id)
                            .ToList();

                        if (inspireFieldOptions.FieldTypeIds.Count != otherFieldsToInspire.Count())
                        {
                            Log.ForContext(
                                "environment",
                                $"{context.CustomerSafeName}/{context.EnvironmentSafeName}")
                                .Information($"Inspire: Skipping generation for some fields because they already have values. Entity [{entityId}].");
                            skipped.AddRange(otherFieldsToInspire
                               .Where(f => f.Data != null && !string.IsNullOrEmpty(f.Data.ToString()))
                               .Select<Field, (Field, CultureInfo)>(f => (f, null)));
                        }
                    }
                    else
                    {
                        inspireFieldOptions.FieldTypeIds = otherFieldsToInspire
                                .Select(f => f.FieldType.Id)
                                .ToList();
                    }

                    inspireFieldOptions.Language = inspireHelper.GetMasterLanguage();
                    cancellationToken.ThrowIfCancellationRequested();
                    var inspireResponses = await inspireHelper.GenerateFieldsAsync(token, entityTypeId, inspireFieldOptions);
                    foreach (var ir in inspireResponses)
                    {
                        var fieldTypeId = ir.FieldTypeId;
                        var field = otherFieldsToInspire.Find(f => f.FieldType.Id == fieldTypeId);
                        if (field == null)
                        {
                            Log.ForContext(
                                "environment",
                                $"{context.CustomerSafeName}/{context.EnvironmentSafeName}")
                                .Error($"Generated field [{fieldTypeId}] not found in entity data of entity [{entityId}]");
                            failed.Add((new Field { FieldType = new FieldType { Id = fieldTypeId } }, null));
                            continue;
                        }

                        if (ir.Result == null || ir.Result.Results.Count == 0)
                        {
                            Log.ForContext(
                                "environment",
                                $"{context.CustomerSafeName}/{context.EnvironmentSafeName}")
                                .Error($"No data generated for [{fieldTypeId}] of entity [{entityId}]");
                            failed.Add((field, null));
                            continue;
                        }

                        generatedData[fieldTypeId] = field;
                        var generatedText = ir.Result.Results[0].GeneratedText;
                        if (field.FieldType.DataType == DataType.CVL)
                        {
                            if (field.FieldType.Multivalue)
                            {
                                var cvlValues = generatedText.Split(", ").ToList();
                                var cvlKeys = new List<string>();

                                foreach (var value in cvlValues)
                                {
                                    // We need to lookup the key for CVLs
                                    if (!fieldToCvlValueToKeyMapping.TryGetValue(ir.FieldTypeId, out var cvlMapping))
                                    {
                                        Log.ForContext(
                                            "environment",
                                            $"{context.CustomerSafeName}/{context.EnvironmentSafeName}")
                                            .Error($"Inspire: No CVL data found for generated field: [{ir.FieldTypeId}] of entity [{entityId}]");
                                    }
                                    else if (!cvlMapping.TryGetValue(value, out var cvlKey))
                                    {
                                        Log.ForContext(
                                            "environment",
                                            $"{context.CustomerSafeName}/{context.EnvironmentSafeName}")
                                            .Error($"Inspire: Key not found for generated CVL value: {value} for FieldTypeId [{ir.FieldTypeId}] of entity [{entityId}]");
                                    }
                                    else
                                    {
                                        cvlKeys.Add(cvlKey);
                                    }
                                }

                                if (cvlKeys.Any())
                                {
                                    generatedData[fieldTypeId].Data = string.Join(';', cvlKeys);
                                    successful.Add((field, null));
                                }
                                else
                                {
                                    failed.Add((field, null));
                                }
                            }
                            else
                            {
                                // We need to lookup the key for CVLs
                                if (!fieldToCvlValueToKeyMapping.TryGetValue(ir.FieldTypeId, out var cvlMapping))
                                {
                                    Log.ForContext(
                                            "environment",
                                            $"{context.CustomerSafeName}/{context.EnvironmentSafeName}")
                                            .Error($"Inspire: No CVL data found for generated field: [{ir.FieldTypeId}] of entity [{entityId}]");
                                    failed.Add((field, null));
                                }
                                else if (!cvlMapping.TryGetValue(generatedText, out var cvlKey))
                                {
                                    Log.ForContext(
                                            "environment",
                                            $"{context.CustomerSafeName}/{context.EnvironmentSafeName}")
                                            .Error($"Inspire: Key not found for generated CVL value: [{generatedText}] for FieldTypeId [{ir.FieldTypeId}] of entity [{entityId}]");
                                    failed.Add((field, null));
                                }
                                else
                                {
                                    generatedData[fieldTypeId].Data = cvlKey;
                                    successful.Add((field, null));
                                }
                            }
                        }
                        else
                        {
                            generatedData[fieldTypeId].Data = generatedText;
                            successful.Add((field, null));
                        }
                    }
                }

                var updatedFields = generatedData.Values
                    .Concat(fieldsToUpdateOnSave.Values)
                    .ToList();

                // Save new values
                if (updatedFields.Count > 0)
                {
                    // Create a new context with a different name
                    var inspireContext = new RequestContext()
                    {
                        Username = "Inspire",
                        DataLanguage = new CultureInfo("en"),
                        ModelLanguage = new CultureInfo("en"),
                        Roles = new List<string>(),
                        Permissions = new List<string>(),
                        EnvironmentId = context.EnvironmentId,
                        Module = "WorkflowInspireJob",
                        EnvironmentSafeName = context.EnvironmentSafeName,
                        CustomerId = context.CustomerId,
                        CustomerSafeName = context.CustomerSafeName,
                        ConnectionString = context.ConnectionString,
                        LogConnectionString = context.LogConnectionString,
                        LogTable = context.LogTable,
                        ConfigurationConnectionString = context.ConfigurationConnectionString,
                        ReadOnlyConfigDatabaseConnectionString = context.ReadOnlyConfigDatabaseConnectionString,
                        RequiredHttps = context.RequiredHttps,
                        AssetServiceUrl = context.AssetServiceUrl,
                        TokenServiceUrl = context.TokenServiceUrl,
                        EntityModel = context.EntityModel,
                    };

                    cancellationToken.ThrowIfCancellationRequested();
                    var inspireDataRepository = new DataRepository(inspireContext);
                    _ = inspireDataRepository.UpdateFieldsForEntity(updatedFields);

                    var comment = inspireHelper.CreateCommentForInspireGenerate(
                        successful,
                        failed,
                        skipped,
                        inspireEntityData.Summary.Id);

                    if (comment != null)
                    {
                        _ = dataRepository.AddCommentWithResult(comment);
                    }
                }
                else
                {
                    Log.ForContext(
                        "environment",
                        $"{context.CustomerSafeName}/{context.EnvironmentSafeName}")
                        .Information($"Inspire: No fields to generate for entity [{entityId}].");
                    var comment = inspireHelper.CreateCommentForInspireGenerate(
                        successful,
                        failed,
                        skipped,
                        inspireEntityData.Summary.Id);

                    if (comment != null)
                    {
                        _ = dataRepository.AddCommentWithResult(comment);
                    }
                }
            }
            catch (Exception e)
            {
                Log.ForContext(
                    "environment",
                    $"{context.CustomerSafeName}/{context.EnvironmentSafeName}")
                    .Error($"Unexpected error encountered while processing Inspire generate rule for entity [{entityId}].");
                var comment = new Comment
                {
                    EntityId = entityId,
                    Text = "An unexpected error was encountered while trying to generate text for this entity.",
                    Author = "Inspire"
                };
                _ = dataRepository.AddCommentWithResult(comment);
                throw;
            }
        }

        private void InitializeJobCancellationProcess() =>
            _ = Task.Run(
                async () => {
                    var actorId = this.Id.GetStringId();
                    if (!int.TryParse(actorId.Split('-').LastOrDefault(), out var jobId))
                    {
                        return;
                    }

                    while (true)
                    {
                        if (this.jobCancellationProcessTokenSource.IsCancellationRequested)
                        {
                            return; // The job is completed
                        }

                        LongRunningJobActorHelper jobActorHelper;
                        lock (jobRepositoryInitLock)
                        {
                            jobActorHelper = this.jobActorHelper;
                        }

                        if (jobActorHelper != null) // jobActorHelper does not exist before job is started
                        {
                            if (jobActorHelper.JobIsCancelled(actorId, jobId))
                            {
                                this.tokenSource.Cancel();
                                return;
                            }
                        }

                        await Task.Delay(this.FiveSecondsDelay, this.tokenSource.Token).ConfigureAwait(false);
                    }
                });

        private object DoMassUpdate(RequestContext context, MassUpdateModel model, User user, CancellationToken cancellationToken)
        {
            var failedEntityIds = new ConcurrentBag<int>();
            var succeededEntityIds = new ConcurrentBag<int>();
            var resultErrors = new ConcurrentDictionary<string, MassUpdateException>();

            if (model.EntityIds?.Count > 0)
            {
                var dataRepository = new DataRepository(context);
                var fieldRepository = new FieldRepository(context);
                var modelRepository = new ModelRepository(context);
                var multiCvlUpdateManager = new MultiCvlUpdateManager(fieldRepository);
                var dtoEntity = dataRepository.GetEntity(model.EntityIds[0], LoadLevel.Shallow);
                var entityType = modelRepository.GetEntityType(dtoEntity.EntityTypeId);

                var categoryRestrictionLookup = user.RestrictedFieldPermissions
                                .Where(restriction => restriction.CategoryId != null)
                                .ToLookup(restriction => Tuple.Create(restriction.EntityTypeId, restriction.CategoryId));

                var fieldTypeRestrictionLookup = user.RestrictedFieldPermissions
                    .Where(restriction => restriction.FieldTypeId != null)
                    .ToLookup(restriction => Tuple.Create(restriction.EntityTypeId, restriction.FieldTypeId));

                var jobProgressUpdater = new JobProgressUpdater(model.EntityIds.Count, this.UpdateJobProgressAsync);
                _ = Parallel.ForEach(model.EntityIds,
                    new ParallelOptions { MaxDegreeOfParallelism = 10 },
                    (entityId, forEachState) => {
                        try
                        {
                            if (cancellationToken.IsCancellationRequested)
                            {
                                forEachState.Break();
                            }

                            var entity = dataRepository.GetEntity(entityId, LoadLevel.Shallow);
                            int? entitySegmentId = entity.Segment?.Id ?? null;

                            var userRolesForSegment = new List<int>();
                            if (entitySegmentId != null)
                            {
                                // get User's Roles associated to the SegmentId of the Entity
                                userRolesForSegment = user.GetRolesForSegmentId(entitySegmentId.Value)
                                    .Select(p => p.Id).ToList();
                            }

                            var entityFields = multiCvlUpdateManager.ProcessEntityFields(model.Fields, entityType, entityId).ToList();
                            var fields = DtoFactory.FieldsFromDtos(entityFields, entityType);
                            fields.ForEach(f => f.EntityId = entityId);

                            var toUpdateFields = new List<Field>();
                            foreach (var field in fields)
                            {
                                var fieldType = field.FieldType;

                                var restriction =
                                categoryRestrictionLookup[Tuple.Create(entity.EntityTypeId, fieldType.CategoryId)]
                                    .FirstOrDefault(a => userRolesForSegment.Contains(a.RoleId))
                                ?? fieldTypeRestrictionLookup[Tuple.Create(fieldType.EntityTypeId, fieldType.Id)]
                                       .FirstOrDefault(a => userRolesForSegment.Contains(a.RoleId));

                                if (restriction != null)
                                {
                                    continue;
                                }

                                toUpdateFields.Add(field);
                            }

                            if (toUpdateFields.Count > 0)
                            { 
                                _ = fieldRepository.UpdateFieldsForEntity(toUpdateFields);
                                succeededEntityIds.Add(entityId);
                            }
                        }
                        catch (Exception exception)
                        {
                            AddErrorMessage(resultErrors, exception, entityId);
                            failedEntityIds.Add(entityId);   
                        }
                        finally
                        {
                            jobProgressUpdater.Increment();
                        }
                    });

                jobProgressUpdater.EnsureFinalUpdateAsync().Wait(cancellationToken);
            }

            if (failedEntityIds.Any())
            {
                LogMassUpdateErrors(resultErrors.Values, context);
            }

            return new {
                FailedEntityIds = failedEntityIds.ToList(),
                SucceededEntityIds = succeededEntityIds.ToList()
            };
        }

        private object DoApplyExpressions(RequestContext context, ApplyExpressionsModel model, CancellationToken cancellationToken)
        {
            var failedEntityIds = new ConcurrentBag<int>();
            var resultErrors = new ConcurrentDictionary<string, MassUpdateException>();
            var dataRepository = new DataRepository(context);
            var fieldRepository = new FieldRepository(context);
            var modelRepository = new ModelRepository(context);

            var entityType = modelRepository.GetEntityType(model.EntityTypeId);

            if (model.Target == ExpressionTarget.FIELDTYPEID)
            {
                var fieldType = entityType.FieldTypes.FirstOrDefault(ft => ft.Id == model.FieldTypeId);
                var entityIds = dataRepository.GetAllEntityIdsForEntityType(model.EntityTypeId);
                var jobProgressUpdater = new JobProgressUpdater(entityIds.Count, this.UpdateJobProgressAsync);
                _ = Parallel.ForEach(
                    entityIds,
                    new ParallelOptions { MaxDegreeOfParallelism = 10 },
                    (entityId, forEachState) => {
                        try
                        {
                            if (cancellationToken.IsCancellationRequested)
                            {
                                forEachState.Break();
                                return;
                            }

                            if (model.OverwriteMode == ApplyExpressionOverwriteMode.All)
                            {
                                var replacementField = new Field
                                {
                                    EntityId = entityId,
                                    Data = model.Expression,
                                    FieldType = fieldType,
                                };

                                _ = fieldRepository.UpdateFieldsForEntity(new List<Field> { replacementField });
                            }
                            else if (model.OverwriteMode == ApplyExpressionOverwriteMode.EmptyOnly)
                            {
                                var field = fieldRepository.GetField(entityId, fieldType);
                                if (field == null || field.IsEmpty())
                                {
                                    var replacementField = new Field
                                    {
                                        EntityId = entityId,
                                        Data = model.Expression,
                                        FieldType = fieldType,
                                    };

                                    _ = fieldRepository.UpdateFieldsForEntity(new List<Field> { replacementField });
                                }
                            }
                            else if (model.OverwriteMode == ApplyExpressionOverwriteMode.ExpressionsOnly)
                            {
                                var expressionExists = dataRepository.EntityHasExpression(entityId, model.FieldTypeId, "FIELDTYPEID");
                                if (expressionExists)
                                {
                                    var replacementField = new Field
                                    {
                                        EntityId = entityId,
                                        Data = model.Expression,
                                        FieldType = fieldType,
                                    };

                                    _ = fieldRepository.UpdateFieldsForEntity(new List<Field> { replacementField });
                                }
                            }
                        }
                        catch (Exception exception)
                        {
                            AddErrorMessage(resultErrors, exception, entityId);
                            failedEntityIds.Add(entityId);
                        }
                        finally
                        {
                            jobProgressUpdater.Increment();
                        }
                    });

                jobProgressUpdater.EnsureFinalUpdateAsync().Wait(cancellationToken);
            }
            else if (model.Target == ExpressionTarget.FIELDSETID)
            {
                var entityIds = dataRepository.GetAllEntityIdsForEntityType(model.EntityTypeId);
                var jobProgressUpdater = new JobProgressUpdater(entityIds.Count, this.UpdateJobProgressAsync);
                _ = Parallel.ForEach(
                    entityIds,
                    new ParallelOptions { MaxDegreeOfParallelism = 10 },
                    (entityId, forEachState) => {
                        try
                        {
                            if (cancellationToken.IsCancellationRequested)
                            {
                                forEachState.Break();
                                return;
                            }

                            if (model.OverwriteMode == ApplyExpressionOverwriteMode.All)
                            {
                                _ = dataRepository.SetEntityFieldSet(entityId, model.Expression);
                            }
                            else if (model.OverwriteMode == ApplyExpressionOverwriteMode.EmptyOnly)
                            {
                                var e = dataRepository.GetEntity(entityId, LoadLevel.Shallow);
                                if (string.IsNullOrEmpty(e?.FieldSetId))
                                {
                                    _ = dataRepository.SetEntityFieldSet(entityId, model.Expression);
                                }
                            }
                            else if (model.OverwriteMode == ApplyExpressionOverwriteMode.ExpressionsOnly)
                            {
                                var expressionExists = dataRepository.EntityHasExpression(entityId, "FIELDSETID", "ENTITYMETADATA");
                                if (expressionExists)
                                {
                                    _ = dataRepository.SetEntityFieldSet(entityId, model.Expression);
                                }
                            }
                        }
                        catch (Exception exception)
                        {
                            AddErrorMessage(resultErrors, exception, entityId);
                            failedEntityIds.Add(entityId);
                        }
                        finally
                        {
                            jobProgressUpdater.Increment();
                        }
                    });

                jobProgressUpdater.EnsureFinalUpdateAsync().Wait(cancellationToken);
            }
            else if (model.Target == ExpressionTarget.SEGMENT)
            {
                if (model.OverwriteMode == ApplyExpressionOverwriteMode.All)
                {
                    var availableSegmentIds = context.DataPersistance.GetAllSegmentsAsync().GetAwaiter().GetResult().Select(x => x.Id).ToHashSet();

                    var entityIds = dataRepository.GetAllEntityIdsForEntityType(model.EntityTypeId);
                    dataRepository.SetSegmentExpressionForEntities(entityIds, model.Expression, availableSegmentIds);
                }
                else if (model.OverwriteMode == ApplyExpressionOverwriteMode.ExpressionsOnly)
                {
                    var availableSegmentIds = context.DataPersistance.GetAllSegmentsAsync().GetAwaiter().GetResult().Select(x => x.Id).ToHashSet();

                    var entityIds = dataRepository.GetEntityIdsForEntityTypeWithExpressions(
                        model.EntityTypeId,
                        ExpressionTarget.SEGMENT,
                        ExpressionTargetType.ENTITYMETADATA);
                    dataRepository.SetSegmentExpressionForEntities(entityIds, model.Expression, availableSegmentIds);
                }
            }

            if (failedEntityIds.Any())
            {
                LogMassUpdateErrors(resultErrors.Values, context);
            }

            return new {
                FailedEntityIds = failedEntityIds.ToList()
            };
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage(
            "Design",
            "CA1031:Do not catch general exception types",
            Justification = "Logging and continuing is required for batch processing; individual failures are non-fatal.")]
        private async Task UpdateJobProgressAsync(int percentCompleted)
        {
            try
            {
                await this.jobRepository.UpdateLongRunningJobPercentCompletedAsync(
                    this.currentLongRunningJobId,
                    percentCompleted).ConfigureAwait(false);
            }
            catch (Exception e) // failure in updating the progress should not affect the job execution
            {
                Log.Warning(
                    e,
                    "UpdateJobProgressAsync encountered an error for Job {LongRunningJobId}: {ExceptionMessage}",
                    this.currentLongRunningJobId,
                    e.Message);
            }
        }

        private void CreateJobRepository(EnvironmentContextData environmentContextData, string username)
        {
            var inRiverPersistance = IPMCPersistanceFactory.GetInstance(
                environmentContextData.ConnectionString,
                this.CreateApiCaller(username),
                environmentContextData.EntityModel,
                environmentContextData.EnvironmentId);

            lock (jobRepositoryInitLock)
            {
                this.jobRepository = new JobRepository(inRiverPersistance, this.CreateApiCaller(username));
                this.jobActorHelper = new LongRunningJobActorHelper(this.jobRepository, this.longRunningJobCache);
            }
        }

        private static void AddErrorMessage(IDictionary<string, MassUpdateException> resultErrors, Exception exception, int entityId)
        {
            var message = exception?.Message ?? string.Empty;

            if (resultErrors.TryGetValue(message, out var value))
            {
                value.EntityIds.Add(entityId);
            }
            else
            {
                resultErrors.Add(message, new MassUpdateException(message, exception)
                {
                    EntityIds = new List<int> { entityId }
                });
            }
        }

        private static void LogMassUpdateErrors(IEnumerable<MassUpdateException> resultErrors, RequestContext context)
        {
            foreach (var error in resultErrors)
            {
                var entityIds = string.Join(", ", error.EntityIds);

                var massUpdateErrorMessage = $"Failed to update the following entities: {entityIds}";

                LogExceptionToAppropriateSink(error.InnerException, massUpdateErrorMessage, context);
            }
        }

        /// <summary>
        /// Log exception to logging facilities depending on type of error.
        /// We shouldn't show unexpected errors to customers.
        /// </summary>
        private static void LogExceptionToAppropriateSink(Exception error, string errorMessage, RequestContext context)
        {
            if (error is ArgumentException)
            {
                context.Log((inRiver.Remoting.Log.LogLevel)LogLevel.Error, $"{errorMessage}. {error.Message}");
            }

            Log.Error(error, "{errorMessage}, Environment: {CustomerSafeName}/{EnvironmentSafeName}", errorMessage, context.CustomerSafeName, context.EnvironmentSafeName);
        }

        private long LogMemoryInMB(string message, long memoryInBytes)
        {
            var memoryToLog = (double)memoryInBytes / MBtoBytes;
            Log.Information(message, memoryToLog);
            return memoryInBytes;
        }

        private void FullLogInformation(RequestContext requestContext, string message)
        {
            requestContext.Log((inRiver.Remoting.Log.LogLevel)LogLevel.Information, message);
            Log.Information(message);
        }

        private RequestContext GetRequestContextForEnvironment(EnvironmentContextData environmentContext)
        {
            if (environmentContext == null)
            {
                Log.Error("Environment context data is null.");
                return null;
            }

            try
            {
                var context = new RequestContext(environmentContext)
                {
                    Username = "system",
                    DataLanguage = new CultureInfo("en"),
                    ModelLanguage = new CultureInfo("en"),
                    Roles = new List<string>(),
                    Permissions = new List<string>(),
                    Module = "LongRunningJob",
                    EnvironmentId = environmentContext.EnvironmentId,
                    EnvironmentSafeName = environmentContext.EnvironmentSafeName,
                    CustomerId = environmentContext.CustomerId,
                    CustomerSafeName = environmentContext.CustomerSafeName,
                    ConnectionString = environmentContext.ConnectionString,
                    LogConnectionString = environmentContext.LogConnectionString,
                    LogTable = environmentContext.LogTable,
                    ConfigurationConnectionString = StackConfig.Instance.ConfigurationDatabaseConnectionString.UnScramble(),
                    ReadOnlyConfigDatabaseConnectionString = StackConfig.Instance.ReadOnlyConfigDatabaseConnectionString.UnScramble(),
                    AssetServiceUrl = Util.CheckIfFabricUrlAndParse(environmentContext.AssetServiceInternalUrl),
                    TokenServiceUrl = Util.TokenServiceAddress(),
                    RequiredHttps = Util.GetRequiredHttps(),
                    SendGridApiKey = StackConfig.Instance.SendGridApiKey.UnScramble(),
                    SmtpSendUser = Util.SMTPSendUser(),
                    SmtpSendUserName = Util.SMTPSendUserName(),
                    EntityModel = environmentContext.EntityModel
                };

                return context;
            }
            catch (Exception e)
            {
                Log.Error(e, "Unexpected error occurred when trying to get RequestContext for Customer: {customerSafeName}/{environmentSafeName}.", environmentContext.CustomerSafeName, environmentContext.EnvironmentSafeName);
                return null;
            }
        }

        private ApiCaller CreateApiCaller(string username) =>
            new ApiCaller()
            {
                Username = username,
                Module = nameof(LongRunningJobActor)
            };

        private void LogJobIsCancelled(RequestContext requestContext, int jobId, string jobType)
        {
            var logMessage = $"{jobType} job with id: {jobId} was cancelled";
            requestContext.Log((inRiver.Remoting.Log.LogLevel)LogLevel.Information, logMessage);
            Log.Information(logMessage, jobId);
            this.methodState = ActorMethodState.Success;
        }

        private static void PopulateRequestContextForUserAccess(RequestContext requestContext, string username)
        {
            if (string.IsNullOrEmpty(username))
            {
                return;
            }

            var user = requestContext.DataPersistance.GetUserByUsername(username);
            if (user == null)
            {
                return;
            }

            requestContext.ContentSegmentIdToPermissions = user.GetSegments()
                .ToDictionary(
                    segment => segment.Id,
                    segment => user.GetPermissionsForSegmentId(segment.Id).Select(s => s.Name).ToList());
            requestContext.Username = username;
            requestContext.Roles = user.Roles.Select(r => r.Name).ToList();
            requestContext.Permissions = user.Permissions.Select(p => p.Name).ToList();
        }
    }
}
