<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Storage.Queues" Version="12.22.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="3.1.9" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="6.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\inRiver.Configuration.Core\inRiver.Configuration.Core.csproj" />
    <ProjectReference Include="..\inRiver.Core\inRiver.Core.csproj" />
  </ItemGroup>

</Project>
