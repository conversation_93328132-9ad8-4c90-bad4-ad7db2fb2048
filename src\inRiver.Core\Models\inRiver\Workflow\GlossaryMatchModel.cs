namespace inRiver.Core.Models.inRiver.Workflow
{
    using System;
    using System.Collections.Generic;
    using System.Text;

    public class GlossaryMatchModel
    {
        public string SourceTerm { get; set; }
        public string TargetTerm { get; set; }
        public string Context { get; set; }
        public int RelevanceScore { get; set; }
        public string GlossaryName { get; set; }
        public int MatchCount { get; set; }
    }
}
