namespace LongRunningJobQueueService
{
    using AzureQueue.Extensions;
    using BackgroundServices;
    using Constants;
    using Data.Extensions;
    using Inriver.StackEssentials;
    using Inriver.StackEssentials.DependencyInjection;
    using Microsoft.ApplicationInsights;
    using Microsoft.ApplicationInsights.AspNetCore.Extensions;
    using Microsoft.ApplicationInsights.Extensibility;
    using Microsoft.AspNetCore.Builder;
    using Microsoft.AspNetCore.Hosting;
    using Microsoft.Extensions.Configuration;
    using Microsoft.Extensions.DependencyInjection;
    using Microsoft.Extensions.Hosting;
    using Serilog;
    using Serilog.Core;
    using Serilog.Events;
    using Telemetry.Initializers;
    using Utils;

    public class Startup
    {
        private readonly IWebHostEnvironment environment;

        public IConfiguration Configuration { get; }

        public Startup(IConfiguration configuration, IWebHostEnvironment environment)
        {
            this.Configuration = configuration;
            this.environment = environment;
            StackEssentialsInitializer.Init(Util.KeyVaultBaseUrl, Util.StackConfigSecretName);
        }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            var options = new ApplicationInsightsServiceOptions
            {
                InstrumentationKey = Util.InstrumentationKey,
                DeveloperMode = this.environment.IsDevelopment(),
                EnableDependencyTrackingTelemetryModule = false
            };

            services.AddApplicationInsightsTelemetry(options);
            services.AddSingleton<ITelemetryInitializer>(_ =>
                new CloudRoleNameTelemetryInitializer(LogConstants.CloudRoleName));

            _ = services
                .AddHostedService<JobStatusQueueBackgroundService>()
                .AddStackConfig(stackConfigOptions => {
                    stackConfigOptions.IsLocalDev = false;
                    stackConfigOptions.KeyVaultBaseUrl = Util.KeyVaultBaseUrl;
                    stackConfigOptions.StackConfigSecretName = Util.StackConfigSecretName;
                })
                .AddDataServices()
                .AddAzureQueue(this.Configuration, Util.KeyVaultBaseUrl);
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env, TelemetryClient telemetryClient)
        {
            ConfigureLogging(telemetryClient);

            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }
        }

        private static void ConfigureLogging(TelemetryClient telemetryClient)
        {
            var levelSwitch = new LoggingLevelSwitch();

            if (System.Enum.TryParse(Util.LogLevel, out LogEventLevel logEventLevel))
            {
                levelSwitch.MinimumLevel = logEventLevel;
            }

            Log.Logger = new LoggerConfiguration()
                .MinimumLevel.ControlledBy(levelSwitch)
                .WriteTo.ApplicationInsights(telemetryClient, TelemetryConverter.Traces)
                .CreateLogger();

            Log.Information("Starting {Name} application.", LogConstants.CloudRoleName);
        }
    }
}
