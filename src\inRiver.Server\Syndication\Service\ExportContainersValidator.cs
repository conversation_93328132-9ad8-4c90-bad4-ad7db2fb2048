namespace inRiver.Server.Syndication.Service
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using inRiver.Server.Syndication.Constants;
    using inRiver.Server.Syndication.Exceptions;
    using inRiver.Server.Syndication.Export;
    using inRiver.Server.Syndication.Mapping;
    using inRiver.Server.Syndication.Models;
    using inRiver.Server.Syndication.Service.Interfaces;

    public class ExportContainersValidator : IExportContainersValidator
    {
        public ValidationResult Validate(IList<MapField> formatFileFields, IList<ExportContainer> exportContainers, bool isDynamicFormatFile = false)
        {
            var result = new ValidationResult();
            foreach (var exportContainer in exportContainers)
            {
                var errors = new List<ValidationError>();
                var warnings = new List<ValidationError>();
                foreach (var exportContainerField in exportContainer.Fields)
                {
                    var formatFileField = this.GetFormatFileField(formatFileFields, exportContainerField, isDynamicFormatFile);
                    var fieldMetadata = formatFileField?.MapFieldType;
                    if (fieldMetadata == null)
                    {
                        continue;
                    }

                    var mappingFormatFieldId = this.GetMappingFormatFieldId(formatFileField);
                    if (fieldMetadata.Mandatory && exportContainerField.Data is null)
                    {
                        errors.Add(new MandatoryFieldValidationError(exportContainerField.Id, mappingFormatFieldId));
                    }

                    if (fieldMetadata.Recommended && exportContainerField.Data is null)
                    {
                        warnings.Add(new RecommendedFieldValidationError(exportContainerField.Id, mappingFormatFieldId));
                    }

                    if ((fieldMetadata.DataType?.Equals("Number", StringComparison.Ordinal) ?? false)
                        && (exportContainerField.Data is null || !double.TryParse(exportContainerField.Data.ToString(), out _)))
                    {
                        errors.Add(new NumberFieldValidationError(exportContainerField.Id, mappingFormatFieldId));
                    }

                    if ((fieldMetadata.DataType?.Equals("Enum", StringComparison.OrdinalIgnoreCase) ?? false)
                        && exportContainerField.Data != null
                        && !ValidateEnumField(formatFileField.Enumerations.Select(e => e.EnumValue).ToList(), exportContainerField.Data.ToString()))
                    {
                        errors.Add(new EnumFieldValidationError(exportContainerField.Id, mappingFormatFieldId));
                    }

                    if (fieldMetadata.MinLength > 0 && exportContainerField.Data != null
                        && exportContainerField.Data.ToString().Length < fieldMetadata.MinLength)
                    {
                        errors.Add(new MinLengthFieldValidationError(exportContainerField.Id, mappingFormatFieldId));
                    }

                    if (fieldMetadata.MaxLength > 0 && exportContainerField.Data != null
                        && exportContainerField.Data.ToString().Length > fieldMetadata.MaxLength)
                    {
                        errors.Add(new MaxLengthFieldValidationError(exportContainerField.Id, mappingFormatFieldId));
                    }
                }

                if (errors.Count == 0 && warnings.Count == 0)
                {
                    continue;
                }

                result.Rows.Add(new ValidationRow
                {
                    EntityId = exportContainer.Id,
                    Errors = errors,
                    Warnings = warnings
                });
            }

            return result;
        }

        public int? GetMappingFormatFieldId(MapField formatFileField) => formatFileField.MappingFormatFieldId < 0
            ? null
            : formatFileField.MappingFormatFieldId as int?;

        public MapField GetFormatFileField(IList<MapField> formatFileFields, ExportField exportContainerField, bool isDynamicFormatFile)
        {
            if (!isDynamicFormatFile)
            {
                return formatFileFields.FirstOrDefault(x => x.MappingFormatFieldId == exportContainerField.MappingFormatFieldId);
            }

            var dynamicFields = formatFileFields.Where(x => x.MapFieldTypeId == exportContainerField.Id).ToList();
            if (dynamicFields.Count > 1)
            {
                throw new DynamicMapFieldTypeIdNotUniqueException(mapFieldTypeId: exportContainerField.Id);
            }

            return dynamicFields.FirstOrDefault();
        }

        public ValidationProgress GetUpdatedProgress(int batchSize, int currentBatch, int totalEntities, int currentEntities)
        {
            if (batchSize <= 0)
            {
                throw new ArgumentException(nameof(batchSize));
            }

            var totalBatches = (int)Math.Ceiling((double)totalEntities / batchSize);
            var status = totalBatches == currentBatch ? ValidationStatus.Finished : ValidationStatus.InProgress;
            var processedEntities = ((currentBatch - 1) * batchSize) + currentEntities;

            return new ValidationProgress
            {
                BatchSize = batchSize,
                CurrentBatch = currentBatch,
                LastUpdate = DateTime.UtcNow,
                TotalEntities = totalEntities,
                ProcessedEntities = processedEntities,
                TotalBatches = totalBatches,
                Status = status
            };
        }

        private static bool ValidateEnumField(ICollection<string> enumValues, string fieldData)
        {
            var subValues = fieldData.Split(';');
            foreach (var subValue in subValues)
            {
                if (!enumValues.Remove(subValue))
                {
                    return false;
                }
            }

            return true;
        }

        public ValidationProgress GetProgressWithError() =>
            new ValidationProgress
            {
                Status = ValidationStatus.Error,
            };
    }
}
