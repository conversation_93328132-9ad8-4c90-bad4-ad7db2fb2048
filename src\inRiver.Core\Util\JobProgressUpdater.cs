namespace inRiver.Core.Util
{
    using System;
    using System.Threading.Tasks;
    using Serilog;

    public class JobProgressUpdater
    {
        private readonly int totalCount;
        private readonly Func<int, Task> updateCallback;
        private readonly object callbackLock = new object();
        private readonly object incrementLock = new object();
        private Task lastUpdateTask = Task.CompletedTask;
        private int lastPercent = -1;
        private int completedCount;
        private int currentPercentCompleted = -1;

        public JobProgressUpdater(int totalCount, Func<int, Task> updateCallback = null)
        {
            if (totalCount == 0)
            {
                throw new ArgumentException("Total Count must be greater than zero.", nameof(totalCount));
            }

            this.totalCount = totalCount;
            this.updateCallback = updateCallback;
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage(
            "Design",
            "CA1031:Do not catch general exception types",
            Justification = "Progress update is not a fatal error.")]
        public void Increment()
        {
            try
            {
                int percentCompleted;
                var shouldUpdate = false;

                lock (this.incrementLock)
                {
                    this.completedCount++;
                    percentCompleted = (int)((double)this.completedCount / this.totalCount * 100);
                    if (percentCompleted > this.currentPercentCompleted)
                    {
                        this.currentPercentCompleted = percentCompleted;
                        shouldUpdate = true;
                    }
                }

                if (!shouldUpdate || this.updateCallback == null)
                {
                    return;
                }

                lock (this.callbackLock)
                {
                    // Always update if percent changed or is 100%
                    if (this.lastPercent == percentCompleted && percentCompleted != 100)
                    {
                        return;
                    }

                    if (this.lastUpdateTask.IsCompleted)
                    {
                        this.lastUpdateTask = this.updateCallback(percentCompleted);
                    }
                    else
                    {
                        if (percentCompleted >= 100) // handle over-increment updates
                        {
                            this.lastUpdateTask = this.lastUpdateTask.ContinueWith(task =>
                                this.updateCallback(percentCompleted)).Unwrap();
                        }
                    }

                    this.lastPercent = percentCompleted;
                }
            }
            catch (Exception e)
            {
                Log.Warning(e, "JobProgressUpdater Increment encountered an exception: {Message}", e.Message);
            }
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage(
            "Design",
            "CA1031:Do not catch general exception types",
            Justification = "Progress update is not a fatal error.")]
        public async Task EnsureFinalUpdateAsync()
        {
            try
            {
                Task task;
                lock (this.callbackLock)
                {
                    task = this.lastUpdateTask;
                }

                if (task == null)
                {
                    return;
                }

                await task.ConfigureAwait(false);
            }
            catch (Exception e)
            {
                Log.Warning(e, "JobProgressUpdater EnsureFinalUpdateAsync encountered an exception: {Message}", e.Message);
            }
        }
    }
}
