namespace LongRunningJobService.Controllers
{
    using System;
    using System.Collections.Generic;
    using System.Threading.Tasks;
    using global::LongRunningJobService.Abstractions;
    using global::LongRunningJobService.Code;
    using inriver.Expressions.Client;
    using inriver.Expressions.Client.Constants;
    using inRiver.Core.Enum;
    using inRiver.Core.Models.inRiver.ApplyExpressions;
    using inRiver.Core.Models.inRiver.DeleteAllLinksForLinkType;
    using inRiver.Core.Models.inRiver.ExcelExport;
    using inRiver.Core.Models.inRiver.ExcelImport;
    using inRiver.Core.Models.inRiver.MassExcludeTypes;
    using inRiver.Core.Models.inRiver.MassUpdate;
    using inRiver.Core.Models.inRiver.Workflow;
    using inRiver.Server.Syndication;
    using inRiver.Server.Syndication.Models;
    using Microsoft.AspNetCore.Mvc;
    using Serilog;

    [Route("api/[controller]")]
    public class LongRunningJobController : Controller
    {
        private readonly ILongRunningJobInitializer jobInitializer;

        public LongRunningJobController(ILongRunningJobInitializer jobInitializer)
        {
            this.jobInitializer = jobInitializer;
        }

        [HttpPost]
        [Route("{customerSafeName}/{environmentSafeName}/{jobId}/Cancel")]
        public IActionResult CancelJob(string customerSafeName, string environmentSafeName, int jobId)
        {
            try
            {
                var longRunningJobManager = new LongRunningJobManager(customerSafeName, environmentSafeName);
                longRunningJobManager.CancelJob(jobId);

                return this.Ok();
            }
            catch
            {
                return this.BadRequest();
            }
        }

        [HttpGet]
        [Route("CalculateCompleteness/{customerSafeName}/{environmentSafeName}/{entitytypeid}")]
        public void CalculateCompleteness(string customerSafeName, string environmentSafeName, string entitytypeid)
        {
            var longRunningJobManager = new LongRunningJobManager(customerSafeName, environmentSafeName);
            longRunningJobManager.InitialiseCompletenessJob(entitytypeid);
        }

        [HttpGet]
        [Route("RebuildChannel/{customerSafeName}/{environmentSafeName}/{channelId}")]
        public void RebuildChannel(string customerSafeName, string environmentSafeName, string channelId)
        {
            try
            {
                Log.Information("RebuildChannel for {Customer} : {Environment} : {ChannelId}", customerSafeName, environmentSafeName, channelId);
                var longRunningJobManager = new LongRunningJobManager(customerSafeName, environmentSafeName);
                longRunningJobManager.InitialiseRebuildChannelJob(Convert.ToInt32(channelId));
            }
            catch (Exception e)
            {
                Log.Error(e, "Error occured in RebuildChannel for ChannelId: {ChannelId}, Customer: {Customer}/{Environment}", channelId, customerSafeName, environmentSafeName);
            }
        }

        [HttpGet]
        [Route("SynchronizeChannel/{customerSafeName}/{environmentSafeName}/{channelId}")]
        public void SynchronizeChannel(string customerSafeName, string environmentSafeName, string channelId)
        {
            var longRunningJobManager = new LongRunningJobManager(customerSafeName, environmentSafeName);
            longRunningJobManager.InitialiseSynchronizeChannelJob(Convert.ToInt32(channelId));
        }

        [HttpPost]
        [Route("ExportExcel/{customerSafeName}/{environmentSafeName}/{username}")]
        public void ExportExcel(string customerSafeName, string environmentSafeName, string username, [FromBody]ExcelExportModel excelExportModel)
        {
            var longRunningJobManager = new LongRunningJobManager(customerSafeName, environmentSafeName);
            longRunningJobManager.InitialiseExcelExportJob(username, excelExportModel);
        }

        [HttpPost]
        [Route("ExportExcelHistory/{customerSafeName}/{environmentSafeName}/{username}")]
        public void ExportExcelHistory(string customerSafeName, string environmentSafeName, string username, [FromBody]ExcelExportHistoryModel excelExportHistoryModel)
        {
            var longRunningJobManager = new LongRunningJobManager(customerSafeName, environmentSafeName);
            longRunningJobManager.InitialiseExcelExportHistoryJob(username, excelExportHistoryModel);
        }

        [HttpPost]
        [Route("ImportExcel/{customerSafeName}/{environmentSafeName}/{username}")]
        public int ImportExcel(string customerSafeName, string environmentSafeName, string username, [FromBody]ExcelImportModel excelImportModel) // filename, validate, import
        {
            var longRunningJobManager = new LongRunningJobManager(customerSafeName, environmentSafeName);
            return longRunningJobManager.InitialiseExcelImportJob(username, excelImportModel);
        }

        [HttpPost]
        [Route("RunSyndicate/{customerSafeName}/{environmentSafeName}/{username}")]
        public SyndicationResponseModel RunSyndicate(string customerSafeName, string environmentSafeName, string username, [FromBody]SyndicationModel syndicationModel)
        {
            Log.Information("RunSyndicate Triggered for {Customer} : {Environment} : {Username} : {SyndicationID}", customerSafeName, environmentSafeName, username, syndicationModel.Id);
            var longRunningJobManager = new LongRunningJobManager(customerSafeName, environmentSafeName);

            return longRunningJobManager.InitialiseRunSyndicationJob(username, syndicationModel);
        }

        [HttpPost]
        [Route("MassUpdate/{customerSafeName}/{environmentSafeName}/{username}")]
        public void MassUpdate(string customerSafeName, string environmentSafeName, string username, [FromBody]MassUpdateModel massUpdateModel)
        {
            var longRunningJobManager = new LongRunningJobManager(customerSafeName, environmentSafeName);
            longRunningJobManager.InitialiseMassUpdateJob(username, massUpdateModel);
        }

        [HttpPut]
        [Route("ContentStore/MassExclude/FieldType/{customerSafeName}/{environmentSafeName}/{username}")]
        public void ContentStoreMassExcludeFieldType(string customerSafeName, string environmentSafeName, string username, [FromBody] MassExcludeFieldTypeModel massExcludeFieldTypeModel)
        {
            var longRunningJobManager = new LongRunningJobManager(customerSafeName, environmentSafeName);
            var massExcludeTypeModel = new MassExcludeTypeModel
            {
                ExcludeType = ExcludeType.FieldType,
                ExcludeFieldTypeModel = massExcludeFieldTypeModel
            };
            var identifier = $"{ExcludeType.FieldType}-{massExcludeFieldTypeModel.EntityTypeId}-{massExcludeFieldTypeModel.FieldTypeId}";
            longRunningJobManager.InitialiseContentStoreMassExcludeTypes(username, identifier, massExcludeTypeModel);
        }

        [HttpPut]
        [Route("ContentStore/MassExclude/EntityType/{customerSafeName}/{environmentSafeName}/{username}/{entityTypeId}")]
        public void ContentStoreMassExcludeEntityType(string customerSafeName, string environmentSafeName, string username, string entityTypeId)
        {
            var longRunningJobManager = new LongRunningJobManager(customerSafeName, environmentSafeName);
            var massExcludeTypeModel = new MassExcludeTypeModel
            {
                ExcludeType = ExcludeType.EntityType,
                EntityTypeId = entityTypeId
            };
            var identifier = $"{ExcludeType.FieldType}-{entityTypeId}";
            longRunningJobManager.InitialiseContentStoreMassExcludeTypes(username, identifier, massExcludeTypeModel);
        }

        [HttpPut]
        [Route("ContentStore/MassExclude/LinkType/{customerSafeName}/{environmentSafeName}/{username}/{linkTypeId}")]
        public void ContentStoreMassExcludeLinkType(string customerSafeName, string environmentSafeName,
            string username, string linkTypeId)
        {
            var longRunningJobManager = new LongRunningJobManager(customerSafeName, environmentSafeName);
            var massExcludeTypeModel = new MassExcludeTypeModel
            {
                ExcludeType = ExcludeType.LinkType,
                LinkTypeId = linkTypeId
            };
            var identifier = $"{ExcludeType.FieldType}-{linkTypeId}";
            longRunningJobManager.InitialiseContentStoreMassExcludeTypes(username, identifier, massExcludeTypeModel);
        }

        [HttpPost]
        [Route("DeleteAllLinksForLinkType/{customerSafeName}/{environmentSafeName}/{username}")]
        public void DeleteAllLinksForLinkType(string customerSafeName, string environmentSafeName, string username, [FromBody]DeleteAllLinksForLinkTypeModel deleteAllLinksForLinkTypeModel)
        {
            var longRunningJobManager = new LongRunningJobManager(customerSafeName, environmentSafeName);
            longRunningJobManager.InitialiseDeleteAllLinksForLinkType(username, deleteAllLinksForLinkTypeModel);
        }

        [HttpPost]
        [Route("ApplyExpressions/{customerSafeName}/{environmentSafeName}/{username}")]
        public IActionResult ApplyExpressions(string customerSafeName, string environmentSafeName, string username, [FromBody] ApplyExpressionsModel model)
        {
            var longRunningJobManager = new LongRunningJobManager(customerSafeName, environmentSafeName);

            // Backward compatibility
            if (string.IsNullOrEmpty(model.Target))
            {
                if (!string.IsNullOrEmpty(model.FieldTypeId))
                {
                    model.Target = ExpressionTarget.FIELDTYPEID;
                }
                else if (!string.IsNullOrEmpty(model.FieldSetId))
                {
                    model.Target = ExpressionTarget.FIELDSETID;
                }
            }

            if (longRunningJobManager.InitialiseApplyExpressionsJob(username, model))
            {
                return this.Ok();
            }
            else
            {
                return this.Conflict();
            }
        }

        [HttpPost]
        [Route("WorkflowInspire/{customerSafeName}/{environmentSafeName}/{username}")]
        public IActionResult WorkflowInspire(string customerSafeName, string environmentSafeName, string username, [FromBody] WorkflowInspireModel model)
        {
            var longRunningJobManager = new LongRunningJobManager(customerSafeName, environmentSafeName);
            var id = longRunningJobManager.InitialiseWorkflowInspireJob(username, model);
            return this.Ok(id);
        }
    }
}
