﻿<?xml version="1.0" encoding="utf-8"?>
<ApplicationManifest xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" ApplicationTypeName="LongRunningJobType" ApplicationTypeVersion="1.0.0" xmlns="http://schemas.microsoft.com/2011/01/fabric">
  <Parameters>
    <Parameter Name="LongRunningJobQueueService_InstanceCount" DefaultValue="-1" />
    <Parameter Name="LongRunningJobQueueService_ASPNETCORE_ENVIRONMENT" DefaultValue="" />
    <Parameter Name="ASPNETCORE_ENVIRONMENT" DefaultValue="" />
    <Parameter Name="LongRunningJobWorkerService_ServicePlacementConstraints" DefaultValue="(NodeType==worker)" />
    <Parameter Name="LongRunningJobWorkerService_CpuCores" DefaultValue="2" />
    <Parameter Name="LongRunningJobService_InstanceCount" DefaultValue="-1" />
    <Parameter Name="LongRunningJobActorService_PartitionCount" DefaultValue="10" />
    <Parameter Name="LongRunningJobActorService_MinReplicaSetSize" DefaultValue="3" />
    <Parameter Name="LongRunningJobActorService_TargetReplicaSetSize" DefaultValue="3" />
    <Parameter Name="CompressionServiceUrl" DefaultValue="http://localhost:19081/Resource/CompressionService/" />
    <Parameter Name="KeyVaultBaseUrl" DefaultValue="" />
    <Parameter Name="KeyVaultServerSettingsEncryptionKeyName" DefaultValue="" />
    <Parameter Name="StackConfigSecretName" DefaultValue="StackConfig" />
    <Parameter Name="LogServiceEndpoint" DefaultValue="" />
    <Parameter Name="BackOfficeUrl" DefaultValue="" />
    <Parameter Name="InstrumentationKey" DefaultValue="" />
    <Parameter Name="RequiredHttps" DefaultValue="false" />
    <Parameter Name="ConnectServiceUrl" DefaultValue="http://localhost:19081/Connect/ConnectGatewayService/" />
    <Parameter Name="ChannelServiceUrl" DefaultValue="http://localhost:19081/Channel/DefaultChannelService/" />
    <Parameter Name="TokenServiceEndpoint" DefaultValue="http://localhost:19081/SystemToken/DefaultTokenService/" />
    <Parameter Name="DataApiUrl" DefaultValue="" />
    <Parameter Name="DataJobServiceUrl" DefaultValue="http://localhost.productmarketingcloud.com:19081/DataApi/DataJobService/" />
    <Parameter Name="ExpressionWorkerServiceUrl" DefaultValue="http://localhost:19081/ExpressionWorker/ExpressionWorkerService/" />
    <Parameter Name="InspireBackendUrl" DefaultValue="http://inspirelocal.productmarketingcloud.com:7071/" />
    <Parameter Name="MessagingServiceUrl" DefaultValue="http://localhost:19081/Messaging/MessagingService/" />
    <Parameter Name="SMTP_SEND_USER" DefaultValue="" />
    <Parameter Name="SMTP_SEND_USER_NAME" DefaultValue="" />
    <Parameter Name="ServicePlacementConstraintsNT3" DefaultValue="" />
    <Parameter Name="ServicePlacementConstraints" DefaultValue="" />
    <Parameter Name="PublicAssetServiceUrl" DefaultValue="" />
    <Parameter Name="BaseServerAddress" DefaultValue="" />
    <Parameter Name="TimerIntervallInMinutes" DefaultValue="10" />
    <Parameter Name="PeriodInHours" DefaultValue="" />
    <Parameter Name="GeoLocation" DefaultValue="localhost" />
    <Parameter Name="Stack" DefaultValue="localhost" />
    <Parameter Name="ServiceLocation" DefaultValue="" />
    <Parameter Name="WorkActorMaxCpuPercentage" DefaultValue="60" />
    <Parameter Name="AzureServicesAuthConnectionString" DefaultValue="" />
    <Parameter Name="AZURE_CLIENT_ID" DefaultValue="" />
    <Parameter Name="AZURE_TENANT_ID" DefaultValue="" />
    <Parameter Name="AZURE_CLIENT_SECRET" DefaultValue="" />
    <Parameter Name="LogLevel" DefaultValue="Information" />
    <Parameter Name="AugmentaApiBaseAddress" DefaultValue="http://localhost:5020/" />
    <Parameter Name="InriverApiOAuthAudience" DefaultValue="https://api.dev.inriver.io" />
    <Parameter Name="StackGroup" DefaultValue="dev" />
    <Parameter Name="OutputAdapterApiBaseAddress" DefaultValue="https://localhost:7100/" />
    <Parameter Name="Auth0Domain" DefaultValue="" />
  </Parameters>
  <!-- Import the ServiceManifest from the ServicePackage. The ServiceManifestName and ServiceManifestVersion
       should match the Name and Version attributes of the ServiceManifest element defined in the
       ServiceManifest.xml file. -->
  <ServiceManifestImport>
    <ServiceManifestRef ServiceManifestName="LongRunningJobQueueServicePkg" ServiceManifestVersion="1.0.0" />
    <ConfigOverrides>
      <ConfigOverride Name="Config">
        <Settings>
          <Section Name="Configuration">
            <Parameter Name="KeyVaultBaseUrl" Value="[KeyVaultBaseUrl]" />
            <Parameter Name="StackConfigSecretName" Value="[StackConfigSecretName]" />
            <Parameter Name="InstrumentationKey" Value="[InstrumentationKey]" />
            <Parameter Name="LogLevel" Value="[LogLevel]" />
          </Section>
        </Settings>
      </ConfigOverride>
    </ConfigOverrides>
    <EnvironmentOverrides CodePackageRef="code">
      <EnvironmentVariable Name="AzureServicesAuthConnectionString" Value="[AzureServicesAuthConnectionString]" />
      <EnvironmentVariable Name="AZURE_CLIENT_ID" Value="[AZURE_CLIENT_ID]" />
      <EnvironmentVariable Name="AZURE_TENANT_ID" Value="[AZURE_TENANT_ID]" />
      <EnvironmentVariable Name="AZURE_CLIENT_SECRET" Value="[AZURE_CLIENT_SECRET]" />
      <EnvironmentVariable Name="ASPNETCORE_ENVIRONMENT" Value="[ASPNETCORE_ENVIRONMENT]" />
    </EnvironmentOverrides>
  </ServiceManifestImport>
  <ServiceManifestImport>
    <ServiceManifestRef ServiceManifestName="LongRunningJobWorkerServicePkg" ServiceManifestVersion="1.0.0" />
    <ConfigOverrides>
      <ConfigOverride Name="Config">
        <Settings>
          <Section Name="Configuration">
            <Parameter Name="KeyVaultBaseUrl" Value="[KeyVaultBaseUrl]" />
            <Parameter Name="StackConfigSecretName" Value="[StackConfigSecretName]" />
            <Parameter Name="InstrumentationKey" Value="[InstrumentationKey]" />
            <Parameter Name="DataApiUrl" Value="[DataApiUrl]" />
            <Parameter Name="DataJobServiceUrl" Value="[DataJobServiceUrl]" />
            <Parameter Name="ExpressionWorkerServiceUrl" Value="[ExpressionWorkerServiceUrl]" />
            <Parameter Name="InspireBackendUrl" Value="[InspireBackendUrl]" />
            <Parameter Name="MessagingServiceUrl" Value="[MessagingServiceUrl]" />
            <Parameter Name="TokenServiceAddress" Value="[TokenServiceEndpoint]" />
            <Parameter Name="RequiredHttps" Value="[RequiredHttps]" />
            <Parameter Name="SmtpSendUser" Value="[SMTP_SEND_USER]" />
            <Parameter Name="SmtpSendUserName" Value="[SMTP_SEND_USER]" />
            <Parameter Name="LogLevel" Value="[LogLevel]" />
            <Parameter Name="Augmenta:ApiBaseAddress" Value="[AugmentaApiBaseAddress]" />
            <Parameter Name="OAuth:Audience" Value="[InriverApiOAuthAudience]" />
            <Parameter Name="OutputAdapter:ApiBaseAddress" Value="[OutputAdapterApiBaseAddress]" />
            <Parameter Name="Auth0Domain" Value="[Auth0Domain]" />
          </Section>
        </Settings>
      </ConfigOverride>
    </ConfigOverrides>
    <EnvironmentOverrides CodePackageRef="Code">
      <EnvironmentVariable Name="AzureServicesAuthConnectionString" Value="[AzureServicesAuthConnectionString]" />
    </EnvironmentOverrides>
    <Policies>
      <ServicePackageResourceGovernancePolicy CpuCores="[LongRunningJobWorkerService_CpuCores]" />
    </Policies>
  </ServiceManifestImport>
  <ServiceManifestImport>
    <ServiceManifestRef ServiceManifestName="LongRunningJobServicePkg" ServiceManifestVersion="1.0.0" />
    <ConfigOverrides>
      <ConfigOverride Name="Config">
        <Settings>
          <Section Name="Configuration">
            <Parameter Name="KeyVaultBaseUrl" Value="[KeyVaultBaseUrl]" />
            <Parameter Name="KeyVaultServerSettingsEncryptionKeyName" Value="[KeyVaultServerSettingsEncryptionKeyName]" />
            <Parameter Name="StackConfigSecretName" Value="[StackConfigSecretName]" />
            <Parameter Name="InstrumentationKey" Value="[InstrumentationKey]" />
            <Parameter Name="GeoLocation" Value="[GeoLocation]" />
            <Parameter Name="Stack" Value="[Stack]" />
            <Parameter Name="DataApiUrl" Value="[DataApiUrl]" />
            <Parameter Name="DataJobServiceUrl" Value="[DataJobServiceUrl]" />
            <Parameter Name="ExpressionWorkerServiceUrl" Value="[ExpressionWorkerServiceUrl]" />
            <Parameter Name="InspireBackendUrl" Value="[InspireBackendUrl]" />
            <Parameter Name="MessagingServiceUrl" Value="[MessagingServiceUrl]" />
            <Parameter Name="LongRunningJobWorkerService_ServicePlacementConstraints" Value="[LongRunningJobWorkerService_ServicePlacementConstraints]" />
            <Parameter Name="Augmenta:ApiBaseAddress" Value="[AugmentaApiBaseAddress]" />
            <Parameter Name="OAuth:Audience" Value="[InriverApiOAuthAudience]" />
            <Parameter Name="StackGroup" Value="[StackGroup]" />
            <Parameter Name="OutputAdapter:ApiBaseAddress" Value="[OutputAdapterApiBaseAddress]" />
            <Parameter Name="Auth0Domain" Value="[Auth0Domain]" />
          </Section>
        </Settings>
      </ConfigOverride>
    </ConfigOverrides>
    <EnvironmentOverrides CodePackageRef="Code">
      <EnvironmentVariable Name="AzureServicesAuthConnectionString" Value="[AzureServicesAuthConnectionString]" />
      <EnvironmentVariable Name="AZURE_CLIENT_ID" Value="[AZURE_CLIENT_ID]" />
      <EnvironmentVariable Name="AZURE_TENANT_ID" Value="[AZURE_TENANT_ID]" />
      <EnvironmentVariable Name="AZURE_CLIENT_SECRET" Value="[AZURE_CLIENT_SECRET]" />
      <EnvironmentVariable Name="ASPNETCORE_ENVIRONMENT" Value="[ASPNETCORE_ENVIRONMENT]" />
    </EnvironmentOverrides>
  </ServiceManifestImport>
  <ServiceManifestImport>
    <ServiceManifestRef ServiceManifestName="LongRunningJobActorPkg" ServiceManifestVersion="1.0.0" />
    <ConfigOverrides>
      <ConfigOverride Name="Config">
        <Settings>
          <Section Name="Configuration">
            <Parameter Name="KeyVaultBaseUrl" Value="[KeyVaultBaseUrl]" />
            <Parameter Name="KeyVaultServerSettingsEncryptionKeyName" Value="[KeyVaultServerSettingsEncryptionKeyName]" />
            <Parameter Name="StackConfigSecretName" Value="[StackConfigSecretName]" />
            <Parameter Name="InstrumentationKey" Value="[InstrumentationKey]" />
            <Parameter Name="GeoLocation" Value="[GeoLocation]" />
            <Parameter Name="Stack" Value="[Stack]" />
            <Parameter Name="DataApiUrl" Value="[DataApiUrl]" />
            <Parameter Name="DataJobServiceUrl" Value="[DataJobServiceUrl]" />
            <Parameter Name="ExpressionWorkerServiceUrl" Value="[ExpressionWorkerServiceUrl]" />
            <Parameter Name="InspireBackendUrl" Value="[InspireBackendUrl]" />
            <Parameter Name="MessagingServiceUrl" Value="[MessagingServiceUrl]" />
            <Parameter Name="ChannelServiceUrl" Value="[ChannelServiceUrl]" />
            <Parameter Name="ConnectServiceUrl" Value="[ConnectServiceUrl]" />
            <Parameter Name="RequiredHttps" Value="[RequiredHttps]" />
            <Parameter Name="TokenServiceAddress" Value="[TokenServiceEndpoint]" />
            <Parameter Name="SMTP_SEND_USER" Value="[SMTP_SEND_USER]" />
            <Parameter Name="SMTP_SEND_USER_NAME" Value="[SMTP_SEND_USER]" />
            <Parameter Name="CompressionServiceUrl" Value="[CompressionServiceUrl]" />
            <Parameter Name="Augmenta:ApiBaseAddress" Value="[AugmentaApiBaseAddress]" />
            <Parameter Name="OAuth:Audience" Value="[InriverApiOAuthAudience]" />
            <Parameter Name="StackGroup" Value="[StackGroup]" />
            <Parameter Name="OutputAdapter:ApiBaseAddress" Value="[OutputAdapterApiBaseAddress]" />
            <Parameter Name="Auth0Domain" Value="[Auth0Domain]" />
          </Section>
        </Settings>
      </ConfigOverride>
    </ConfigOverrides>
    <EnvironmentOverrides CodePackageRef="Code">
      <EnvironmentVariable Name="AzureServicesAuthConnectionString" Value="[AzureServicesAuthConnectionString]" />
      <EnvironmentVariable Name="AZURE_CLIENT_ID" Value="[AZURE_CLIENT_ID]" />
      <EnvironmentVariable Name="AZURE_TENANT_ID" Value="[AZURE_TENANT_ID]" />
      <EnvironmentVariable Name="AZURE_CLIENT_SECRET" Value="[AZURE_CLIENT_SECRET]" />
    </EnvironmentOverrides>
  </ServiceManifestImport>
  <DefaultServices>
    <!-- The section below creates instances of service types, when an instance of this
         application type is created. You can also create one or more instances of service type using the
         ServiceFabric PowerShell module.

         The attribute ServiceTypeName below must match the name defined in the imported ServiceManifest.xml file. -->
    <Service Name="LongRunningJobQueueService" ServicePackageActivationMode="ExclusiveProcess">
      <StatelessService ServiceTypeName="LongRunningJobQueueServiceType" InstanceCount="[LongRunningJobQueueService_InstanceCount]">
        <SingletonPartition />
        <PlacementConstraints>[ServicePlacementConstraints]</PlacementConstraints>
      </StatelessService>
    </Service>
    <Service Name="LongRunningJobService">
      <StatelessService ServiceTypeName="LongRunningJobServiceType" InstanceCount="[LongRunningJobService_InstanceCount]">
        <SingletonPartition />
        <PlacementConstraints>[ServicePlacementConstraints]</PlacementConstraints>
      </StatelessService>
    </Service>
    <Service Name="LongRunningJobActorService" GeneratedIdRef="7a6e292f-5c20-43e2-85d1-984afb669ab6|Persisted">
      <StatefulService ServiceTypeName="LongRunningJobActorServiceType" TargetReplicaSetSize="[LongRunningJobActorService_TargetReplicaSetSize]" MinReplicaSetSize="[LongRunningJobActorService_MinReplicaSetSize]">
        <UniformInt64Partition PartitionCount="[LongRunningJobActorService_PartitionCount]" LowKey="-9223372036854775808" HighKey="9223372036854775807" />
        <PlacementConstraints>[ServicePlacementConstraintsNT3]</PlacementConstraints>
      </StatefulService>
    </Service>
  </DefaultServices>
</ApplicationManifest>