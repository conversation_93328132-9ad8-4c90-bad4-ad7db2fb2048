namespace inRiver.Core.Models.inRiver.Workflow
{
    using System.Collections.Generic;
    using Newtonsoft.Json;

    public class InspireResponseModel
    {
        [JsonProperty("entity_id")]
        public int EntityId { get; set; }

        [JsonProperty("entity_type_id")]
        public string EntityTypeId { get; set; }

        [JsonProperty("field_type_id")]
        public string FieldTypeId { get; set; }

    }

    public class InspireGenerateResponseModel : InspireResponseModel
    {
        [JsonProperty("result")]
        public InspireGenerateResultModel Result { get; set; }

    }

    public class InspireGenerateResultModel
    {
        [JsonProperty("system")]
        public object System { get; set; }

        [JsonProperty("prompt")]
        public object Prompt { get; set; }

        [JsonProperty("results")]
        public List<InspireTextGenerationResultModel> Results { get; set; }

    }

    public class InspireTextGenerationResultModel
    {
        [JsonProperty("llm")]
        public string Llm { get; set; }

        [JsonProperty("generated_text")]
        public string GeneratedText { get; set; }

    }

    public class InspireTranslateResponseModel : InspireResponseModel
    {
        [JsonProperty("result")]
        public InspireTranslateResultModel Result { get; set; }

    }

    public class InspireTranslateResultModel
    {
        [JsonProperty("text")]
        public string Text { get; set; }

        [JsonProperty("results")]
        public List<InspireTextTranslationResultModel> Results { get; set; }

    }

    public class InspireTextTranslationResultModel
    {
        [JsonProperty("language")]
        public string Language { get; set; }

        [JsonProperty("translation")]
        public string Translation { get; set; }

        [JsonProperty("prompt")]
        public object Prompt { get; set; }
    }
}
