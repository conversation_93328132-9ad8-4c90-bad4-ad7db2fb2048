namespace LongRunningJobQueueService.Data.Repositories
{
    using System;
    using System.Data.SqlClient;
    using System.Threading.Tasks;
    using Dapper;
    using Models;
    using Serilog;
    using Services;

    public class EnvironmentConfigurationRepository : IEnvironmentConfigurationRepository
    {
        private readonly IStackSettingsService stackSettingsService;

        public EnvironmentConfigurationRepository(IStackSettingsService stackSettingsService)
        {
            this.stackSettingsService = stackSettingsService;
        }

        public async Task<EnvironmentConfiguration> GetEnvironmentConfigurationAsync(string customerSafeName, string environmentSaveName)
        {
            try
            {
                await using var connection = new SqlConnection(this.stackSettingsService.ReadonlyConfigConnectionString);

                return await connection.QueryFirstOrDefaultAsync<EnvironmentConfiguration>(@"
                        SELECT Environment.DbConnectionString FROM dbo.Customer 
                        INNER JOIN CustomerEnvironment ON Customer.Id = CustomerEnvironment.CustomerId 
                        INNER JOIN Environment ON CustomerEnvironment.EnvironmentId = Environment.Id 
                        WHERE Customer.Safename = @CustomerSafeName AND Environment.Safename = @EnvironmentSaveName
                    ", new { CustomerSafeName = customerSafeName, EnvironmentSaveName = environmentSaveName });
            }
            catch (Exception ex)
            {
                Log.Error(ex, "GetEnvironmentConfigurationAsync caught an unexpected exception");
                throw;
            }
        }
    }
}
