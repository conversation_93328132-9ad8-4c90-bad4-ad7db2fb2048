namespace LongRunningJobQueueService.Data.Extensions
{
    using Inriver.StackEssentials.Abstractions;
    using Microsoft.Extensions.DependencyInjection;
    using Repositories;
    using Services;

    public static class ServiceCollectionExtensions
    {
        public static IServiceCollection AddDataServices(this IServiceCollection services) =>
            services.AddScoped<IEnvironmentService, EnvironmentService>()
                .AddScoped<IStackSettingsService, StackSettingsService>(serviceProvider => {
                    var stackConfig = serviceProvider.GetRequiredService<IStackConfig>();

                    return new StackSettingsService(stackConfig.ReadOnlyConfigDatabaseConnectionString.UnScramble());
                })
                .AddScoped<ILongRunningJobService, LongRunningJobService>()
                .AddScoped<IEnvironmentConfigurationRepository, EnvironmentConfigurationRepository>()
                .AddScoped<ILongRunningJobRepository, LongRunningJobRepository>();
    }
}
