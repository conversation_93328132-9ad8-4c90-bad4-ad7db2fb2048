namespace LongRunningJobQueueService.AzureQueue.Interfaces
{
    using System;
    using System.Collections.Generic;
    using System.Threading;
    using System.Threading.Tasks;
    using Azure.Storage.Queues.Models;

    public interface IJobStatusQueueService
    {
        Task<bool> ExistsAsync(CancellationToken cancellationToken);

        Task<IEnumerable<QueueMessage>> ReceiveMessagesAsync(int maxMessages, TimeSpan visibilityTimeout, CancellationToken cancellationToken);

        Task DeleteMessageAsync(string messageId, string popReceipt, CancellationToken cancellationToken);
    }
}
