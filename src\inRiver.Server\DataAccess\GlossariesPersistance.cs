namespace inRiver.Server.DataAccess
{
    using System;
    using System.Collections.Generic;
    using System.Data.SqlClient;
    using System.Text;
    using inRiver.Core.Models.inRiver.Workflow;
    using Newtonsoft.Json;
    using System.Threading.Tasks;
    using Dapper;

    /// <summary>
    /// Partial implementation of inRiverPersistance handling glossary-related data operations.
    /// </summary>
    public partial class inRiverPersistance
    {
        public async Task<List<SegmentGlossaryMappingConfig>> GetGlossaryConfigFromInspireConfigurationAsync()
        {
            using var connection = new SqlConnection(this.ConnectionString);
            using var command = new SqlCommand("SELECT [Value] FROM [dbo].[InspireConfig] WHERE [ConfigType] = 'Inspire'", connection);

            await connection.OpenAsync();
            var result = await command.ExecuteScalarAsync();

            if (result == null || result == DBNull.Value)
            {
                return null;
            }

            var jsonString = result.ToString();
            var customerConfig = JsonConvert.DeserializeObject<CustomerConfig>(jsonString);

            return customerConfig?.TranslationGlossaryConfig;
        }

        public async Task<List<GlossaryMatchModel>> GetRelevantTermsBasicAsync(
            string description,
            string sourceLocale,
            string targetLocale,
            int glossaryId,
            int maxResults = 15)
        {
            await using var connection = new SqlConnection(this.ConnectionString);
            using var command = new SqlCommand(@"
                   WITH RelevantTerms AS (
                       SELECT DISTINCT
                           ge.Id as EntryId,
                           source.Text as SourceTerm,
                           target.Text as TargetTerm,
                           COALESCE(target.Context, '') as Context,
                           LEN(source.Text) as TermLength,
                           CASE
                               WHEN @description LIKE '%' + source.Text + '%' THEN 100
                               WHEN @description LIKE '% ' + source.Text + ' %' THEN 90
                               WHEN @description LIKE source.Text + ' %' THEN 85
                               WHEN @description LIKE '% ' + source.Text THEN 85
                               ELSE 50
                           END as RelevanceScore
                       FROM GlossaryEntries ge
                       JOIN GlossaryEntryTranslations source ON ge.Id = source.EntryId
                           AND source.Locale = @sourceLocale
                           AND source.GlossaryId = @glossaryId
                       JOIN GlossaryEntryTranslations target ON ge.Id = target.EntryId
                           AND target.Locale = @targetLocale
                           AND target.GlossaryId = @glossaryId
                       WHERE LOWER(@description) LIKE '%' + LOWER(source.Text) + '%'
                   )
                   SELECT TOP (@maxResults)
                       SourceTerm,
                       TargetTerm,
                       Context,
                       RelevanceScore
                   FROM RelevantTerms
                   WHERE RelevanceScore > 0
                   ORDER BY RelevanceScore DESC, TermLength DESC", connection);

            _ = command.Parameters.AddWithValue("@description", description);
            _ = command.Parameters.AddWithValue("@sourceLocale", sourceLocale);
            _ = command.Parameters.AddWithValue("@targetLocale", targetLocale);
            _ = command.Parameters.AddWithValue("@glossaryId", glossaryId);
            _ = command.Parameters.AddWithValue("@maxResults", maxResults);

            var results = new List<GlossaryMatchModel>();
            await connection.OpenAsync();
            await using var reader = await command.ExecuteReaderAsync();

            while (await reader.ReadAsync())
            {
                results.Add(new GlossaryMatchModel
                {
                    SourceTerm = reader.GetString(0),
                    TargetTerm = reader.GetString(1),
                    Context = reader.GetString(2),
                    RelevanceScore = reader.GetInt32(3)
                });
            }

            return results;
        }
    }
}
