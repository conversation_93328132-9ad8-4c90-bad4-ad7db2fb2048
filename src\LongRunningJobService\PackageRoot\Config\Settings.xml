<?xml version="1.0" encoding="utf-8" ?>
<Settings xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://schemas.microsoft.com/2011/01/fabric">
  <Section Name="Configuration">
    <Parameter Name="KeyVaultBaseUrl" Value="" />
    <Parameter Name="KeyVaultServerSettingsEncryptionKeyName" Value="" />
    <Parameter Name="StackConfigSecretName" Value="" />
    <Parameter Name="ConfigurationDatabaseConnectionString" Value="" />
    <Parameter Name="ReadOnlyConfigDatabaseConnectionString" Value="" />
    <Parameter Name="LogDatabaseConnectionString" Value="" />
    <Parameter Name="InstrumentationKey" Value="" />
    <Parameter Name="GeoLocation" Value="" />
    <Parameter Name="Stack" Value="" />
    <Parameter Name="DataApiUrl" Value=""/>
    <Parameter Name="DataJobServiceUrl" Value="" />
    <Parameter Name="ExpressionWorkerServiceUrl" Value=""/>
    <Parameter Name="InspireBackendUrl" Value="" />
    <Parameter Name="MessagingServiceUrl" Value=""/>
    <Parameter Name="RedisConnectionString" Value="" />
    <Parameter Name="LogLevel" Value="" />
    <Parameter Name="LongRunningJobWorkerService_ServicePlacementConstraints" Value=""/>
    <Parameter Name="Augmenta:ApiBaseAddress" Value="" />
    <Parameter Name="OAuth:Audience" Value="" />
    <Parameter Name="StackGroup" Value="" />
    <Parameter Name="OutputAdapter:ApiBaseAddress" Value="" />
    <Parameter Name="Auth0Domain" Value="" />
  </Section>
</Settings>
