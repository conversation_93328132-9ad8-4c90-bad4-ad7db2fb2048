namespace LongRunningJobActor
{
    using System;
    using System.Collections.Generic;
    using System.Threading.Tasks;
    using global::LongRunningJobActor.Models;
    using inRiver.Core.Constants.LongRunningJobs;
    using inRiver.Core.Repository;
    using LongRunningJob.Core.Cache;
    using Newtonsoft.Json;
    using Polly;

    internal class LongRunningJobActorHelper
    {
        private readonly LongRunningJobCache longRunningJobCache;
        private readonly JobRepository jobRepository;

        internal LongRunningJobActorHelper(JobRepository jobRepository, LongRunningJobCache jobCache)
        {
            if (jobRepository == null || jobCache == null)
            {
                throw new ArgumentException("jobRepository or jobCache can't be null");
            }

            this.jobRepository = jobRepository;
            this.longRunningJobCache = jobCache;
        }

        internal void UpdateLongRunningJobState(string actorId, int jobId, string state)
        {
            if (this.JobIsCancelled(actorId, jobId))
            {
                return;
            }

            this.jobRepository.UpdateLongRunningJobState(jobId, state);
        }

        internal void UpdateLongRunningJobStateAndMetadata(string actorId, int jobId, string state, string metadata)
        {
            try
            {
                var metaDataModel = JsonConvert.DeserializeObject<MetaDataModel>(metadata);
                if (metaDataModel != null && metaDataModel.FailedEntityIds.Length > 0)
                {
                    state = LongRunningJobsStatus.FinishedWithErrors;
                }
                else if (this.JobIsCancelled(actorId, jobId))
                {
                    state = LongRunningJobsStatus.Cancelled;
                }
            }
            finally
            {
                this.jobRepository.UpdateLongRunningJobStateAndMetadata(jobId, state, metadata);
            }
        }

        internal void UpdateSyndicationJobStateAndMetadata(string actorId, int jobId, string state, IDictionary<string, object> metadata)
        {
            state = this.JobIsCancelled(actorId, jobId) ? LongRunningJobsStatus.Cancelled : state;
            this.jobRepository.UpdateSyndicationJobStateAndMetadata(jobId, state, metadata);
        }

        internal Task UpdateLongRunningJobMetadataAsync(int jobId, string metadata)
            => this.jobRepository.UpdateLongRunningJobMetadataAsync(jobId, metadata);

        internal bool JobIsCancelled(string actorId, int jobId)
        {
            var databaseFallbackPolicy = Policy<bool>
                .Handle<Exception>()
                .Fallback(() => this.jobRepository.JobIsCancelled(jobId));

            var redisCacheRetryPolicy = Policy<bool>
                .Handle<Exception>()
                .WaitAndRetry(3, retryAttempt => TimeSpan.FromSeconds(retryAttempt));

            return databaseFallbackPolicy.Wrap(redisCacheRetryPolicy).Execute(() => this.longRunningJobCache.JobIsCancelled(actorId));
        }
    }
}
