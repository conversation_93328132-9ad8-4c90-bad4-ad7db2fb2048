namespace LongRunningJobQueueService.Data.Exceptions
{
    using System;

    public class EnvironmentConfigurationNotFoundException : Exception
    {
        public EnvironmentConfigurationNotFoundException()
            : base("Environment configuration was not found.")
        {
        }

        public EnvironmentConfigurationNotFoundException(string customerSafeName, string environmentSafeName)
            : base($"Environment configuration was not found for customer '{customerSafeName}' and environment '{environmentSafeName}'.")
        {
        }
    }
}
