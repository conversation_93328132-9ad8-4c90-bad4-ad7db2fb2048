namespace inRiver.Server.Repository
{
    using System;
    using System.Collections.Generic;
    using System.Text;
    using inRiver.Core.Models.inRiver.Workflow;
    using System.Threading.Tasks;
    using inRiver.Remoting.Security;
    using inRiver.Server.DataAccess;
    using inRiver.Server.Error;
    using inRiver.Server.Request;

    public class GlossariesRepository
    {
        private readonly IDataPersistance dataContext;

        private readonly RequestContext context;

        public GlossariesRepository(RequestContext context)
        {
            if (!context.UserHasPermission(UserPermission.View))
            {
                throw ErrorUtility.GetSecurityException("User does not have permission to view data in inRiver");
            }

            this.dataContext = context.DataPersistance;

            this.context = context;
        }

        public async Task<List<SegmentGlossaryMappingConfig>> GetGlossaryConfigFromInspireConfigurationAsync()
        {
            return await this.context.DataPersistance.GetGlossaryConfigFromInspireConfigurationAsync();
        }

        public async Task<List<GlossaryMatchModel>> GetRelevantTermsBasicAsync(
            string description,
            string sourceLocale,
            string targetLocale,
            int glossaryId,
            int maxResults = 15)
        {
            return await this.context.DataPersistance.GetRelevantTermsBasicAsync(
                description,
                sourceLocale,
                targetLocale,
                glossaryId,
                maxResults);
        }
    }
}
