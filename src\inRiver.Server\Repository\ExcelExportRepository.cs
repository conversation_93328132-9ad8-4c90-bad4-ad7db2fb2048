namespace inRiver.Server.Repository
{
    using System;
    using System.Collections.Generic;
    using System.Data;
    using System.Diagnostics.CodeAnalysis;
    using System.Globalization;
    using System.IO;
    using System.Linq;
    using System.Linq.Expressions;
    using System.Runtime.CompilerServices;
    using System.Text.RegularExpressions;
    using System.Threading;
    using System.Threading.Tasks;
    using Core.Util;
    using DocumentFormat.OpenXml;
    using DocumentFormat.OpenXml.Packaging;
    using DocumentFormat.OpenXml.Spreadsheet;
    using inriver.Expressions.Client.Constants;
    using inRiver.Core.Enum;
    using inRiver.Core.Models.inRiver;
    using inRiver.Core.Models.inRiver.ExcelExport;
    using inRiver.iPMC.Persistance;
    using inRiver.Remoting.Log;
    using inRiver.Remoting.Objects;
    using inRiver.Remoting.Security;
    using inRiver.Server.DataAccess;
    using inRiver.Server.Error;
    using inRiver.Server.ExcelExport;
    using inRiver.Server.Extension;
    using inRiver.Server.Managers;
    using inRiver.Server.Request;
    using inRiver.Server.Util;
    using inRiver.Server.Util.IllegalCharacters;
    using Inriver.Expressions.Dto;
    using Microsoft.Extensions.Azure;
    using Newtonsoft.Json;
    using Serilog;
    using Util.Workbook;
    using DataValidation = DocumentFormat.OpenXml.Spreadsheet.DataValidation;
    using DataValidations = DocumentFormat.OpenXml.Spreadsheet.DataValidations;
    using Entity = Remoting.Objects.Entity;
    using EntityType = Remoting.Objects.EntityType;
    using Field = Remoting.Objects.Field;
    using FieldType = Remoting.Objects.FieldType;
    using LoadLevel = Remoting.Objects.LoadLevel;
    using LocaleString = Remoting.Objects.LocaleString;
    using Sheet = DocumentFormat.OpenXml.Spreadsheet.Sheet;

    public class ExcelExportRepository : IExcelExportRepository
    {
        public const int DefaultExcelExportBatchSize = 100;

        private readonly IDataPersistance dataContext;
        private readonly RequestContext context;
        private readonly CloudBlobManager cloudBlobManager;
        private readonly CloudBlobManager cloudBlobManagerTemp;

        public ExcelExportRepository(RequestContext context)
        {
            if (!context.UserHasPermission(UserPermission.View))
            {
                throw ErrorUtility.GetSecurityException("User does not have permission to view data in inRiver");
            }

            this.dataContext = context.DataPersistance;
            this.context = context;
            this.cloudBlobManager = new CloudBlobManager(context, "excelexport");
            this.cloudBlobManagerTemp = new CloudBlobManager(context, "excelexportTemp");
        }

        public async Task<bool> BatchExportExcelAsync(ExcelExportModel excelExportModel,
            int batchSize,
            CancellationToken cancellationToken,
            Func<int, Task> onProgressChanged = null)
        {
            var languages = excelExportModel.Languages.Select(language => new CultureInfo(language)).ToList();
            var currentCultureInfo = !string.IsNullOrEmpty(excelExportModel.CurrentLanguage) ? new CultureInfo(excelExportModel.CurrentLanguage) : null;
            var dataRepository = new DataRepository(this.context);
            var entityType = this.GetExportMainEntityType(excelExportModel.EntityTypeModels);
            Dictionary<string, Dictionary<string, CVLValue>> cvlKeysDictionary = null;
            cancellationToken.ThrowIfCancellationRequested();

            if (excelExportModel.EntityTypeModels.Count == 1)
            {
                if (context.EntityModel == 1)
                {
                    this.context.Log(LogLevel.Information, "excel-Before Get Entities");

                    List<FieldType> fieldTypesToExport = entityType.FieldTypes.FindAll(
                          ft => excelExportModel.EntityTypeModels[0].FieldTypeIds.Contains(ft.Id)).OrderBy(f => f.Index).ToList();
                    var table = dataRepository.GetEntitiesAsDataTable(excelExportModel.EntityIds, entityType.Id, fieldTypesToExport, excelExportModel.SegmentationOption, cancellationToken);
                    this.context.Log(LogLevel.Information, "excel-After Get Entities");

                    FormatDataTable(table, entityType, languages, excelExportModel.IncludeExpressions);

                    FormatDataInTable(table, entityType, languages, currentCultureInfo, excelExportModel.CVLExportSetting, excelExportModel.AvailableCVLKeysSetting, excelExportModel.IncludeExpressions, cancellationToken);

                    cancellationToken.ThrowIfCancellationRequested();

                    cvlKeysDictionary = GetCvlIdCvlKeysDictionary(excelExportModel, fieldTypesToExport);

                    this.WriteToExcelFile(table, excelExportModel.Name, entityType, cvlKeysDictionary, excelExportModel.AvailableCVLKeysSetting, null, onProgressChanged);

                    this.context.Log(LogLevel.Information, "excel-After Wrote excel");
                    return table.HasErrors;
                }
                else
                {
                    this.context.Log(LogLevel.Information, "excel-Before Get Entities");

                    List<FieldType> fieldTypesToExport = entityType.FieldTypes.FindAll(
                        ft => excelExportModel.EntityTypeModels[0].FieldTypeIds.Contains(ft.Id)).OrderBy(f => f.Index).ToList();

                    using var table = this.CreateDataTable(entityType, fieldTypesToExport, languages, excelExportModel.SegmentationOption, excelExportModel.IncludeExpressions);

                    cvlKeysDictionary = GetCvlIdCvlKeysDictionary(excelExportModel, fieldTypesToExport);
                    using var dataTable = await GetEntitiesAsDataTableForOdlAsync(table, entityType, excelExportModel, batchSize, cancellationToken);
                    this.context.Log(LogLevel.Information, "excel-After Get Entities");

                    cancellationToken.ThrowIfCancellationRequested();

                    this.WriteToExcelFile(dataTable, excelExportModel.Name, entityType, cvlKeysDictionary, excelExportModel.AvailableCVLKeysSetting, null, onProgressChanged);
                    this.context.Log(LogLevel.Information, "excel-After Wrote excel");
                    return table.HasErrors;
                }
            }

            return CreateExcelExportDifferentTypes(excelExportModel, dataRepository, entityType, languages, currentCultureInfo, cancellationToken, onProgressChanged);
        }

        private async Task<DataTable> GetEntitiesAsDataTableForOdlAsync(DataTable table, EntityType entityType, ExcelExportModel excelExportModel, int batchSize, CancellationToken cancellationToken)
        {
            var languages = excelExportModel.Languages.Select(language => new CultureInfo(language)).ToList();
            var currentCultureInfo = !string.IsNullOrEmpty(excelExportModel.CurrentLanguage) ? new CultureInfo(excelExportModel.CurrentLanguage) : null;
            var dataRepository = new DataRepository(this.context);

            List<FieldType> fieldTypesToExport = entityType.FieldTypes.FindAll(
                ft => excelExportModel.EntityTypeModels[0].FieldTypeIds.Contains(ft.Id)).OrderBy(f => f.Index).ToList();

            string dataCacheFolderName = Guid.NewGuid().ToString();

            var iteration = 0;
            foreach (var ids in excelExportModel.EntityIds.SplitIntoChunks(batchSize))
            {
                var rootEntities = await dataRepository.GetEntitiesAsync(ids, LoadLevel.DataOnly, cancellationToken);
                this.AddRows(table, rootEntities, fieldTypesToExport, languages, currentCultureInfo, excelExportModel.CVLExportSetting, excelExportModel.AvailableCVLKeysSetting, excelExportModel.IncludeExpressions, cancellationToken);

                var serializedExportContainers = JsonConvert.SerializeObject(table, Formatting.None);
                await this.cloudBlobManagerTemp.UploadTextAsync($"{dataCacheFolderName}/{iteration++}-{batchSize}", serializedExportContainers);
                table.Rows.Clear();
            }

            return await GetDataTableFromStorage(dataCacheFolderName, table);
        }

        private async Task<DataTable> GetDataTableFromStorage(string dataCacheFolderName, DataTable table)
        {
            var blobs = this.cloudBlobManagerTemp.GetBlobs(dataCacheFolderName);
            foreach (var blob in blobs)
            {
                var blobName = blob.Name.Split('/').LastOrDefault();
                var tableJson = await this.cloudBlobManagerTemp.DownloadTextAsync($"{dataCacheFolderName}/{blobName}");
                if (table is null)
                {
                    table = JsonConvert.DeserializeObject<DataTable>(tableJson);
                }
                else
                {
                    table.Merge(JsonConvert.DeserializeObject<DataTable>(tableJson));
                }
            }

            return table;
        }

        public EntityType GetExportMainEntityType(List<ExcelExportEntityTypeModel> entityTypeModels)
        {
            foreach (ExcelExportEntityTypeModel model in entityTypeModels)
            {
                if (string.IsNullOrEmpty(model.LinkTypeId))
                {
                    return this.dataContext.GetEntityType(model.EntityTypeId);
                }
            }

            return null;
        }

        public void ExcelExportHistory(ExcelExportHistoryModel excelExportHistoryModel, string fileName, CancellationToken cancellationToken)
        {
            using (DataTable table = new DataTable())
            {
                PopulateTableColumns(table);
                PopulateDataHistoryToTableRows(table, excelExportHistoryModel, cancellationToken);

                this.WriteToExcelFile(table, fileName);
            }
        }

        private bool CreateExcelExportDifferentTypes(ExcelExportModel excelExportModel,
            DataRepository dataRepository,
            EntityType entityType,
            List<CultureInfo> languages,
            CultureInfo currentCultureInfo,
            CancellationToken cancellationToken,
            Func<int, Task> onProgressChanged = null
        )
        {
            var entityTypeModelByLinkDirection =
                excelExportModel.EntityTypeModels.ToLookup(
                    entityTypeModel => entityTypeModel.LinkDirection ?? string.Empty);

            var sortedEntityTypeModels =
                entityTypeModelByLinkDirection["inbound"].Union(entityTypeModelByLinkDirection[string.Empty])
                    .Union(entityTypeModelByLinkDirection["outbound"])
                    .ToList();

            var entityParents = this.GetRootEntityParents(excelExportModel.EntityIds, sortedEntityTypeModels, cancellationToken);

            List<int> entityIds = new List<int>();

            List<int> entitySolo = new List<int>();

            if (entityParents.Any())
            {
                // only get children that has parent
                foreach (var ids in entityParents.Values)
                {
                    entityIds.AddRange(ids);
                }

                entitySolo.AddRange(GetSoloEntities(excelExportModel.EntityIds, entityParents));

                entityIds = entityIds.Distinct().ToList();
            }
            else
            {
                entityIds = excelExportModel.EntityIds;
            }

            var entityChildren = this.GetRootEntityChildren(entityIds, sortedEntityTypeModels, cancellationToken);

            var allEntityIds = entityParents.Keys.ToList();

            foreach (var ids in entityParents.Values)
            {
                allEntityIds.AddRange(ids);
            }

            allEntityIds.AddRange(entityChildren.Keys.ToList());

            allEntityIds.AddRange(entitySolo);

            foreach (var ids in entityChildren.Values)
            {
                allEntityIds.AddRange(ids);
            }

            allEntityIds = allEntityIds.Distinct().ToList();

            var currentUserSegmentIds = this.context.ContentSegmentIdToPermissions == null
                ? IPMCServerPersistanceFactory.GetInstance(this.context)
                    .GetUserByUsername(excelExportModel.Username).GetSegments().Select(s => s.Id).ToArray()
                : this.context.ContentSegmentIdToPermissions.Keys.ToArray();

            var allEntities = dataRepository.GetEntities(allEntityIds, LoadLevel.DataOnly, cancellationToken)
                .Where(e => currentUserSegmentIds.Contains(e.Segment.Id)).ToList();

            var entities = allEntities.ToDictionary(e => e.Id, e => e);

            var fieldTypes =
                sortedEntityTypeModels.SelectMany(entityTypeModel => entityTypeModel.FieldTypeIds)
                    .Distinct()
                    .ToDictionary(
                        fieldTypeId => fieldTypeId,
                        fieldTypeId => this.dataContext.GetFieldType(fieldTypeId));


            var cvlIds = fieldTypes.Values.Where(field => field.DataType == DataType.CVL).Select(field => field.CVLId).ToList();

            // If we're getting CVL Values, need to cache the CVL values.  This is very important for custom CVLs since this can have a huge impact
            // on performance if we go out and get the value for each row/column.
            cancellationToken.ThrowIfCancellationRequested();
            var cvlValueDictionary = new Dictionary<String, Dictionary<String, CVLValue>>();
            if ((excelExportModel.CVLExportSetting != CVLExportSetting.CvlKey || excelExportModel.AvailableCVLKeysSetting == AvailableCVLKeysSetting.IncludeAvailableCVLKeysAndValues) && (cvlIds.Count > 0))
            {
                var modelRepository = new ModelRepository(this.context);
                cvlValueDictionary = cvlIds.Distinct().ToDictionary(
                    cvlId => cvlId,
                    cvlId => modelRepository.GetCVLValuesForCVL(cvlId).ToDictionary(cvlValue => cvlValue.Key));
            }

            var entityParentsNotPendingDelete = entityParents.Where(ep => entities.ContainsKey(ep.Key))
                .ToDictionary(ep => ep.Key, ep => ep.Value);

            var entityChildrenNotPendingDelete = entityChildren.Where(ep => entities.ContainsKey(ep.Key))
                .ToDictionary(ep => ep.Key, ep => ep.Value);

            var entitySoloNotPendingDelete = entitySolo.Where(ep => entities.ContainsKey(ep)).ToList();

            var dataTable = new DataTable(entityType.Id);
            AddColumns(excelExportModel, dataTable, fieldTypes.Values.ToList(), languages);
            this.AddRows(dataTable, entityParentsNotPendingDelete, entityChildrenNotPendingDelete, entitySoloNotPendingDelete, entities, fieldTypes, languages, currentCultureInfo, excelExportModel.CVLExportSetting, excelExportModel.AvailableCVLKeysSetting, cvlValueDictionary);

            var cvlKeysDictionary = GetCvlIdCvlKeysDictionary(excelExportModel, fieldTypes.Select(o => o.Value).ToList());

            cancellationToken.ThrowIfCancellationRequested();

            this.WriteToExcelFile(dataTable, excelExportModel.Name, entityType, cvlKeysDictionary, excelExportModel.AvailableCVLKeysSetting, fieldTypes, onProgressChanged);

            return dataTable.HasErrors;
        }

        private void FormatDataInTable(DataTable dataTable,
            EntityType entityType,
            List<CultureInfo> cultureInfos,
            CultureInfo currentCulture,
            string cvlExportSetting,
            string availableCvlKeysSetting,
            bool includeExpressions,
            CancellationToken cancellationToken)
        {
            var cVLColumns = new List<FieldType>();
            var dateColumns = new List<FieldType>();
            var localeColumns = new List<FieldType>();
            var expressionsColumns = new List<FieldType>();
            var cvls = new Dictionary<string, CVL>();
            var cvlIds = new List<string>();

            var modelRepository = new ModelRepository(this.context);

            foreach (var fieldType in entityType.FieldTypes)
            {
                cancellationToken.ThrowIfCancellationRequested();

                if ((fieldType.DataType == DataType.CVL) && (cvlExportSetting != CVLExportSetting.CvlKey || availableCvlKeysSetting == AvailableCVLKeysSetting.IncludeAvailableCVLKeysAndValues))
                {
                    cVLColumns.Add(fieldType);
                    if (!cvls.ContainsKey(fieldType.CVLId))
                    {
                        cvlIds.Add(fieldType.CVLId);
                        cvls.Add(fieldType.CVLId, modelRepository.GetCVL(fieldType.CVLId));
                    }
                }
                else if ((fieldType.DataType == DataType.LocaleString) && (currentCulture != null))
                    localeColumns.Add(fieldType);
                else if (fieldType.DataType == DataType.DateTime)
                    dateColumns.Add(fieldType);

                if (includeExpressions && fieldType.ExpressionSupport && dataTable.Columns.Contains(fieldType.Id))
                {
                    expressionsColumns.Add(fieldType);
                }
            }

            if(cvlExportSetting != CVLExportSetting.CvlKey)
            {
                InsertCVLColumns(cVLColumns, cvls, dataTable, cultureInfos, cvlExportSetting);
            }


            foreach (var dateColumn in dateColumns)
            {
                if (dataTable.Columns[dateColumn.Id] != null)
                {
                    DataColumn newcolumn = new DataColumn("temp" + dateColumn.Id, Type.GetType("System.String"));
                    dataTable.Columns.Add(newcolumn);
                }
            }

            // If we're getting CVL Values, need to cache the CVL values.  This is very important for custom CVLs since this can have a huge impact
            // on performance if we go out and get the value for each row/column.
            Dictionary<string, Dictionary<string, CVLValue>> cvlValueDictionary = null;
            if (cvlExportSetting != CVLExportSetting.CvlKey || availableCvlKeysSetting == AvailableCVLKeysSetting.IncludeAvailableCVLKeysAndValues)
            {
                cvlValueDictionary = cvlIds.Distinct().ToDictionary(
                        cvlId => cvlId,
                        cvlId => modelRepository.GetCVLValuesForCVL(cvlId).ToDictionary(cvlValue => cvlValue.Key));
            }

            foreach (DataRow row in dataTable.Rows)
            {
                cancellationToken.ThrowIfCancellationRequested();

                if (cvlExportSetting != CVLExportSetting.CvlKey)
                {
                    foreach (var cvlColumn in cVLColumns)
                    {
                        InsertColumnCVLValues(cvlColumn, cvls, dataTable, cultureInfos, cvlExportSetting, row, cvlValueDictionary);
                    }
                }
                else if (availableCvlKeysSetting == AvailableCVLKeysSetting.IncludeAvailableCVLKeysAndValues)
                {
                    foreach (var cvlColumn in cVLColumns)
                    {
                        InsertCVLValuesWithKeys(cvlColumn, cvls, dataTable, availableCvlKeysSetting, row, cvlValueDictionary);
                    }
                }

                foreach (var localeColumn in localeColumns)
                {
                    if (dataTable.Columns.Contains(localeColumn.Id) && !DBNull.Value.Equals(row[localeColumn.Id]))
                    {
                        foreach (var cultureInfo in cultureInfos)
                        {
                            var columnName = GetColumnNameByFieldType(localeColumn, cultureInfo);
                            if (dataTable.Columns.Contains(columnName))
                            {
                                var data = ((LocaleString)JsonConvert.DeserializeObject<LocaleString>(row[localeColumn.Id].ToString()))[cultureInfo];
                                row[columnName] = data;
                            }
                        }
                    }
                }

                if (expressionsColumns.Count > 0)
                {
                    if (int.TryParse(row["sys_id"].ToString(), out var entityId))
                    {
                        var expressions = this.dataContext.GetExpressionsForEntity(entityId);
                        foreach (var expressionColumn in expressionsColumns)
                        {
                            if (expressions[ExpressionTargetType.FIELDTYPEID].TryGetValue(expressionColumn.Id, out var expression))
                            {
                                row[$"{expressionColumn.Id}_Expression"] = $"'{expression.Data}";
                            }
                        }
                    }
                }

                foreach (var dateColumn in dateColumns)
                {
                    if (dataTable.Columns.Contains(dateColumn.Id) && !DBNull.Value.Equals(row[dateColumn.Id]))
                        row["temp" + dateColumn.Id] = ((DateTime)row[dateColumn.Id]).ToString(currentCulture);
                }


            }
            foreach (var dateColumn in dateColumns)
            {
                DataColumn column = dataTable.Columns[dateColumn.Id];
                DataColumn newcolumn = dataTable.Columns["temp" + dateColumn.Id];
                if (column != null)
                {
                    newcolumn.SetOrdinal(column.Ordinal);
                    dataTable.Columns.Remove(column.ColumnName);
                    newcolumn.ColumnName = column.ColumnName;
                }
            }
            foreach (var localeColumn in localeColumns)
            {
                if (dataTable.Columns.Contains(localeColumn.Id))
                    dataTable.Columns.Remove(localeColumn.Id);
            }
            RemoveColumnCVLValue(cVLColumns, cvlExportSetting, availableCvlKeysSetting, dataTable, modelRepository);
        }

        private Dictionary<string, Dictionary<string, CVLValue>> GetCvlIdCvlKeysDictionary(ExcelExportModel excelExportModel, List<FieldType> fieldTypes)
        {
            if(string.IsNullOrEmpty(excelExportModel.AvailableCVLKeysSetting))
            {
                return new Dictionary<string, Dictionary<string, CVLValue>>();
            }

            var modelRepository = new ModelRepository(this.context);

            return fieldTypes
                .Where(field => field.DataType == DataType.CVL)
                .Select(field => new { field.Id, field.CVLId })
                .ToDictionary(
                    o => o.Id,
                    o => modelRepository.GetCVLValuesForCVL(o.CVLId)
                        .ToDictionary(cvlValue => cvlValue.Key, cvlValue => cvlValue)
                );
        }

        private void FormatDataTable(DataTable dataTable, EntityType entityType, List<CultureInfo> cultureInfos, bool includeExpression)
        {

            int columnIndex = 0;
            var sortedFieldTypes = from i in entityType.FieldTypes orderby i.Index ascending select i;

            if (dataTable.Columns.Contains("sys_id"))
            {
                dataTable.Columns["sys_id"].SetOrdinal(columnIndex++);
                dataTable.Columns["sys_id"].ColumnName = "sys_id";
            }

            if (dataTable.Columns.Contains("sys_fieldset"))
            {
                if (entityType.FieldSets.Any())
                    dataTable.Columns["sys_fieldset"].SetOrdinal(columnIndex++);
                else
                    dataTable.Columns.Remove("sys_fieldset");
            }

            if (dataTable.Columns.Contains(ContentSegmentationEnum.ID.GetDescription()))
            {
                dataTable.Columns[ContentSegmentationEnum.ID.GetDescription()].SetOrdinal(columnIndex++);
                dataTable.Columns[ContentSegmentationEnum.ID.GetDescription()].ColumnName = ContentSegmentationEnum.ID.GetDescription();
            }

            if (dataTable.Columns.Contains(ContentSegmentationEnum.SegmentName.GetDescription()))
            {
                dataTable.Columns[ContentSegmentationEnum.SegmentName.GetDescription()].SetOrdinal(columnIndex++);
                dataTable.Columns[ContentSegmentationEnum.SegmentName.GetDescription()].ColumnName = ContentSegmentationEnum.SegmentName.GetDescription();
            }

            foreach (var fieldType in sortedFieldTypes)
            {
                if (dataTable.Columns.Contains(fieldType.Id))
                {
                    if (fieldType.DataType == DataType.LocaleString)
                    {
                        dataTable.Columns[fieldType.Id].SetOrdinal(columnIndex);
                        columnIndex++;
                        foreach (var cultureInfo in cultureInfos)
                        {
                            dataTable.Columns.Add(GetColumnNameByFieldType(fieldType, cultureInfo)).SetOrdinal(columnIndex);
                            columnIndex++;
                        }
                    }
                    else
                    {
                        dataTable.Columns[fieldType.Id].SetOrdinal(columnIndex);
                        columnIndex++;
                    }

                    if (includeExpression && fieldType.ExpressionSupport)
                    {
                        dataTable.Columns.Add($"{fieldType.Id}_Expression").SetOrdinal(columnIndex);
                        columnIndex++;
                    }
                }
            }
        }

        private bool SaveFile(string fileName, MemoryStream excelExport)
        {
            return this.cloudBlobManager.AddFile(fileName, excelExport.ToArray());
        }

        private DataTable CreateDataTable(EntityType entityType, List<FieldType> fieldTypes, List<CultureInfo> cultureInfos, ContentSegmentationEnum segmentationOption, bool includeExpression)
        {
            DataTable dataTable = new DataTable(entityType.Id);
            dataTable.Columns.Add("sys_id");

            if (entityType.FieldSets.Any())
            {
                dataTable.Columns.Add("sys_fieldset");
            }

            if (segmentationOption == ContentSegmentationEnum.SegmentationIdAndSegmentationName)
            {
                dataTable.Columns.Add(ContentSegmentationEnum.ID.GetDescription());
                dataTable.Columns.Add(ContentSegmentationEnum.SegmentName.GetDescription());
            }
            else
            {
                if (segmentationOption != ContentSegmentationEnum.None)
                    dataTable.Columns.Add(segmentationOption.GetDescription());
            }

            foreach (var fieldType in fieldTypes)
            {
                if (fieldType.DataType == DataType.LocaleString)
                {
                    foreach (var cultureInfo in cultureInfos)
                    {
                        dataTable.Columns.Add(GetColumnNameByFieldType(fieldType, cultureInfo));
                    }
                }
                else
                {
                    dataTable.Columns.Add(GetColumnNameByFieldType(fieldType));
                }

                if (includeExpression && fieldType.ExpressionSupport)
                {
                    dataTable.Columns.Add($"{fieldType.Id}_Expression");
                }
            }

            return dataTable;
        }

        private static string GetColumnNameByFieldType(FieldType fieldType, CultureInfo ci = null)
        {
            if (ci != null)
            {
                return fieldType.Id + "_" + ci.Name;
            }

            return fieldType.Id;
        }

        private void InsertColumnAtIndex(DataTable dataTable, string columnName, int index)
        {
            if (!dataTable.Columns.Contains(columnName))
            {
                dataTable.Columns.Add(columnName).SetOrdinal(index);
            }
        }

        private void AddRows(
            DataTable dataTable,
            List<Entity> entities,
            List<FieldType> fieldTypes,
            List<CultureInfo> cultureInfos,
            CultureInfo currentCultureInfo,
            string cvlExportSetting,
            string availableCVLKeySetting,
            bool includeExpressions,
            CancellationToken cancellationToken)
        {

            Dictionary<string, Dictionary<string, CVLValue>> cvlValueDictionary = null;
            if (cvlExportSetting != CVLExportSetting.CvlKey || availableCVLKeySetting == AvailableCVLKeysSetting.IncludeAvailableCVLKeysAndValues)
            {
                var modelRepository = new ModelRepository(this.context);
                var cvlIds = fieldTypes.Where(field => field.DataType == DataType.CVL).Select(field => field.CVLId).ToList();
                cvlValueDictionary = cvlIds.Distinct().ToDictionary(
                        cvlId => cvlId,
                        cvlId => modelRepository.GetCVLValuesForCVL(cvlId).ToDictionary(cvlValue => cvlValue.Key));
            }

            foreach (var entity in entities)
            {
                cancellationToken.ThrowIfCancellationRequested();
                try
                {
                    var row = dataTable.NewRow();

                    row["sys_id"] = entity.Id.ToString(CultureInfo.InvariantCulture);

                    if (!string.IsNullOrEmpty(entity.FieldSetId) && dataTable.Columns.Contains("sys_fieldset"))
                    {
                        row["sys_fieldset"] = entity.FieldSetId;
                    }

                    var expressions = new Dictionary<string, Dictionary<string, DtoExpression>>();
                    if (includeExpressions)
                    {
                        expressions = this.dataContext.GetExpressionsForEntity(entity.Id);
                    }

                    foreach (var field in entity.Fields)
                    {
                        try
                        {
                            if (field is null)
                            {
                                continue;
                            }

                            if (!fieldTypes.Exists(ft => ft.Id.Equals(field.FieldType.Id, StringComparison.InvariantCultureIgnoreCase)))
                            {
                                continue;
                            }

                            if (includeExpressions && field.FieldType.ExpressionSupport)
                            {
                                if (expressions[ExpressionTargetType.FIELDTYPEID].TryGetValue(field.FieldType.Id, out var expression))
                                {
                                    row[$"{field.FieldType.Id}_Expression"] = $"'{expression.Data}";
                                }
                            }

                            if (field.Data is null)
                            {
                                continue;
                            }

                            if (field.FieldType.DataType == DataType.LocaleString)
                            {
                                foreach (var cultureInfo in cultureInfos)
                                {
                                    var data = ((LocaleString)field.Data)[cultureInfo];

                                    row[GetColumnNameByFieldType(field.FieldType, cultureInfo)] = data;
                                }
                            }
                            else
                            {
                                if (field.FieldType.DataType == DataType.DateTime && currentCultureInfo != null)
                                {
                                    row[GetColumnNameByFieldType(field.FieldType)] = ((DateTime)field.Data).ToString(currentCultureInfo);
                                }
                                else if (field.FieldType.DataType == DataType.CVL)
                                {
                                    if(cvlExportSetting != CVLExportSetting.CvlKey)
                                    {
                                        SetCvlFieldData(dataTable, row, field, cvlExportSetting, cultureInfos, cvlValueDictionary);
                                    }
                                    else if (availableCVLKeySetting == AvailableCVLKeysSetting.IncludeAvailableCVLKeysAndValues && !field.FieldType.Multivalue)
                                    {
                                        InsertCVLValuesWithKeys(field.FieldType, field, dataTable, availableCVLKeySetting, row, cvlValueDictionary);
                                    }
                                    else
                                    {
                                        row[GetColumnNameByFieldType(field.FieldType)] = field.Data;
                                    }
                                }
                                else
                                {
                                    row[GetColumnNameByFieldType(field.FieldType)] = field.Data;
                                }
                            }
                        }
                        catch (Exception e)
                        {
                            this.context.Log(LogLevel.Error, $"An error occurred during the Excel export process. {e}");
                            throw;
                        }
                    }

                    if (dataTable.Columns.Contains(ContentSegmentationEnum.ID.GetDescription()))
                    {
                        row[ContentSegmentationEnum.ID.GetDescription()] = entity.Segment.Id;
                    }
                    if (!string.IsNullOrEmpty(entity.Segment.Name) && dataTable.Columns.Contains(ContentSegmentationEnum.SegmentName.GetDescription()))
                    {
                        row[ContentSegmentationEnum.SegmentName.GetDescription()] = entity.Segment.Name;
                    }
                    if (!string.IsNullOrEmpty(entity.Segment.Description) && dataTable.Columns.Contains(ContentSegmentationEnum.SegmentDescription.GetDescription()))
                    {
                        row[ContentSegmentationEnum.SegmentDescription.GetDescription()] = entity.Segment.Description;
                    }
                    dataTable.Rows.Add(row);
                }
                catch (Exception e)
                {
                    this.context.Log(LogLevel.Error, $"An error occurred during the Excel export process. {e}");
                    throw;
                }
            }
        }

        private void AddRows(
            DataTable dataTable,
            Dictionary<int, List<int>> parentEntitites,
            Dictionary<int, List<int>> childEntitites,
            List<int> soloEntities,
            Dictionary<int, Entity> entities,
            Dictionary<string, FieldType> fieldTypeIds,
            List<CultureInfo> cultureInfos,
            CultureInfo currentCultureInfo,
            string cvlExportSetting,
            string availableCVLKeySetting,
            Dictionary<string, Dictionary<string, CVLValue>> cvlValueDictionary
            )
        {

            if (parentEntitites.Any())
            {
                foreach (var parent in parentEntitites)
                {
                    Entity entity = entities[parent.Key];

                    var row = this.AddRowData(null, entity, dataTable, fieldTypeIds.Keys.ToList(), cultureInfos, currentCultureInfo, cvlExportSetting, availableCVLKeySetting, cvlValueDictionary);

                    foreach (int childId in parent.Value)
                    {
                        Entity childEntity = entities[childId];

                        var row2 = this.AddRowData(row, childEntity, dataTable, fieldTypeIds.Keys.ToList(), cultureInfos, currentCultureInfo, cvlExportSetting, availableCVLKeySetting, cvlValueDictionary);

                        if (childEntitites.ContainsKey(childEntity.Id))
                        {
                            foreach (int i in childEntitites[childEntity.Id])
                            {
                                Entity grandChildEntity = entities[i];

                                var row3 = this.AddRowData(row2, grandChildEntity, dataTable, fieldTypeIds.Keys.ToList(), cultureInfos, currentCultureInfo, cvlExportSetting, availableCVLKeySetting, cvlValueDictionary);
                            }
                        }
                    }
                }

                foreach (var soloEntityId in soloEntities)
                {
                    Entity soloEntity = entities[soloEntityId];

                    var row = this.AddRowData(null, soloEntity, dataTable, fieldTypeIds.Keys.ToList(), cultureInfos, currentCultureInfo, cvlExportSetting, availableCVLKeySetting, cvlValueDictionary);

                    if (childEntitites.ContainsKey(soloEntity.Id))
                    {
                        foreach (int i in childEntitites[soloEntity.Id])
                        {
                            Entity grandChildEntity = entities[i];

                            var row2 = this.AddRowData(row, grandChildEntity, dataTable, fieldTypeIds.Keys.ToList(), cultureInfos, currentCultureInfo, cvlExportSetting, availableCVLKeySetting, cvlValueDictionary);
                        }
                    }
                }
            }
            else
            {
                foreach (var parent in childEntitites)
                {
                    Entity entity = entities[parent.Key];

                    var row = this.AddRowData(null, entity, dataTable, fieldTypeIds.Keys.ToList(), cultureInfos, currentCultureInfo, cvlExportSetting, availableCVLKeySetting, cvlValueDictionary);

                    foreach (int childId in parent.Value)
                    {
                        Entity childEntity;
                        entities.TryGetValue(childId, out childEntity);
                        if (childEntity != null)
                            this.AddRowData(row, childEntity, dataTable, fieldTypeIds.Keys.ToList(), cultureInfos, currentCultureInfo, cvlExportSetting, availableCVLKeySetting, cvlValueDictionary);
                    }
                }
            }
        }

        private DataRow AddRowData(DataRow parentRow, Entity entity, DataTable dataTable, List<string> fieldTypes, List<CultureInfo> cultureInfos, CultureInfo currentCultureInfo, string cvlExportSetting, string availableCvlKeysSetting, Dictionary<string, Dictionary<string, CVLValue>> cvlValueDictionary)
        {
            var row = dataTable.NewRow();

            if (parentRow != null)
            {
                row.ItemArray = (object[])parentRow.ItemArray.Clone();
            }

            row["sys_id"] = entity.Id.ToString(CultureInfo.InvariantCulture);

            if (dataTable.Columns.Contains("sys_entitytype"))
            {
                row["sys_entitytype"] = entity.EntityType.Id;
            }

            if (dataTable.Columns.Contains("sys_fieldset"))
            {
                row["sys_fieldset"] = entity.FieldSetId;
            }

            if (dataTable.Columns.Contains(ContentSegmentationEnum.ID.GetDescription()))
            {
                row[ContentSegmentationEnum.ID.GetDescription()] = entity.Segment.Id;
            }
            if (!string.IsNullOrEmpty(entity.Segment.Name) && dataTable.Columns.Contains(ContentSegmentationEnum.SegmentName.GetDescription()))
            {
                row[ContentSegmentationEnum.SegmentName.GetDescription()] = entity.Segment.Name;
            }
            if (!string.IsNullOrEmpty(entity.Segment.Description) && dataTable.Columns.Contains(ContentSegmentationEnum.SegmentDescription.GetDescription()))
            {
                row[ContentSegmentationEnum.SegmentDescription.GetDescription()] = entity.Segment.Description;
            }

            foreach (var field in
                entity.Fields.Where(field => fieldTypes.Contains(field.FieldType.Id))
                    .Where(field => field.Data != null))
            {
                if (field.FieldType.DataType == DataType.LocaleString)
                {
                    foreach (var cultureInfo in cultureInfos)
                    {
                        var data = ((LocaleString)field.Data)[cultureInfo];
                        row[GetColumnNameByFieldType(field.FieldType, cultureInfo)] = data;
                    }
                }
                else
                {
                    if (field.FieldType.DataType == DataType.DateTime && currentCultureInfo != null)
                    {
                        row[GetColumnNameByFieldType(field.FieldType)] = ((DateTime)field.Data).ToString(currentCultureInfo);
                    }
                    else if (field.FieldType.DataType == DataType.CVL)
                    {
                        if (cvlExportSetting != CVLExportSetting.CvlKey)
                        {
                            SetCvlFieldData(dataTable, row, field, cvlExportSetting, cultureInfos, cvlValueDictionary);
                        }
                        else if (availableCvlKeysSetting == AvailableCVLKeysSetting.IncludeAvailableCVLKeysAndValues && !field.FieldType.Multivalue)
                        {
                            InsertCVLValuesWithKeys(field.FieldType, field, dataTable, availableCvlKeysSetting, row, cvlValueDictionary);
                        }
                        else
                        {
                            row[GetColumnNameByFieldType(field.FieldType)] = field.Data;
                        }
                    }
                    else
                    {
                        row[GetColumnNameByFieldType(field.FieldType)] = field.Data;
                    }
                }
            }

            dataTable.Rows.Add(row);

            return row;
        }

        private void SetCvlFieldData(DataTable dataTable, DataRow row, Field field, string cvlExportSetting, List<CultureInfo> cultureInfos, Dictionary<string, Dictionary<string, CVLValue>> cvlValueDictionary)
        {
            var modelRepository = new ModelRepository(this.context);
            object fieldData;

            var cvl = modelRepository.GetCVL(field.FieldType.CVLId);
            var cvlKey = field.Data.ToString();

            var keys = cvlKey.Split(';');

            if (cvlExportSetting == CVLExportSetting.CvlKeyAndValue)
            {
                row[GetColumnNameByFieldType(field.FieldType)] = cvlKey;
            }

            if (cvl.DataType == DataType.LocaleString)
            {
                var index = dataTable.Columns.IndexOf(field.FieldType.Id);

                foreach (var cultureInfo in cultureInfos)
                {

                    var valueList = new List<string>();
                    foreach (var key in keys)
                    {
                        if (this.CvlKeyIsValid(cvlValueDictionary, cvl.Id, key, valueList, row))
                        {
                            valueList.Add(((LocaleString)cvlValueDictionary[cvl.Id][key].Value)[cultureInfo]);
                        }
                    }

                    var columnName = GetColumnNameByFieldType(field.FieldType, cultureInfo);

                    if (dataTable.Columns.IndexOf(columnName) == -1)
                    {
                        InsertColumnAtIndex(dataTable, columnName, ++index);
                    }

                    fieldData = string.Join(";", valueList.ToArray());

                    row[columnName] = fieldData;
                }

                if (cvlExportSetting == CVLExportSetting.CvlValue && dataTable.Columns.Contains(field.FieldType.Id))
                {
                    dataTable.Columns.Remove(field.FieldType.Id);
                }
            }
            else
            {
                if (cvlExportSetting == CVLExportSetting.CvlKeyAndValue)
                {
                    var index = dataTable.Columns.IndexOf(field.FieldType.Id);
                    var columnName = $"{field.FieldType.Id} (values)";

                    InsertColumnAtIndex(dataTable, columnName, index + 1);
                }
                var valueList = new List<string>();
                foreach (var key in keys)
                {
                    if (this.CvlKeyIsValid(cvlValueDictionary, cvl.Id, key, valueList, row))
                    {
                        valueList.Add(cvlValueDictionary[cvl.Id][key].Value?.ToString());
                    }
                }

                fieldData = string.Join(";", valueList.ToArray());

                var col = cvlExportSetting == CVLExportSetting.CvlKeyAndValue
                    ? $"{field.FieldType.Id} (values)"
                    : field.FieldType.Id;

                row[col] = fieldData;
            }
        }

        private static void AddColumns(
            ExcelExportModel excelExportModel,
            DataTable dataTable,
            List<FieldType> fieldTypes,
            List<CultureInfo> cultureInfos)
        {
            _ = dataTable.Columns.Add("sys_id");

            if (excelExportModel.EntityTypeModels.Count > 1)
            {
                _ = dataTable.Columns.Add("sys_entitytype");
            }

            if (
                excelExportModel.EntityTypeModels.Any(
                    entityTypeModel =>
                    entityTypeModel.FieldSetIds != null
                    && entityTypeModel.FieldSetIds.Any(fieldSetId => fieldSetId != null)))
            {
                _ = dataTable.Columns.Add("sys_fieldset");
            }

            if (excelExportModel.SegmentationOption == ContentSegmentationEnum.SegmentationIdAndSegmentationName)
            {
                _ = dataTable.Columns.Add(ContentSegmentationEnum.ID.GetDescription());
                _ = dataTable.Columns.Add(ContentSegmentationEnum.SegmentName.GetDescription());
            }
            else
            {
                if (excelExportModel.SegmentationOption != ContentSegmentationEnum.None)
                {
                    _ = dataTable.Columns.Add(excelExportModel.SegmentationOption.GetDescription());
                }
            }

            foreach (var fieldType in fieldTypes)
            {
                if (fieldType.DataType == DataType.LocaleString)
                {
                    foreach (var cultureInfo in cultureInfos)
                    {
                        _ = dataTable.Columns.Add(GetColumnNameByFieldType(fieldType, cultureInfo));
                    }
                }
                else if (fieldType.DataType == DataType.Double)
                {
                    _ = dataTable.Columns.Add(GetColumnNameByFieldType(fieldType), typeof(double));
                }
                else if (fieldType.DataType == DataType.Integer)
                {
                    _ = dataTable.Columns.Add(GetColumnNameByFieldType(fieldType), typeof(int));
                }
                else
                {
                    _ = dataTable.Columns.Add(GetColumnNameByFieldType(fieldType));
                }
            }
        }

        private void WriteToExcelFile(DataTable dataTable,
            string fileName,
            EntityType entityType = null,
            Dictionary<string, Dictionary<string, CVLValue>> cvlKeys = null,
            string availableKeysSetting = "",
            Dictionary<string, FieldType> fieldTypes = null,
            Func<int, Task> onProgressChanged = null
            )
        {
            var memoryStream = new MemoryStream();

            using var workBook = SpreadsheetDocument.Create(memoryStream, SpreadsheetDocumentType.Workbook);
            var workBookPart = workBook.AddWorkbookPart();
            workBookPart.Workbook = new Workbook();

            var worksheetPart = workBookPart.AddNewPart<WorksheetPart>();
            var sheetData = new SheetData();
            var worksheet = new Worksheet(sheetData);
            worksheetPart.Worksheet = worksheet;

            var sheets = workBook.WorkbookPart.Workbook.AppendChild(new Sheets());

            var sheetName = entityType is null ? "Import history" : this.GetWorksheetName(entityType.Id, entityType.Name);
            var sheet = new Sheet()
            {
                Id = workBook.WorkbookPart.GetIdOfPart(worksheetPart),
                SheetId = 1,
                Name = sheetName
            };

            sheets.Append(sheet);

            ImportTableHeader(dataTable, sheetData);

            // Add styles for data error fields highlighting
            if (dataTable.HasErrors)
            {
                WorkbookStyler.AddStyleSheet(workBook);
            }

            var resultErrors = PopulateSheetData(dataTable, sheetData, entityType, fieldTypes, onProgressChanged);

            if (resultErrors.Any())
            {
                this.LogExcelExportErrors(resultErrors, fileName);
            }

            this.PopulateCvlKeySheetsAndDataValidation(dataTable, cvlKeys, workBookPart, worksheet, sheets, availableKeysSetting);

            worksheetPart.Worksheet.Save();

            workBook.Close();

            _ = memoryStream.Seek(0, SeekOrigin.Begin);

            _ = this.SaveFile(fileName, memoryStream);
        }

        private void PopulateCvlKeySheetsAndDataValidation(DataTable dataTable, Dictionary<string, Dictionary<string, CVLValue>> cvlKeys, WorkbookPart workBookPart, Worksheet worksheet, Sheets sheets, string availableKeysSetting)
        {
            if (cvlKeys != null && cvlKeys.Any() && (availableKeysSetting == AvailableCVLKeysSetting.IncludeAvailableCVLKeys || availableKeysSetting == AvailableCVLKeysSetting.IncludeAvailableCVLKeysAndValues))
            {
                foreach (var keyValue in cvlKeys)
                {
                    this.AddCvlKeysReferenceSheet(workBookPart, sheets, keyValue, availableKeysSetting);

                    var column = dataTable.Columns[keyValue.Key];
                    if (column != null)
                    {
                        var dataValidation = CreateDataValidation(dataTable, keyValue, column, availableKeysSetting);
                        AppendDataValidation(worksheet, dataValidation);
                    }

                }
            }
        }

        private static DataValidation CreateDataValidation(DataTable dataTable, KeyValuePair<string, Dictionary<string, CVLValue>> keyValue, DataColumn column, string availableKeysSetting)
        {
            var columnName = GetColumnName(dataTable.Columns.IndexOf(column) + 1);
            var finalRowNumber = dataTable.Rows.Count + 1;
            var formula = availableKeysSetting == AvailableCVLKeysSetting.IncludeAvailableCVLKeysAndValues
                ? new Formula1($"'{keyValue.Key}'!$C$1:$C${keyValue.Value.Count() + 1}")
                : new Formula1($"'{keyValue.Key}'!$A$1:$A${keyValue.Value.Count() + 1}");
            DataValidation dataValidation = new DataValidation
            {
                Type = DataValidationValues.List,
                AllowBlank = true,
                SequenceOfReferences = new ListValue<StringValue>() { InnerText = $"{columnName}2:{columnName}{finalRowNumber}" },
                Formula1 = formula
            };
            return dataValidation;
        }

        private static void AppendDataValidation(Worksheet worksheet, DataValidation dataValidation)
        {
            var cvlKeysDataValidations = worksheet.GetFirstChild<DataValidations>();
            if (cvlKeysDataValidations != null)
            {
                cvlKeysDataValidations.Count = cvlKeysDataValidations.Count + 1;
                cvlKeysDataValidations.Append(dataValidation);
            }
            else
            {
                cvlKeysDataValidations = new DataValidations();
                cvlKeysDataValidations.Append(dataValidation);
                cvlKeysDataValidations.Count = 1;
                worksheet.Append(cvlKeysDataValidations);
            }
        }

        private void AddCvlKeysReferenceSheet(WorkbookPart workbookPart, Sheets sheets, KeyValuePair<string, Dictionary<string, CVLValue>> cvlDictionary, string availableKeysSetting)
        {
            var worksheetPart = workbookPart.AddNewPart<WorksheetPart>();
            var sheetData = new SheetData();
            var worksheet = new Worksheet(sheetData);
            worksheetPart.Worksheet = worksheet;

            var sheet = new Sheet()
            {
                Id = workbookPart.GetIdOfPart(worksheetPart),
                SheetId = Convert.ToUInt32(sheets.Count() + 1),
                Name = cvlDictionary.Key
            };

            int rowIndex = 1;

            foreach (var cvl in cvlDictionary.Value)
            {
                var row = new Row();
                var cell = new Cell()
                {
                    CellValue = string.IsNullOrEmpty(cvl.Key) ? null : new CellValue(cvl.Key),
                    DataType = CellValues.String,
                };

                row.Append(cell);

                if (availableKeysSetting == AvailableCVLKeysSetting.IncludeAvailableCVLKeysAndValues)
                {
                    var cell2 = new Cell()
                    {
                        CellValue = new CellValue(GetCVLValueForLanguage(cvl.Value)),
                        DataType = CellValues.String,
                    };

                    row.Append(cell2);

                    var cell3 = new Cell()
                    {
                        CellFormula = new CellFormula("A" + rowIndex + " & \" | \" & B" + rowIndex),
                        DataType = CellValues.String,
                    };

                    row.Append(cell3);
                    rowIndex++;
                }

                sheetData.Append(row);
            }

            sheets.Append(sheet);
            worksheet.Save();
        }

        private static void ImportTableHeader(DataTable dataTable, OpenXmlElement sheetData)
        {
            var header = new Row();

            var headerRowNumber = 1;

            foreach (DataColumn column in dataTable.Columns)
            {
                var headerColumnNumber = dataTable.Columns.IndexOf(column) + 1;

                var headerCell = CreateTextCell(headerColumnNumber, headerRowNumber, column.ColumnName);

                header.AppendChild(headerCell);
            }

            sheetData.AppendChild(header);
        }

        private static IReadOnlyCollection<ExcelExportException> PopulateSheetData(
            DataTable dataTable,
            OpenXmlElement sheetData,
            EntityType entityType = null,
            Dictionary<string, FieldType> fieldTypes = null,
            Func<int, Task> onProgressChanged = null)
        {
            var columnFieldTypes = new List<string>();
            if (fieldTypes != null)
            {
                for (var i = 0; i < dataTable.Columns.Count; i++)
                {
                    _ = fieldTypes.TryGetValue(dataTable.Columns[i].ColumnName, out var fieldType);
                    columnFieldTypes.Add(fieldType?.DataType);
                }
            }
            else if (entityType != null)
            {
                var fieldTypesDictionary = entityType.FieldTypes.ToDictionary(ft => ft.Id);
                for (var i = 0; i < dataTable.Columns.Count; i++)
                {
                    _ = fieldTypesDictionary.TryGetValue(dataTable.Columns[i].ColumnName, out var fieldType);
                    columnFieldTypes.Add(fieldType?.DataType);
                }
            }
            else
            {
                // Treat all fields as strings if exact field types are unknown
                columnFieldTypes.AddRange(Enumerable.Repeat(nameof(DataType.String), dataTable.Columns.Count));
            }

            return ParseDataTableIntoSheetData(dataTable, columnFieldTypes, sheetData, onProgressChanged);
        }

        private static IReadOnlyCollection<ExcelExportException> ParseDataTableIntoSheetData(
            DataTable dataTable,
            IReadOnlyList<string> columnFieldTypes,
            OpenXmlElement sheetData,
            Func<int, Task> onProgressChanged = null)
        {
            var hasErrors = dataTable.HasErrors;
            var resultErrors = new Dictionary<string, ExcelExportException>();
            var progressUpdater = new JobProgressUpdater(dataTable.Rows.Count, onProgressChanged);
            foreach (DataRow item in dataTable.Rows)
            {
                var row = CreateRowFromDataRow(item, columnFieldTypes, hasErrors, resultErrors);
                _ = sheetData.AppendChild(row);
                progressUpdater.Increment();
            }

            progressUpdater.EnsureFinalUpdateAsync().Wait(CancellationToken.None);

            return resultErrors.Values;
        }

        private static Row CreateRowFromDataRow(
            DataRow dataRow,
            IReadOnlyList<string> columnFieldTypes,
            bool hasErrors,
            IDictionary<string, ExcelExportException> resultErrors)
        {
            var row = new Row();

            for (var i = 0; i < dataRow.ItemArray.Length; i++)
            {
                var cell = CreateCellFromDataItem(dataRow[i], columnFieldTypes[i], hasErrors, resultErrors);
                _ = row.AppendChild(cell);
            }

            return row;
        }

        [SuppressMessage("Design", "CA1031:Do not catch general exception types", Justification = "Need to collect all errors")]
        private static Cell CreateCellFromDataItem(
            object dataItem,
            string columnFieldType,
            bool hasErrors,
            IDictionary<string, ExcelExportException> resultErrors)
        {
            var fieldValue = IllegalCharacters.GetSanatizedFieldValue(dataItem.ToString());
            var cell = new Cell();

            try
            {
                SetCellValueBasedOnType(cell, fieldValue, columnFieldType);

                if (hasErrors && cell.CellValue != null && cell.CellValue.Text.ToLower().Contains("data error", StringComparison.OrdinalIgnoreCase))
                {
                    cell.StyleIndex = Convert.ToUInt32(1); // Apply first style format
                }
            }
            catch (Exception ex)
            {
                AddErrorMessage(resultErrors, ex, dataItem.ToString());
                cell = new Cell
                {
                    CellValue = new CellValue("Data error: " + ex.Message),
                    DataType = CellValues.String
                };
            }

            return cell;
        }

        private static void SetCellValueBasedOnType(Cell cell, string fieldValue, string columnFieldType)
        {
            switch (columnFieldType)
            {
                case nameof(DataType.Integer):
                    SetInt32CellValue(cell, fieldValue);
                    break;
                case nameof(DataType.Double):
                    SetDoubleCellValue(cell, fieldValue);
                    break;
                default:
                    SetStringCellValue(cell, fieldValue);
                    break;
            }
        }

        private static void SetInt32CellValue(Cell cell, string fieldValue)
        {
            if (string.IsNullOrEmpty(fieldValue))
            {
                return;
            }

            if (fieldValue.StartsWith('0') && fieldValue.Length > 1)
            {
                cell.CellValue = new CellValue(fieldValue);
                cell.DataType = CellValues.String;
            }
            else
            {
                cell.CellValue = int.TryParse(fieldValue, out var intValue) ?
                    new CellValue(intValue) : new CellValue(fieldValue);
                cell.DataType = CellValues.Number;
            }
        }

        private static void SetDoubleCellValue(Cell cell, string fieldValue)
        {
            if (string.IsNullOrEmpty(fieldValue))
            {
                return;
            }

            cell.CellValue = double.TryParse(fieldValue, out var doubleValue) ?
                                    new CellValue(doubleValue) : new CellValue(fieldValue);
            cell.DataType = CellValues.Number;
        }

        private static void SetStringCellValue(Cell cell, string fieldValue)
        {
            if (string.IsNullOrEmpty(fieldValue))
            {
                return;
            }

            cell.CellValue = new CellValue(fieldValue);
            cell.DataType = CellValues.String;
        }

        private static Cell CreateTextCell(int columnNumber, int rowNumber, object cellValue)
        {
            var cell = new Cell
            {
                DataType = CellValues.InlineString,
                CellReference = GetColumnName(columnNumber) + rowNumber
            };

            var inlineString = new InlineString();
            var t = new Text
            {
                Text = cellValue.ToString()
            };

            inlineString.AppendChild(t);
            cell.AppendChild(inlineString);

            return cell;
        }

        private static string GetColumnName(int columnIndex)
        {
            var dividend = columnIndex;
            var columnName = string.Empty;

            while (dividend > 0)
            {
                var modifier = (dividend - 1) % 26;
                columnName = Convert.ToChar(65 + modifier) + columnName;
                dividend = (dividend - modifier) / 26;
            }

            return columnName;
        }

        private Dictionary<int, List<int>> GetRootEntityChildren(List<int> entityIds, List<ExcelExportEntityTypeModel> sortedEntityTypeModels, CancellationToken cancellationToken)
        {
            Dictionary<int, List<int>> entityChildren = entityIds.ToDictionary(e => e, e => new List<int>());
            DataRepository repository = new DataRepository(context);

            foreach (ExcelExportEntityTypeModel model in sortedEntityTypeModels)
            {
                cancellationToken.ThrowIfCancellationRequested();

                if (string.IsNullOrEmpty(model.LinkDirection))
                {
                    continue;
                }

                if (!model.LinkDirection.Equals("outbound"))
                {
                    continue;
                }

                var result = repository.GetOutboundChildrenForEntitiesAndLinkType(entityIds, model.LinkTypeId, cancellationToken);

                foreach (var parent in result)
                {
                    if (!entityChildren.ContainsKey(parent.Key))
                    {
                        entityChildren.Add(parent.Key, new List<int>());
                    }

                    foreach (int id in parent.Value)
                    {
                        if (!entityChildren[parent.Key].Contains(id))
                        {
                            entityChildren[parent.Key].Add(id);
                        }
                    }
                }
            }
            return entityChildren;
        }

        private Dictionary<int, List<int>> GetRootEntityParents(List<int> entityIds, List<ExcelExportEntityTypeModel> sortedEntityTypeModels, CancellationToken cancellationToken)
        {
            Dictionary<int, List<int>> entityParents = new Dictionary<int, List<int>>();

            DataRepository repository = new DataRepository(context);

            foreach (ExcelExportEntityTypeModel model in sortedEntityTypeModels)
            {
                cancellationToken.ThrowIfCancellationRequested();

                if (string.IsNullOrEmpty(model.LinkDirection))
                {
                    continue;
                }

                if (!model.LinkDirection.Equals("inbound"))
                {
                    continue;
                }

                var result = repository.GetInboundParentsForEntitiesAndLinkType(entityIds, model.LinkTypeId, cancellationToken);

                foreach (var parent in result)
                {
                    if (!entityParents.ContainsKey(parent.Key))
                    {
                        entityParents.Add(parent.Key, new List<int>());
                    }

                    foreach (int id in parent.Value)
                    {
                        if (!entityParents[parent.Key].Contains(id))
                        {
                            entityParents[parent.Key].Add(id);
                        }
                    }
                }
            }
            return entityParents;
        }

        private List<int> GetSoloEntities(List<int> entityIds, Dictionary<int, List<int>> entityParents)
        {
            var parentEntities = entityParents.Values.SelectMany(x => x).ToHashSet();

            var result = entityIds.Where(id => !parentEntities.Contains(id)).ToList();

            return result;
        }

        private string GetWorksheetName(string id, LocaleString name, CultureInfo cultureInfo = null)
        {

            cultureInfo = cultureInfo ?? this.context.ModelLanguage;
            var worksheetName = name?[cultureInfo];

            if (string.IsNullOrEmpty(worksheetName))
            {
                worksheetName = string.IsNullOrEmpty(id) ? string.Empty : $"[{id}]";
            }

            // Worksheet names can't have  the following character: ],\,*,[,?,/,:
            worksheetName = Regex.Replace(worksheetName, @"[]|\\|*|[|?|/|:]", "");
            return worksheetName;

        }

        private void PopulateTableColumns(DataTable table)
        {
            //Add all necessary columns
            table.Columns.Add("UploadName");
            table.Columns.Add("UploadBy");
            table.Columns.Add("UploadedDate");
            table.Columns.Add("ImportedBy");
            table.Columns.Add("ImportedDate");
            table.Columns.Add("EntityId", typeof(Int32));
            table.Columns.Add("DisplayName");
            table.Columns.Add("FieldName");
            table.Columns.Add("FieldDataBefore");
            table.Columns.Add("FieldDataAfter");
            table.Columns.Add("Status");
            table.Columns.Add("ApprovedOrRejectedBy");
        }

        private void PopulateDataHistoryToTableRows(DataTable table, ExcelExportHistoryModel excelExportHistoryModel, CancellationToken cancellationToken)
        {
            if (excelExportHistoryModel.EntityId.HasValue)
            {
                //Only grab data for a selected entity and selected suppliers
                PopulateDataHistoryToTableForEntity(table, excelExportHistoryModel);
            }
            else
            {
                Dictionary<string, string> displayNameByFieldType = GetDisplayNameByFieldTypes();

                List<string> specFieldTypeIds = this.context.DataPersistance.GetSpecFieldTypeIdsFromImportedHistory(excelExportHistoryModel.SupplierIds);
                Dictionary<string, string> displayNameBySpecFieldTypes = GetDisplayNameBySpecFieldTypes(specFieldTypeIds);

                foreach (int supplierId in excelExportHistoryModel.SupplierIds)
                {
                    if (cancellationToken.IsCancellationRequested)
                    {
                        return;
                    }

                    List<ExcelExportHistoryResultModel> result = this.dataContext.GetExcelExportHistoryForSupplier(supplierId);

                    PopulateHistoryDataToRow(result, table, displayNameByFieldType, displayNameBySpecFieldTypes);
                }

            }
        }

        private void PopulateDataHistoryToTableForEntity(DataTable table, ExcelExportHistoryModel excelExportHistoryModel)
        {
            if (excelExportHistoryModel.EntityId != null)
            {
                Dictionary<string, string> displayNameByFieldType = GetDisplayNameByFieldTypes();
                Dictionary<string, string> displayNameBySpecFieldTypes =
                    GetDisplayNameBySpecFieldTypesFromEntityId(excelExportHistoryModel.EntityId.Value);

                List<ExcelExportHistoryResultModel> result = this.dataContext.GetExcelExportHistoryForEntity(excelExportHistoryModel.EntityId.Value);

                PopulateHistoryDataToRow(result, table, displayNameByFieldType, displayNameBySpecFieldTypes);
            }
        }

        private void PopulateHistoryDataToRow(List<ExcelExportHistoryResultModel> result, DataTable table, Dictionary<string, string> displayNameByFieldType, Dictionary<string, string> displayNameBySpecFieldTypes)
        {
            foreach (ExcelExportHistoryResultModel historyResultModel in result)
            {
                var row = table.NewRow();
                var name = String.Empty;

                row["UploadName"] = historyResultModel.BatchName;
                row["UploadBy"] = historyResultModel.UploadedBy;
                row["UploadedDate"] = historyResultModel.UploadedDate;
                row["ImportedBy"] = historyResultModel.ImportedBy;
                row["ImportedDate"] = historyResultModel.ImportedDate;
                row["EntityId"] = historyResultModel.EntityId;
                row["DisplayName"] = historyResultModel.DisplayName;

                if (historyResultModel.IsSpecificationField)
                {
                    if (displayNameBySpecFieldTypes.TryGetValue(historyResultModel.FieldTypeId, out name))
                    {
                        row["FieldName"] = name;
                    }
                    else
                    {
                        row["FieldName"] = "Undefined Field Type";
                    }
                }
                else
                {
                    if (displayNameByFieldType.TryGetValue(historyResultModel.FieldTypeId, out name))
                    {
                        row["FieldName"] = name;
                    }
                    else
                    {
                        row["FieldName"] = "Undefined Field Type";
                    }
                }

                row["FieldDataBefore"] = historyResultModel.FieldDataBefore;
                row["FieldDataAfter"] = historyResultModel.FieldDataAfter;

                if (historyResultModel.FieldApprovalStatus.HasValue)
                {
                    if (historyResultModel.FieldApprovalStatus.Value == 1)
                        row["Status"] = ImportStatus.Imported;
                    else if (historyResultModel.FieldApprovalStatus.Value == 2)
                        row["Status"] = ImportStatus.Rejected;
                    else
                        row["Status"] = ImportStatus.Unknown;
                }
                else
                {
                    row["Status"] = historyResultModel.EntityStatus;
                }

                row["ApprovedOrRejectedBy"] = historyResultModel.FieldApprovalStatusUsername;

                table.Rows.Add(row);
            }
        }

        private Dictionary<string, string> GetDisplayNameByFieldTypes()
        {
            List<FieldType> fieldTypes = this.context.DataPersistance.GetAllFieldTypes();

            Dictionary<string, string> result = new Dictionary<string, string>();

            foreach (FieldType fieldType in fieldTypes)
            {
                if (result.ContainsKey(fieldType.Id))
                {
                    continue;
                }

                if (fieldType.Name != null && !string.IsNullOrEmpty(fieldType.Name[this.context.DataLanguage]))
                {
                    result.Add(fieldType.Id, fieldType.Name[this.context.DataLanguage]);
                }
                else
                {
                    result.Add(fieldType.Id, "[" + fieldType.Id + "]");
                }
            }

            return result;
        }

        private Dictionary<string, string> GetDisplayNameBySpecFieldTypes(List<string> specFieldTypeIds)
        {
            List<SpecificationFieldType> specFieldTypes = this.context.DataPersistance.GetSpecificationFieldTypes(specFieldTypeIds);

            Dictionary<string, string> result = new Dictionary<string, string>();

            foreach (SpecificationFieldType fieldType in specFieldTypes ?? Enumerable.Empty<SpecificationFieldType>())
            {
                if (result.ContainsKey(fieldType.Id))
                {
                    continue;
                }

                if (fieldType.Name != null && !string.IsNullOrEmpty(fieldType.Name[this.context.DataLanguage]))
                {
                    result.Add(fieldType.Id, fieldType.Name[this.context.DataLanguage]);
                }
                else
                {
                    result.Add(fieldType.Id, "[" + fieldType.Id + "]");
                }
            }

            return result;
        }

        private Dictionary<string, string> GetDisplayNameBySpecFieldTypesFromEntityId(int entityId)
        {
            List<string> specFieldTypeIds =
                this.context.DataPersistance.GetSpecFieldTypeIdsFromImportedHistoryByEntityId(entityId);

            return GetDisplayNameBySpecFieldTypes(specFieldTypeIds);
        }

        private string GetCVLValueForLanguage(CVLValue cvlValue, CultureInfo cultureInfo = null)
        {
            return cvlValue.Value.GetType() == typeof(LocaleString)
                ? ((LocaleString)cvlValue.Value)[cultureInfo ?? this.context.DataLanguage]
                : cvlValue.Value.ToString();
        }

        private void InsertCVLColumns(List<FieldType> cVLColumns, Dictionary<string, CVL> cvls, DataTable dataTable, List<CultureInfo> cultureInfos, string cvlExportSetting)
        {
            foreach (var cvlColumn in cVLColumns)
            {
                var columnName = GetColumnNameByFieldType(cvlColumn);
                if (dataTable.Columns.Contains(columnName))
                {
                    if (cvls.ContainsKey(cvlColumn.CVLId))
                    {
                        var cvl = cvls[cvlColumn.CVLId];
                        if (cvl.DataType == DataType.LocaleString)
                        {
                            var index = dataTable.Columns.IndexOf(cvlColumn.Id);
                            foreach (var cultureInfo in cultureInfos)
                            {
                                columnName = GetColumnNameByFieldType(cvlColumn, cultureInfo);
                                InsertColumnAtIndex(dataTable, columnName, ++index);
                            }
                        }
                        else
                        {
                            if (cvlExportSetting == CVLExportSetting.CvlKeyAndValue)
                            {
                                var index = dataTable.Columns.IndexOf(cvlColumn.Id);
                                columnName = $"{cvlColumn.Id} (values)";
                                InsertColumnAtIndex(dataTable, columnName, index + 1);
                            }
                        }
                    }
                }
            }
        }

        private void InsertColumnCVLValues(FieldType cvlColumn, Dictionary<string, CVL> cvls, DataTable dataTable, List<CultureInfo> cultureInfos, string cvlExportSetting, DataRow row, Dictionary<string, Dictionary<string, CVLValue>> cvlValueDictionary)
        {
            if (cvlExportSetting == CVLExportSetting.CvlKey)
            {
                return;
            }

            if (!dataTable.Columns.Contains(cvlColumn.Id) || DBNull.Value.Equals(row[cvlColumn.Id]))
            {
                return;
            }

            var columnName = GetColumnNameByFieldType(cvlColumn);
            if (!dataTable.Columns.Contains(columnName))
            {
                return;
            }

            object fieldData;
            if (!cvls.ContainsKey(cvlColumn.CVLId))
            {
                return;
            }

            var cvl = cvls[cvlColumn.CVLId];
            var cvlKey = row[columnName].ToString();
            var keys = cvlKey.Split(';');
            var valueList = new List<string>();

            if (cvl.DataType == DataType.LocaleString)
            {
                var index = dataTable.Columns.IndexOf(cvlColumn.Id);
                foreach (var cultureInfo in cultureInfos)
                {
                    valueList = new List<string>();
                    foreach (var key in keys)
                    {
                        if (this.CvlKeyIsValid(cvlValueDictionary, cvlColumn.CVLId, key, valueList, row))
                        {
                            valueList.Add(((LocaleString)cvlValueDictionary[cvlColumn.CVLId][key].Value)[cultureInfo]);
                        }
                    }

                    columnName = GetColumnNameByFieldType(cvlColumn, cultureInfo);
                    fieldData = string.Join(";", valueList.ToArray());
                    row[columnName] = fieldData;
                }
            }
            else
            {
                foreach (var key in keys)
                {
                    if (this.CvlKeyIsValid(cvlValueDictionary, cvlColumn.CVLId, key, valueList, row))
                    {
                        valueList.Add(cvlValueDictionary[cvlColumn.CVLId][key].Value?.ToString());
                    }
                }

                fieldData = string.Join(";", valueList.ToArray());
                var col = cvlExportSetting == CVLExportSetting.CvlKeyAndValue
                    ? $"{cvlColumn.Id} (values)"
                    : cvlColumn.Id;
                row[col] = fieldData;
            }
        }

        private void RemoveColumnCVLValue(List<FieldType> cVLColumns, string cvlExportSetting, DataTable dataTable, ModelRepository modelRepository)
        {
            foreach (var cvlColumn in cVLColumns)
            {
                var cvl = modelRepository.GetCVL(cvlColumn.CVLId);
                if (cvl.DataType == DataType.LocaleString)
                {
                    if (cvlExportSetting == CVLExportSetting.CvlValue && dataTable.Columns.Contains(cvlColumn.Id))
                    {
                        dataTable.Columns.Remove(cvlColumn.Id);
                    }
                }
            }
        }

        private void InsertCVLValuesWithKeys(FieldType cvlColumn, Dictionary<string, CVL> cvls, DataTable dataTable, string availableCvlKeysSetting, DataRow row, Dictionary<string, Dictionary<string, CVLValue>> cvlValueDictionary)
        {
            if (!dataTable.Columns.Contains(cvlColumn.Id) || DBNull.Value.Equals(row[cvlColumn.Id]))
            {
                return;
            }

            var columnName = GetColumnNameByFieldType(cvlColumn);
            if (!dataTable.Columns.Contains(columnName))
            {
                return;
            }

            if (cvlColumn.Multivalue)
            {
                return;
            }

            if(cvls.Count == 0)
            {
                var modelRepository = new ModelRepository(this.context);
                cvls = new Dictionary<string, CVL>
                {
                    {cvlColumn.CVLId, modelRepository.GetCVL(cvlColumn.CVLId) }
                };
            }

            object fieldData;

            var cvl = cvls[cvlColumn.CVLId];
            var key = row[columnName].ToString();

            var valueList = new List<string>();

            if (this.CvlKeyIsValid(cvlValueDictionary, cvlColumn.CVLId, key, valueList, row))
            {
                if(cvl.DataType == DataType.LocaleString)
                {
                    valueList.Add(((LocaleString)cvlValueDictionary[cvlColumn.CVLId][key].Value)[context.DataLanguage]);
                }
                else
                {
                    valueList.Add(cvlValueDictionary[cvlColumn.CVLId][key].Value?.ToString());
                }

            }

            fieldData = key + " | " + valueList.FirstOrDefault();

            row[cvlColumn.Id] = fieldData;
        }

        private void InsertCVLValuesWithKeys(FieldType cvlColumn, Field field, DataTable dataTable, string availableCvlKeysSetting, DataRow row, Dictionary<string, Dictionary<string, CVLValue>> cvlValueDictionary)
        {

            var modelRepository = new ModelRepository(this.context);

            object fieldData;

            var cvl = modelRepository.GetCVL(cvlColumn.CVLId);
            var key = field.Data.ToString();

            var valueList = new List<string>();

            if (this.CvlKeyIsValid(cvlValueDictionary, cvlColumn.CVLId, key, valueList, row))
            {
                if (cvl.DataType == DataType.LocaleString)
                {
                    valueList.Add(((LocaleString)cvlValueDictionary[cvlColumn.CVLId][key].Value)[context.DataLanguage]);
                }
                else
                {
                    valueList.Add(cvlValueDictionary[cvlColumn.CVLId][key].Value?.ToString());
                }

            }

            fieldData = key + " | " + valueList.FirstOrDefault();

            row[cvlColumn.Id] = fieldData;
        }

        private void RemoveColumnCVLValue(List<FieldType> cVLColumns, string cvlExportSetting, string availableCvlKeysSetting, DataTable dataTable, ModelRepository modelRepository)
        {
            foreach (var cvlColumn in cVLColumns)
            {
                var cvl = modelRepository.GetCVL(cvlColumn.CVLId);
                if (cvl.DataType == DataType.LocaleString)
                {
                    if (cvlExportSetting == CVLExportSetting.CvlValue && dataTable.Columns.Contains(cvlColumn.Id))
                    {
                        dataTable.Columns.Remove(cvlColumn.Id);
                    }
                }
            }
        }

        private bool CvlKeyIsValid(Dictionary<string, Dictionary<string, CVLValue>> cvlValueDictionary, string cvlId, string key, List<string> valueList, DataRow row)
        {
            if (!cvlValueDictionary[cvlId].ContainsKey(key))
            {
                valueList.Add($"Data error: The cvl key [{key}] does not exist");
                row.RowError = $"Data error: The cvl key does not exist";
                return false;
            }

            return true;
        }

        private static void AddErrorMessage(IDictionary<string, ExcelExportException> resultErrors, Exception exception, string fieldValue)
        {
            var message = exception?.Message ?? string.Empty;

            if (resultErrors.TryGetValue(message, out var value))
            {
                value.FieldValues.Add(fieldValue);
            }
            else
            {
                resultErrors.Add(message, new ExcelExportException(message, exception)
                {
                    FieldValues = new List<string> { fieldValue }
                });
            }
        }

        private void LogExcelExportErrors(IEnumerable<ExcelExportException> resultErrors, string fileName)
        {
            foreach (var error in resultErrors)
            {
                var fieldValues = string.Join(", ", error.FieldValues);

                var excelExportErrorMessage = $"Export failed the following fields: {fieldValues} in {fileName}";

                this.LogExceptionToAppropriateSink(error.InnerException, excelExportErrorMessage);
            }
        }

        private void LogExceptionToAppropriateSink(Exception error, string errorMessage)
        {
            if (error is ArgumentException)
            {
                context.Log(LogLevel.Error, $"{errorMessage}. {error.Message}");
            }

            Log.Error(error, "{errorMessage}, Environment: {CustomerSafeName}/{EnvironmentSafeName}", errorMessage, this.context.CustomerSafeName, this.context.EnvironmentSafeName);
        }
    }
}
