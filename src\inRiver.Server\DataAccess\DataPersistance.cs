namespace inRiver.Server.DataAccess
{
    using System;
    using System.Collections.Generic;
    using System.Data;
    using System.Data.SqlClient;
    using System.Globalization;
    using System.Linq;
    using System.Security.Cryptography;
    using System.ServiceModel;
    using System.Text;
    using System.Text.RegularExpressions;
    using System.Threading;
    using System.Threading.Tasks;
    using System.Xml;
    using System.Xml.Serialization;
    using Dapper;
    using EventPublishing;
    using Helpers;
    using inRiver.Core.Models.inRiver.ExcelImport;
    using inRiver.Core.Util;
    using inRiver.Log;
    using inRiver.Remoting.Dto;
    using inRiver.Remoting.Objects;
    using inRiver.Remoting.Util;
    using inRiver.Server.Error;
    using inRiver.Server.Managers;
    using Models;
    using Newtonsoft.Json;
    using Serilog;
    using Criteria = inRiver.Remoting.Query.Criteria;
    using Join = inRiver.Remoting.Query.Join;
    using LinkDirection = inRiver.Remoting.Query.LinkDirection;
    using LinkQuery = inRiver.Remoting.Query.LinkQuery;
    using LogLevel = Remoting.Log.LogLevel;
    using Operator = inRiver.Remoting.Query.Operator;
    using Query = inRiver.Remoting.Query.Query;
    using SpecificationQuery = inRiver.Remoting.Query.SpecificationQuery;
    using SyndicationRelatedEntityFieldValue = inRiver.iPMC.Persistance.SyndicationRelatedEntityFieldValue;
    using SystemQuery = inRiver.Remoting.Query.SystemQuery;
    using Utilities = inRiver.Server.Util.Utilities;
    
    public partial class inRiverPersistance
    {
        private delegate void AsyncStoreEntityFieldRevisionHistory(int entityId, List<Field> fieldsToUpdate, bool entityIsNew, string username);

        #region ContentSegmentation

        public bool SetSegmentForEntities(List<int> entityIds, int segmentId)
        {
            bool success = false;

            // Make sure there are ids in the list
            if (entityIds.Count < 1)
                return true;

            List<List<int>> splitList = Core.Util.Utilities.SplitListIntoBatches(entityIds);

            using (var conn = new SqlConnection(ConnectionString))
            {
                conn.Open();
                var tran = conn.BeginTransaction("SetEntityContSeg");

                foreach (var batch in splitList)
                {


                    try
                    {
                        var cmd = conn.CreateCommand();
                        cmd.Transaction = tran;
                        cmd.CommandText = $"Update [dbo].[Entity] SET [ContentSegmentationId] = @ContentSegmentationId WHERE [Id] IN ({string.Join(",", batch)}) ";
                        cmd.Parameters.AddWithValue("@ContentSegmentationId", segmentId);
                        success = cmd.ExecuteNonQuery() > 0 ? true : false;
                    }
                    catch (Exception e)
                    {
                        tran.Rollback();
                        throw new Exception($"Error occured while updating Segment Id {segmentId}, to the Entities {string.Join(",", batch)}");
                    }
                }
                tran.Commit();
            }
            return success;
        }

        public async Task<IEnumerable<Segment>> GetAllSegmentsAsync()
        {
            var lst = new List<Segment>();

            using (var connection = new SqlConnection(ConnectionString))
            {
                try
                {
                    await using var command = connection.CreateCommand();
                    command.CommandText = "SELECT [ID],[SegmentName],[SegmentDescription] FROM [dbo].[ContentSegmentation];";
                    connection.Open();

                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            lst.Add(new Segment()
                            {
                                Id = reader.GetInt32(0),
                                Name = reader.GetString(1),
                                Description = reader.GetString(2)
                            });
                        }
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, $"An unexpected error occured when getting All Content Segmentations.");
                    throw;
                }
            }

            return lst;
        }

        #endregion

        #region Entity

        /**
         * This method is to accommodate adding entity with optional SQL transaction handled by the caller (via SqlCommand) 
         */
        private DtoEntity AddEntityTransactional(Entity entity, SqlCommand command)
        {
            DateTime now = DateTime.UtcNow;

            try
            {
                command.Parameters.Clear();  // it is important to clear the Parameters since the 'command' may have been previously used

                command.CommandText = "INSERT INTO Entity (Version, EntityTypeId, DateCreated, LastModified, ChangeSet, FieldSetId, CreatedBy, ModifiedBy, DisplayNameFieldTypeId, DisplayName, DisplayDescriptionFieldTypeId, DisplayDescription, MainPicture, MainPictureUrl, ContentSegmentationId) VALUES (@Version, @EntityTypeId, @DateCreated, @LastModified, @ChangeSet, @FieldSetId, @CreatedBy, @ModifiedBy, @DisplayNameFieldTypeId, @DisplayName, @DisplayDescriptionFieldTypeId, @DisplayDescription, @MainPicture, @MainPictureUrl, @ContentSegmentationId) SET @Id = SCOPE_IDENTITY()";

                command.Parameters.AddWithValue("@Version", 1);
                command.Parameters.AddWithValue("@EntityTypeId", entity.EntityType.Id);
                command.Parameters.AddWithValue("@DateCreated", now);
                command.Parameters.AddWithValue("@LastModified", now);
                command.Parameters.AddWithValue("@ChangeSet", 1);
                command.Parameters.AddWithValue("@CreatedBy", this.context.Username);
                command.Parameters.AddWithValue("@ModifiedBy", this.context.Username);

                if (string.IsNullOrWhiteSpace(entity.FieldSetId))
                {
                    command.Parameters.AddWithValue("@FieldSetId", DBNull.Value);
                }
                else
                {
                    command.Parameters.AddWithValue("@FieldSetId", entity.FieldSetId);
                }

                if (entity.MainPictureId.HasValue)
                {
                    command.Parameters.AddWithValue("@MainPicture", entity.MainPictureId.Value);
                }
                else
                {
                    command.Parameters.AddWithValue("@MainPicture", DBNull.Value);
                }

                if (entity.MainPictureUrl != null)
                {
                    command.Parameters.AddWithValue("@MainPictureUrl", entity.MainPictureUrl);
                }
                else
                {
                    command.Parameters.AddWithValue("@MainPictureUrl", DBNull.Value);
                }

                int segmentId = entity.Segment == null ? 0 : entity.Segment.Id;
                command.Parameters.AddWithValue("@ContentSegmentationId", segmentId);

                if (entity.DisplayName != null)
                {
                    command.Parameters.AddWithValue("@DisplayNameFieldTypeId", entity.DisplayName.FieldType.Id);

                    if (entity.DisplayName.Data == null)
                    {
                        command.Parameters.AddWithValue("@DisplayName", DBNull.Value);
                    }
                    else if (entity.DisplayName.Data is DateTime)
                    {
                        command.Parameters.AddWithValue("@DisplayName", ((DateTime)entity.DisplayName.Data).ToString(Constants.InternalDateTimeFormat, CultureInfo.InvariantCulture));
                    }
                    else if (entity.DisplayName.Data is LocaleString)
                    {
                        command.Parameters.AddWithValue("@DisplayName", JsonConvert.SerializeObject(entity.DisplayName.Data));
                    }
                    else
                    {
                        command.Parameters.AddWithValue("@DisplayName", entity.DisplayName.Data.ToString());
                    }
                }
                else
                {
                    command.Parameters.AddWithValue("@DisplayNameFieldTypeId", DBNull.Value);
                    command.Parameters.AddWithValue("@DisplayName", DBNull.Value);
                }

                if (entity.DisplayDescription != null)
                {
                    command.Parameters.AddWithValue("@DisplayDescriptionFieldTypeId", entity.DisplayDescription.FieldType.Id);

                    if (entity.DisplayDescription.Data == null)
                    {
                        command.Parameters.AddWithValue("@DisplayDescription", DBNull.Value);
                    }
                    else if (entity.DisplayDescription.Data is DateTime)
                    {
                        command.Parameters.AddWithValue("@DisplayDescription", ((DateTime)entity.DisplayDescription.Data).ToString(Constants.InternalDateTimeFormat, CultureInfo.InvariantCulture));
                    }
                    else if (entity.DisplayDescription.Data is LocaleString)
                    {
                        command.Parameters.AddWithValue("@DisplayDescription", JsonConvert.SerializeObject(entity.DisplayDescription.Data));
                    }
                    else
                    {
                        command.Parameters.AddWithValue("@DisplayDescription", entity.DisplayDescription.Data.ToString());
                    }
                }
                else
                {
                    command.Parameters.AddWithValue("@DisplayDescriptionFieldTypeId", DBNull.Value);
                    command.Parameters.AddWithValue("@DisplayDescription", DBNull.Value);
                }

                SqlParameter idParameter = new SqlParameter("@Id", SqlDbType.Int);
                idParameter.Direction = ParameterDirection.Output;
                command.Parameters.Add(idParameter);

                command.ExecuteNonQuery();

                var id = (int)idParameter.Value;

                entity.DateCreated = now;
                entity.LastModified = now;
                entity.Id = id;
                entity.Version = 1;
                entity.ChangeSet = 1;
                entity.CreatedBy = this.context.Username;
                entity.ModifiedBy = this.context.Username;
                entity.Segment = GetSegment(segmentId);
            }
            catch (FaultException)
            {
                throw;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "An unexpected error occurred when adding entity");
                throw ErrorUtility.GetDataAccessException("An unexpected error occurred when adding entity", ex);
            }

            return DtoFactory.DtoFromEntity(entity);
        }


        public DtoEntity AddEntity(Entity entity)
        {
            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                connection.Open();

                using (SqlCommand command = connection.CreateCommand())
                {
                    return AddEntityTransactional(entity, command);
                }
            }
        }

        public DtoEntity AddEntityWithFields(Entity entity)
        {
            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                connection.Open();

                using (SqlCommand command = connection.CreateCommand())
                {
                    var transaction = connection.BeginTransaction("AddEntityAndFields");
                    try
                    {
                        command.Transaction = transaction;

                        DtoEntity dtoEntity = AddEntityTransactional(entity, command);

                        IEnumerable<Field> fields = entity.Fields?.Where(f => !f.IsEmpty());
                        if (fields != null && fields.Any())
                        {
                            foreach (var field in fields)
                            {
                                field.EntityId = dtoEntity.Id;
                            }

                            AddFieldsTransactional(fields.ToList(), command);
                        }

                        transaction.Commit();
                        return dtoEntity;
                    }
                    catch (Exception e)
                    {
                        transaction.Rollback();
                        throw e;
                    }
                }
            }
        }

        public List<DtoEntity> AddEntities(List<Entity> entities)
        {
            List<DtoEntity> retVal = new List<DtoEntity>();

            if (entities != null && entities.Any())
            {
                using (SqlConnection connection = new SqlConnection(this.ConnectionString))
                {
                    connection.Open();

                    var transac = connection.BeginTransaction("AddEntities");

                    using (SqlCommand command = connection.CreateCommand())
                    {
                        command.Transaction = transac;

                        try
                        {
                            foreach (var entity in entities)
                            {
                                // 1. to insert the Entity record ----
                                DtoEntity dtoEntity = AddEntityTransactional(entity, command);
                                retVal.Add(dtoEntity);

                                // 2. to insert the Field records associated to Entity ----
                                if (entity.Fields != null && entity.Fields.Any())
                                {
                                    foreach (var field in entity.Fields)
                                    {
                                        field.EntityId = dtoEntity.Id;
                                    }

                                    AddFieldsTransactional(entity.Fields, command);
                                }
                            }

                            transac.Commit();
                        }
                        catch (Exception ex)
                        {
                            try
                            {
                                transac.Rollback();
                            }
                            catch  // this could happen
                            {
                                Log.Error(ex, $"Error on trying to ROLLBACK on AddEntities().  Env: {context?.EnvironmentFullName}");
                            }

                            throw ErrorUtility.GetDataAccessException(
                                "An unexpected error occured when adding entities ",
                                ex);
                        }
                    }
                }
            }

            return retVal;
        }

        public DtoEntity GetEntity(int id)
        {
            DtoEntity entity;

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    string segmentIdsInClause = permittedSegmentIdsClause();

                    command.CommandText = "SELECT EntityTypeId, DateCreated, LastModified, [Version], Locked, ChangeSet, FieldSetId, " +
                        "CreatedBy, ModifiedBy, [MainPicture], [DisplayNameFieldTypeId], [DisplayName], [DisplayDescriptionFieldTypeId], " +
                        $"[DisplayDescription], Completeness, [MainPictureUrl], ContentSegmentationId, C.SegmentName FROM Entity E inner join ContentSegmentation C on E.ContentSegmentationId = C.ID WHERE {segmentIdsInClause} E.Id = @Id AND PendingDelete IS NULL";

                    command.Parameters.AddWithValue("@Id", id);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        if (!reader.HasRows)
                        {
                            reader.Close();
                            connection.Close();

                            return null;
                        }

                        reader.Read();

                        entity = new DtoEntity();
                        entity.Id = id;
                        entity.EntityTypeId = reader.GetString(0);
                        entity.DateCreated = DateTime.SpecifyKind(reader.GetDateTime(1), DateTimeKind.Utc).ToString(Constants.InternalDateTimeFormat, CultureInfo.InvariantCulture);
                        entity.LastModified = DateTime.SpecifyKind(reader.GetDateTime(2), DateTimeKind.Utc).ToString(Constants.InternalDateTimeFormat, CultureInfo.InvariantCulture);

                        entity.Version = reader.GetInt32(3);

                        if (!reader.IsDBNull(4))
                        {
                            entity.Locked = reader.GetString(4);
                        }

                        entity.ChangeSet = reader.GetInt32(5);

                        if (!reader.IsDBNull(6))
                        {
                            entity.FieldSetId = reader.GetString(6);
                        }

                        entity.CreatedBy = reader.GetString(7);
                        entity.ModifiedBy = reader.GetString(8);

                        if (!reader.IsDBNull(9))
                        {
                            entity.MainPictureId = reader.GetInt32(9);
                        }

                        if (!reader.IsDBNull(10))
                        {
                            string data = string.Empty;

                            if (!reader.IsDBNull(11))
                            {
                                data = reader.GetString(11);
                            }

                            entity.DisplayName = new DtoField
                            {
                                EntityId = id,
                                LastModified = entity.LastModified,
                                FieldTypeId = reader.GetString(10),
                                Data = data
                            };
                        }

                        if (!reader.IsDBNull(12))
                        {
                            string data = string.Empty;

                            if (!reader.IsDBNull(13))
                            {
                                data = reader.GetString(13);
                            }

                            entity.DisplayDescription = new DtoField
                            {
                                EntityId = id,
                                LastModified = entity.LastModified,
                                FieldTypeId = reader.GetString(12),
                                Data = data
                            };
                        }

                        if (!reader.IsDBNull(14))
                        {
                            entity.Completeness = reader.GetInt32(14);
                        }

                        if (!reader.IsDBNull(15))
                        {
                            entity.MainPictureUrl = reader.GetString(15);
                        }

                        entity.Segment = new DtoSegment() { Id = reader.GetInt32(16), Name = reader.GetString(17) };

                        entity.LoadLevel = LoadLevel.Shallow;

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting entity " + id);
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting entity " + id, ex);
                }
            }

            return entity;
        }

        public IDictionary<int, DtoEntity> GetEntitiesDictionary(List<int> ids)
        {
            return context.DataPersistance.GetEntities(ids).ToDictionary(e => e.Id, e => e);
        }

        public async Task<IDictionary<int, DtoEntity>> GetEntitiesDictionaryAsync(List<int> ids)
        {
            return (await context.DataPersistance.GetEntitiesAsync(ids)).ToDictionary(e => e.Id, e => e);
        }

        private List<DtoLink> GetAllLinksFromLinkListByEntityIds(List<DtoLink> links, List<int> ids, CancellationToken cancellationToken)
        {
            IDictionary<int, DtoEntity> entitiesDictionary = this.GetEntitiesDictionary(ids);

            Parallel.ForEach(
                links,
                new ParallelOptions { CancellationToken = cancellationToken },
                link => {
                    link.Source = entitiesDictionary.ContainsKey(link.Source.Id) ? entitiesDictionary[link.Source.Id] : null;
                    link.Target = entitiesDictionary.ContainsKey(link.Target.Id) ? entitiesDictionary[link.Target.Id] : null;

                    if (link.LinkEntity != null)
                    {
                        link.LinkEntity = entitiesDictionary.ContainsKey(link.LinkEntity.Id) ? entitiesDictionary[link.LinkEntity.Id] : null;
                    }
                });

            return links.Where(e => e.Target != null && e.Source != null).ToList();
        }

        private async Task<IEnumerable<DtoLink>> GetAllLinksFromLinkListByEntityIdsAsync(IEnumerable<DtoLink> links, IEnumerable<int> ids)
        {
            var entities = await this.context.DataPersistance.GetEntitiesAsync(ids);
            var entitiesDictionary = entities.ToDictionary(e => e.Id, e => e);

            return links.Select(link => {
                link.Source = entitiesDictionary.ContainsKey(link.Source.Id) ? entitiesDictionary[link.Source.Id] : null;
                link.Target = entitiesDictionary.ContainsKey(link.Target.Id) ? entitiesDictionary[link.Target.Id] : null;

                if (link.LinkEntity != null)
                {
                    link.LinkEntity = entitiesDictionary.ContainsKey(link.LinkEntity.Id) ? entitiesDictionary[link.LinkEntity.Id] : null;
                }

                return link;
            }).Where(l => l.Target != null && l.Source != null);
        }

        public Entity GetFullEntity(
            int id,
            EntityType entityType,
            bool includeDisplayInformation = true,
            bool includePendingDelete = false,
            bool ignoreSegmentCheck = false  // if 'true' then it will ignore the the permission check,
                                             // matching entity of any segments will be returned regardless of which segment user has permission to
            )
        {
            Entity entity = null;

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    string segmentIdsInClause = ignoreSegmentCheck ? "" : permittedSegmentIdsClause();

                    command.Parameters.AddWithValue("@Id", id);
                    command.Parameters.AddWithValue("@IncludePendingDelete", includePendingDelete);
                    command.CommandText = "SELECT EntityTypeId, DateCreated, LastModified, [Version], Locked, ChangeSet, FieldSetId, " +
                                          " CreatedBy, ModifiedBy, Completeness, ContentSegmentationId FROM Entity " +
                        $"WHERE {segmentIdsInClause} Id = @Id AND (PendingDelete IS NULL OR @IncludePendingDelete = 1)";

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            entity = new Entity();
                            entity.Id = id;
                            entity.EntityType = new EntityType { Id = reader.GetString(0) };
                            entity.DateCreated = reader.GetDateTime(1);
                            entity.LastModified = reader.GetDateTime(2);
                            entity.Version = reader.GetInt32(3);

                            if (!reader.IsDBNull(4))
                            {
                                entity.Locked = reader.GetString(4);
                            }

                            entity.ChangeSet = reader.GetInt32(5);

                            if (!reader.IsDBNull(6))
                            {
                                entity.FieldSetId = reader.GetString(6);
                            }

                            entity.CreatedBy = reader.GetString(7);
                            entity.ModifiedBy = reader.GetString(8);

                            if (!reader.IsDBNull(9))
                            {
                                entity.Completeness = reader.GetInt32(9);
                            }

                            entity.LoadLevel = LoadLevel.Shallow;

                            entity.Segment = new Segment() { Id = reader.GetInt32(10) };
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting entity " + id);
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting entity " + id, ex);
                }
            }

            if (entity == null)
            {
                return entity;
            }

            if (entityType == null)
            {
                entity.EntityType = context.DataPersistance.GetEntityType(entity.EntityType.Id);
            }
            else
            {
                entity.EntityType = entityType;
            }

            if (includeDisplayInformation)
            {

                if (!string.IsNullOrEmpty(entity.EntityType.GetDisplayNameFieldTypeId))
                {
                    entity.DisplayName = context.DataPersistance.GetFullField(
                        entity.Id,
                        entity.EntityType.Id,
                        entity.EntityType.GetDisplayNameFieldTypeId,
                        entity.DateCreated);
                }

                if (!string.IsNullOrEmpty(entity.EntityType.GetDisplayDescriptionFieldTypeId))
                {
                    entity.DisplayDescription = context.DataPersistance.GetFullField(
                        entity.Id,
                        entity.EntityType.Id,
                        entity.EntityType.GetDisplayDescriptionFieldTypeId,
                        entity.DateCreated);
                }
            }

            return entity;
        }

        public DtoEntity GetEntityWithData(int id)
        {
            DtoEntity entity;

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    string segmentIdsInClause = permittedSegmentIdsClause();

                    if (!string.IsNullOrWhiteSpace(segmentIdsInClause))
                    {
                        segmentIdsInClause = "Entity." + segmentIdsInClause;
                    }

                    command.CommandText =
                        "SELECT Entity.Id, Entity.EntityTypeId, Entity.DateCreated, Entity.LastModified, Entity.Version, Entity.Locked, Entity.ChangeSet, Entity.FieldSetId, Entity.CreatedBy, "
                        + "Entity.ModifiedBy, Entity.MainPicture, Entity.DisplayNameFieldTypeId, Entity.DisplayName, Entity.DisplayDescriptionFieldTypeId, Entity.DisplayDescription, "
                        + "FieldType.Id, Field.Value, Field.LastModified, Field.Revision, Entity.Completeness, Entity.MainPictureUrl "
                        + "FROM dbo.Entity INNER JOIN "
                        + "FieldType ON Entity.EntityTypeId = FieldType.EntityTypeId AND Entity.Id = @EntityId LEFT OUTER JOIN "
                        + $"Field ON FieldType.Id = Field.FieldTypeId AND {segmentIdsInClause} Field.EntityId = @EntityId";

                    command.Parameters.AddWithValue("@EntityId", id);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        entity = new DtoEntity();

                        while (reader.Read())
                        {
                            if (entity.Id == 0)
                            {
                                entity.Id = reader.GetInt32(0);
                                entity.Fields = new List<DtoField>();

                                entity.EntityTypeId = reader.GetString(1);
                                entity.DateCreated = reader.GetDateTime(2).ToString(Constants.InternalDateTimeFormat, CultureInfo.InvariantCulture);
                                entity.LastModified = reader.GetDateTime(3).ToString(Constants.InternalDateTimeFormat, CultureInfo.InvariantCulture);
                                entity.Version = reader.GetInt32(4);

                                if (!reader.IsDBNull(5))
                                {
                                    entity.Locked = reader.GetString(5);
                                }

                                entity.ChangeSet = reader.GetInt32(6);

                                if (!reader.IsDBNull(7))
                                {
                                    entity.FieldSetId = reader.GetString(7);
                                }

                                entity.CreatedBy = reader.GetString(8);
                                entity.ModifiedBy = reader.GetString(9);

                                if (!reader.IsDBNull(10))
                                {
                                    entity.MainPictureId = reader.GetInt32(10);
                                }

                                if (!reader.IsDBNull(11))
                                {
                                    string data = string.Empty;

                                    if (!reader.IsDBNull(12))
                                    {
                                        data = reader.GetString(12);
                                    }

                                    entity.DisplayName = new DtoField
                                    {
                                        EntityId = entity.Id,
                                        LastModified = entity.LastModified,
                                        FieldTypeId = reader.GetString(11),
                                        Data = data
                                    };
                                }

                                if (!reader.IsDBNull(13))
                                {
                                    string data = string.Empty;

                                    if (!reader.IsDBNull(14))
                                    {
                                        data = reader.GetString(14);
                                    }

                                    entity.DisplayDescription = new DtoField
                                    {
                                        EntityId = entity.Id,
                                        LastModified = entity.LastModified,
                                        FieldTypeId = reader.GetString(13),
                                        Data = data
                                    };
                                }

                                if (!reader.IsDBNull(19))
                                {
                                    entity.Completeness = reader.GetInt32(19);
                                }

                                if (!reader.IsDBNull(20))
                                {
                                    entity.MainPictureUrl = reader.GetString(20);
                                }

                                entity.LoadLevel = LoadLevel.DataOnly;
                            }

                            DtoField field = new DtoField();

                            field.FieldTypeId = reader.GetString(15);

                            if (!reader.IsDBNull(16))
                            {
                                field.Data = reader.GetString(16);
                            }

                            field.EntityId = entity.Id;

                            if (reader.IsDBNull(17))
                            {
                                field.LastModified = entity.LastModified;
                            }
                            else
                            {
                                field.LastModified = reader.GetDateTime(17).ToString(Constants.InternalDateTimeFormat, CultureInfo.InvariantCulture);
                            }

                            if (reader.IsDBNull(18))
                            {
                                field.Revision = 0;
                            }
                            else
                            {
                                field.Revision = reader.GetInt32(18);
                            }

                            entity.Fields.Add(field);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting list of entities");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting list of entities", ex);
                }
            }

            return entity;
        }

        public List<DtoEntity> GetEntitiesWithData(List<int> list, CancellationToken cancellationToken)
        {
            if (!list.Any())
            {
                return new List<DtoEntity>();
            }
            Dictionary<int, DtoEntity> entities = new Dictionary<int, DtoEntity>();

            Dictionary<string, string> entityFields = new Dictionary<string, string>();
            int totalIdsCount = list.Count;
            int batchCounter = 0;
            do
            {
                cancellationToken.ThrowIfCancellationRequested();

                List<int> batchIds = list.Skip(batchCounter).Take(4000).ToList();
                batchCounter += batchIds.Count;

                string selectIn = string.Join(",", batchIds);

                using (SqlConnection connection = new SqlConnection(this.ConnectionString))
                {
                    try
                    {
                        SqlCommand command = connection.CreateCommand();
                        command.CommandTimeout = 600;
                        cancellationToken.Register(() =>
                        {
                            command.Cancel();
                            cancellationToken.ThrowIfCancellationRequested();
                        });

                        string segmentIdsInClause = permittedSegmentIdsClause();

                        if (!string.IsNullOrWhiteSpace(segmentIdsInClause))
                        {
                            segmentIdsInClause = "Entity." + segmentIdsInClause;
                        }

                        command.CommandText =
                            "SELECT Entity.Id, Entity.EntityTypeId, Entity.DateCreated, Entity.LastModified, Entity.Version, Entity.Locked, Entity.ChangeSet, Entity.FieldSetId, Entity.CreatedBy, "
                            + "Entity.ModifiedBy, Entity.MainPicture, Entity.DisplayNameFieldTypeId, Entity.DisplayName, Entity.DisplayDescriptionFieldTypeId, Entity.DisplayDescription, "
                            + "Field.FieldTypeId, Field.Value, Field.LastModified, Field.Revision, Entity.Completeness, Entity.MainPictureUrl, Entity.ContentSegmentationId, C.SegmentName, C.SegmentDescription "
                            + "FROM dbo.Entity INNER JOIN "
                            + $"Field ON Entity.Id = Field.EntityId AND {segmentIdsInClause} Entity.Id IN ({selectIn}) "
                            + "INNER JOIN ContentSegmentation C on Entity.ContentSegmentationId = C.ID "
                            + "WHERE dbo.Entity.PendingDelete IS NULL";

                        connection.Open();

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                cancellationToken.ThrowIfCancellationRequested();
                                int id = reader.GetInt32(0);

                                DtoEntity entity;

                                if (!entities.ContainsKey(id))
                                {
                                    entity = new DtoEntity();
                                    entity.Id = id;
                                    entity.Fields = new List<DtoField>();

                                    entity.EntityTypeId = reader.GetString(1);
                                    entity.DateCreated = reader.GetDateTime(2).ToString(Constants.InternalDateTimeFormat, CultureInfo.InvariantCulture);
                                    entity.LastModified = reader.GetDateTime(3).ToString(Constants.InternalDateTimeFormat, CultureInfo.InvariantCulture);
                                    entity.Version = reader.GetInt32(4);

                                    if (!reader.IsDBNull(5))
                                    {
                                        entity.Locked = reader.GetString(5);
                                    }

                                    entity.ChangeSet = reader.GetInt32(6);

                                    if (!reader.IsDBNull(7))
                                    {
                                        entity.FieldSetId = reader.GetString(7);
                                    }

                                    entity.CreatedBy = reader.GetString(8);
                                    entity.ModifiedBy = reader.GetString(9);

                                    if (!reader.IsDBNull(10))
                                    {
                                        entity.MainPictureId = reader.GetInt32(10);
                                    }

                                    if (!reader.IsDBNull(11))
                                    {
                                        string data = string.Empty;

                                        if (!reader.IsDBNull(12))
                                        {
                                            data = reader.GetString(12);
                                        }

                                        entity.DisplayName = new DtoField
                                        {
                                            EntityId = entity.Id,
                                            LastModified = entity.LastModified,
                                            FieldTypeId = reader.GetString(11),
                                            Data = data
                                        };
                                    }

                                    if (!reader.IsDBNull(13))
                                    {
                                        string data = string.Empty;

                                        if (!reader.IsDBNull(14))
                                        {
                                            data = reader.GetString(14);
                                        }

                                        entity.DisplayDescription = new DtoField
                                        {
                                            EntityId = entity.Id,
                                            LastModified = entity.LastModified,
                                            FieldTypeId = reader.GetString(13),
                                            Data = data
                                        };
                                    }

                                    if (!reader.IsDBNull(19))
                                    {
                                        entity.Completeness = reader.GetInt32(19);
                                    }

                                    if (!reader.IsDBNull(20))
                                    {
                                        entity.MainPictureUrl = reader.GetString(20);
                                    }

                                    if (!reader.IsDBNull(21))
                                    {
                                        entity.Segment = new DtoSegment();
                                        entity.Segment.Id = reader.GetInt32(21);
                                    }

                                    if (!reader.IsDBNull(22))
                                    {
                                        entity.Segment.Name = reader.GetString(22);
                                    }

                                    if (!reader.IsDBNull(23))
                                    {
                                        entity.Segment.Description = reader.GetString(23);
                                    }

                                    entity.LoadLevel = LoadLevel.DataOnly;

                                    entities.Add(id, entity);
                                }
                                else
                                {
                                    entity = entities[id];
                                }

                                DtoField field = new DtoField();

                                field.FieldTypeId = reader.GetString(15);

                                if (!reader.IsDBNull(16))
                                {
                                    field.Data = reader.GetString(16);
                                }

                                field.EntityId = entity.Id;

                                if (reader.IsDBNull(17))
                                {
                                    field.LastModified = entity.LastModified;
                                }
                                else
                                {
                                    field.LastModified = reader.GetDateTime(17).ToString(Constants.InternalDateTimeFormat, CultureInfo.InvariantCulture);
                                }

                                if (reader.IsDBNull(18))
                                {
                                    field.Revision = 0;
                                }
                                else
                                {
                                    field.Revision = reader.GetInt32(18);
                                }

                                // check for duplicate field row
                                if (!entityFields.ContainsKey($"{id}_{field.FieldTypeId}"))
                                {
                                    entityFields.Add($"{id}_{field.FieldTypeId}", "");
                                    entity.Fields.Add(field);
                                }
                            }

                            reader.Close();
                        }

                        connection.Close();

                        if (!entities.Any())
                        {
                            return new List<DtoEntity>();
                        }

                        Dictionary<string, List<string>> fieldTypeIds = this.GetAllFieldTypeIdsForEntityTypes(entities.Values.Select(e => e.EntityTypeId).Distinct().ToList());

                        foreach (DtoEntity entity in entities.Values)
                        {
                            if (entity.Fields.Count == fieldTypeIds[entity.EntityTypeId].Count)
                            {
                                continue;
                            }

                            foreach (string fieldTypeId in fieldTypeIds[entity.EntityTypeId])
                            {
                                // if already added field
                                if (entityFields.ContainsKey($"{entity.Id}_{fieldTypeId}"))
                                {
                                    continue;
                                }

                                DtoField field = new DtoField();
                                field.Revision = 0;
                                field.FieldTypeId = fieldTypeId;
                                field.EntityId = entity.Id;
                                field.LastModified = entity.DateCreated;

                                entity.Fields.Add(field);
                            }
                        }
                    }
                    catch (Exception ex) when (!(ex is OperationCanceledException))
                    {
                        if (connection.State != ConnectionState.Closed)
                        {
                            connection.Close();
                        }

                        Log.Error(ex, "An unexpected error occurred when getting list of entities with data");
                        throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting list of entities with data", ex);
                    }
                }
            } while (batchCounter < totalIdsCount);

            return entities.Values.ToList();
        }

        public async Task<List<DtoEntity>> GetEntitiesWithDataAsync(List<int> list, CancellationToken cancellationToken)
        {
            if (!list.Any())
            {
                return new List<DtoEntity>();
            }

            var entities = new Dictionary<int, DtoEntity>();
            var entityFields = new Dictionary<string, string>();
            var totalIdsCount = list.Count;
            var batchCounter = 0;

            do
            {
                cancellationToken.ThrowIfCancellationRequested();

                var batchIds = list.Skip(batchCounter).Take(4000).ToList();
                batchCounter += batchIds.Count;

                using var connection = new SqlConnection(this.ConnectionString);
                try
                {
                    var segmentIdsInClause = this.permittedSegmentIdsClause();

                    if (!string.IsNullOrWhiteSpace(segmentIdsInClause))
                    {
                        segmentIdsInClause = "Entity." + segmentIdsInClause;
                    }

                    var query = new CommandDefinition(
                        $@"SELECT Entity.Id, Entity.EntityTypeId, Entity.DateCreated, Entity.LastModified, Entity.Version, Entity.Locked, Entity.ChangeSet, Entity.FieldSetId, Entity.CreatedBy, 
                                   Entity.ModifiedBy, Entity.MainPicture, Entity.DisplayNameFieldTypeId, Entity.DisplayName, Entity.DisplayDescriptionFieldTypeId, Entity.DisplayDescription, 
                                   Field.FieldTypeId, Field.Value, Field.LastModified, Field.Revision, Entity.Completeness, Entity.MainPictureUrl, Entity.ContentSegmentationId, C.SegmentName, C.SegmentDescription 
                            FROM dbo.Entity 
                            LEFT OUTER JOIN Field ON Entity.Id = Field.EntityId 
                            INNER JOIN ContentSegmentation C on Entity.ContentSegmentationId = C.ID 
                            WHERE dbo.Entity.PendingDelete IS NULL
                            AND {segmentIdsInClause} Entity.Id IN @Ids ",
                        new { Ids = batchIds },
                        commandTimeout: 600,
                        cancellationToken: cancellationToken);

                    var result = await connection.QueryAsync(query);

                    foreach (var row in result)
                    {
                        cancellationToken.ThrowIfCancellationRequested();
                        int id = row.Id;

                        if (!entities.ContainsKey(id))
                        {
                            var entity = new DtoEntity
                            {
                                Id = id,
                                Fields = new List<DtoField>(),
                                EntityTypeId = row.EntityTypeId,
                                DateCreated = row.DateCreated.ToString(Constants.InternalDateTimeFormat, CultureInfo.InvariantCulture),
                                LastModified = row.LastModified.ToString(Constants.InternalDateTimeFormat, CultureInfo.InvariantCulture),
                                Version = row.Version,
                                Locked = row.Locked,
                                ChangeSet = row.ChangeSet,
                                FieldSetId = row.FieldSetId,
                                CreatedBy = row.CreatedBy,
                                ModifiedBy = row.ModifiedBy,
                                MainPictureId = row.MainPicture,
                                DisplayName = new DtoField
                                {
                                    EntityId = id,
                                    LastModified = row.LastModified.ToString(Constants.InternalDateTimeFormat, CultureInfo.InvariantCulture),
                                    FieldTypeId = row.DisplayNameFieldTypeId,
                                    Data = row.DisplayName
                                },
                                DisplayDescription = new DtoField
                                {
                                    EntityId = id,
                                    LastModified = row.LastModified.ToString(Constants.InternalDateTimeFormat, CultureInfo.InvariantCulture),
                                    FieldTypeId = row.DisplayDescriptionFieldTypeId,
                                    Data = row.DisplayDescription
                                },
                                Completeness = row.Completeness,
                                MainPictureUrl = row.MainPictureUrl,
                                Segment = new DtoSegment
                                {
                                    Id = row.ContentSegmentationId,
                                    Name = row.SegmentName,
                                    Description = row.SegmentDescription
                                },
                                LoadLevel = LoadLevel.DataOnly
                            };

                            entities.Add(id, entity);
                        }

                        var field = new DtoField
                        {
                            FieldTypeId = row.FieldTypeId,
                            Data = row.Value,
                            EntityId = id,
                            LastModified = row.LastModified?.ToString(Constants.InternalDateTimeFormat, CultureInfo.InvariantCulture) ?? entities[id].LastModified,
                            Revision = row.Revision ?? 0
                        };

                        // check for duplicate field row
                        if (!entityFields.ContainsKey($"{id}_{field.FieldTypeId}"))
                        {
                            entityFields.Add($"{id}_{field.FieldTypeId}", string.Empty);
                            entities[id].Fields.Add(field);
                        }
                    }

                    if (!entities.Any())
                    {
                        return new List<DtoEntity>();
                    }

                    var fieldTypeIds = await this.GetAllFieldTypeIdsForEntityTypesAsync(entities.Values.Select(e => e.EntityTypeId).Distinct().ToList());

                    foreach (var entity in entities.Values)
                    {
                        if (entity.Fields.Count == fieldTypeIds[entity.EntityTypeId].Count)
                        {
                            continue;
                        }

                        foreach (var fieldTypeId in fieldTypeIds[entity.EntityTypeId])
                        {
                            // if already added field
                            if (entityFields.ContainsKey($"{entity.Id}_{fieldTypeId}"))
                            {
                                continue;
                            }

                            var field = new DtoField
                            {
                                Revision = 0,
                                FieldTypeId = fieldTypeId,
                                EntityId = entity.Id,
                                LastModified = entity.DateCreated
                            };

                            entity.Fields.Add(field);
                        }
                    }
                }
                catch (Exception ex) when (!(ex is OperationCanceledException))
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting list of entities with data");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting list of entities with data", ex);
                }
            } while (batchCounter < totalIdsCount);

            return entities.Values.ToList();
        }

        public async Task<IEnumerable<DtoEntity>> GetEntitiesAsync(IEnumerable<int> ids)
        {
            var entities = new List<DtoEntity>();

            if (!ids.Any())
            {
                return new List<DtoEntity>();
            }

            var totalIdsCount = ids.Count();
            var batchCounter = 0;

            do
            {
                var batchIds = ids.Skip(batchCounter).Take(10000).ToList();
                batchCounter += batchIds.Count;

                var commaSeparatedIds = string.Join(",", batchIds.Select(n => n.ToString()));

                using (var connection = new SqlConnection(this.ConnectionString))
                {
                    try
                    {
                        await using var command = connection.CreateCommand();
                        var segmentIdsInClause = permittedSegmentIdsClause();

                        command.CommandText = "SELECT Id, EntityTypeId, DateCreated, LastModified, [Version], Locked, " +
                            "ChangeSet, FieldSetId, CreatedBy, ModifiedBy, [MainPicture], [DisplayNameFieldTypeId], " +
                            "[DisplayName], [DisplayDescriptionFieldTypeId], [DisplayDescription], Completeness, MainPictureUrl, ContentSegmentationId " +
                            $"FROM Entity WHERE {segmentIdsInClause} Id IN ({commaSeparatedIds}) AND PendingDelete IS NULL";

                        command.CommandTimeout = 3000;
                        await connection.OpenAsync();

                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            while (await reader.ReadAsync())
                            {
                                var entity = new DtoEntity();
                                entity.Id = reader.GetInt32(0);
                                entity.EntityTypeId = reader.GetString(1);
                                entity.DateCreated = reader.GetDateTime(2).ToString(Constants.InternalDateTimeFormat, CultureInfo.InvariantCulture);
                                entity.LastModified = reader.GetDateTime(3).ToString(Constants.InternalDateTimeFormat, CultureInfo.InvariantCulture);
                                entity.Version = reader.GetInt32(4);

                                if (!reader.IsDBNull(5))
                                {
                                    entity.Locked = reader.GetString(5);
                                }

                                entity.ChangeSet = reader.GetInt32(6);

                                if (!reader.IsDBNull(7))
                                {
                                    entity.FieldSetId = reader.GetString(7);
                                }

                                entity.CreatedBy = reader.GetString(8);
                                entity.ModifiedBy = reader.GetString(9);

                                if (!reader.IsDBNull(10))
                                {
                                    entity.MainPictureId = reader.GetInt32(10);
                                }

                                if (!reader.IsDBNull(11))
                                {
                                    var data = string.Empty;

                                    if (!reader.IsDBNull(12))
                                    {
                                        data = reader.GetString(12);
                                    }

                                    entity.DisplayName = new DtoField
                                    {
                                        EntityId = entity.Id,
                                        LastModified = entity.LastModified,
                                        FieldTypeId = reader.GetString(11),
                                        Data = data
                                    };
                                }

                                if (!reader.IsDBNull(13))
                                {
                                    var data = string.Empty;

                                    if (!reader.IsDBNull(14))
                                    {
                                        data = reader.GetString(14);
                                    }

                                    entity.DisplayDescription = new DtoField
                                    {
                                        EntityId = entity.Id,
                                        LastModified = entity.LastModified,
                                        FieldTypeId = reader.GetString(13),
                                        Data = data
                                    };
                                }

                                if (!reader.IsDBNull(15))
                                {
                                    entity.Completeness = reader.GetInt32(15);
                                }

                                if (!reader.IsDBNull(16))
                                {
                                    entity.MainPictureUrl = reader.GetString(16);
                                }

                                entity.Segment = new DtoSegment() { Id = reader.GetInt32(17) };

                                entity.LoadLevel = LoadLevel.Shallow;

                                entities.Add(entity);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.Error(ex, "An unexpected error occurred when getting list of entities");
                        throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting list of entities", ex);
                    }
                }

            }
            while (batchCounter < totalIdsCount);

            return entities;
        }

        public List<DtoEntity> GetEntities(List<int> ids)
        {
            List<DtoEntity> entities = new List<DtoEntity>();

            if (!ids.Any())
            {
                return new List<DtoEntity>();
            }

            int totalIdsCount = ids.Count;
            int batchCounter = 0;

            do
            {
                List<int> batchIds = ids.Skip(batchCounter).Take(10000).ToList();
                batchCounter += batchIds.Count;

                StringBuilder builder = new StringBuilder();

                builder.Append("(");
                builder.Append(string.Join(",", batchIds.Select(n => n.ToString())));
                string selectIn = builder.Append(")").ToString();

                using (SqlConnection connection = new SqlConnection(this.ConnectionString))
                {
                    try
                    {
                        SqlCommand command = connection.CreateCommand();

                        string segmentIdsInClause = permittedSegmentIdsClause();

                        command.CommandText = "SELECT Id, EntityTypeId, DateCreated, LastModified, [Version], Locked, " +
                            "ChangeSet, FieldSetId, CreatedBy, ModifiedBy, [MainPicture], [DisplayNameFieldTypeId], " +
                            "[DisplayName], [DisplayDescriptionFieldTypeId], [DisplayDescription], Completeness, MainPictureUrl, ContentSegmentationId " +
                            $"FROM Entity WHERE {segmentIdsInClause} Id IN " + selectIn + " AND PendingDelete IS NULL";

                        command.CommandTimeout = 3000;
                        connection.Open();

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                DtoEntity entity = new DtoEntity();
                                entity.Id = reader.GetInt32(0);
                                entity.EntityTypeId = reader.GetString(1);
                                entity.DateCreated = reader.GetDateTime(2).ToString(Constants.InternalDateTimeFormat, CultureInfo.InvariantCulture);
                                entity.LastModified = reader.GetDateTime(3).ToString(Constants.InternalDateTimeFormat, CultureInfo.InvariantCulture);
                                entity.Version = reader.GetInt32(4);

                                if (!reader.IsDBNull(5))
                                {
                                    entity.Locked = reader.GetString(5);
                                }

                                entity.ChangeSet = reader.GetInt32(6);

                                if (!reader.IsDBNull(7))
                                {
                                    entity.FieldSetId = reader.GetString(7);
                                }

                                entity.CreatedBy = reader.GetString(8);
                                entity.ModifiedBy = reader.GetString(9);

                                if (!reader.IsDBNull(10))
                                {
                                    entity.MainPictureId = reader.GetInt32(10);
                                }

                                if (!reader.IsDBNull(11))
                                {
                                    string data = string.Empty;

                                    if (!reader.IsDBNull(12))
                                    {
                                        data = reader.GetString(12);
                                    }

                                    entity.DisplayName = new DtoField
                                    {
                                        EntityId = entity.Id,
                                        LastModified = entity.LastModified,
                                        FieldTypeId = reader.GetString(11),
                                        Data = data
                                    };
                                }

                                if (!reader.IsDBNull(13))
                                {
                                    string data = string.Empty;

                                    if (!reader.IsDBNull(14))
                                    {
                                        data = reader.GetString(14);
                                    }

                                    entity.DisplayDescription = new DtoField
                                    {
                                        EntityId = entity.Id,
                                        LastModified = entity.LastModified,
                                        FieldTypeId = reader.GetString(13),
                                        Data = data
                                    };
                                }

                                if (!reader.IsDBNull(15))
                                {
                                    entity.Completeness = reader.GetInt32(15);
                                }

                                if (!reader.IsDBNull(16))
                                {
                                    entity.MainPictureUrl = reader.GetString(16);
                                }

                                entity.Segment = new DtoSegment() { Id = reader.GetInt32(17) };

                                entity.LoadLevel = LoadLevel.Shallow;

                                entities.Add(entity);
                            }

                            reader.Close();
                        }

                        connection.Close();
                    }
                    catch (Exception ex)
                    {
                        if (connection.State != ConnectionState.Closed)
                        {
                            connection.Close();
                        }

                        Log.Error(ex, "An unexpected error occurred when getting list of entities");
                        throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting list of entities", ex);
                    }
                }

            } while (batchCounter < totalIdsCount);

            return entities;
        }

        public async Task<List<DtoEntity>> GetEntitiesAsync(List<int> ids)
        {
            List<DtoEntity> entities = new List<DtoEntity>();

            if (!ids.Any())
            {
                return new List<DtoEntity>();
            }

            int totalIdsCount = ids.Count;
            int batchCounter = 0;

            do
            {
                List<int> batchIds = ids.Skip(batchCounter).Take(10000).ToList();
                batchCounter += batchIds.Count;

                StringBuilder builder = new StringBuilder();

                builder.Append("(");
                builder.Append(string.Join(",", batchIds.Select(n => n.ToString())));
                string selectIn = builder.Append(")").ToString();

                using (SqlConnection connection = new SqlConnection(this.ConnectionString))
                {
                    try
                    {
                        SqlCommand command = connection.CreateCommand();

                        string segmentIdsInClause = permittedSegmentIdsClause();

                        command.CommandText = "SELECT Id, EntityTypeId, DateCreated, LastModified, [Version], Locked, " +
                            "ChangeSet, FieldSetId, CreatedBy, ModifiedBy, [MainPicture], [DisplayNameFieldTypeId], " +
                            "[DisplayName], [DisplayDescriptionFieldTypeId], [DisplayDescription], Completeness, MainPictureUrl, ContentSegmentationId " +
                            $"FROM Entity WHERE {segmentIdsInClause} Id IN " + selectIn + " AND PendingDelete IS NULL";

                        command.CommandTimeout = 3000;
                        connection.Open();

                        using (SqlDataReader reader = await command.ExecuteReaderAsync())
                        {
                            while (reader.Read())
                            {
                                DtoEntity entity = new DtoEntity();
                                entity.Id = reader.GetInt32(0);
                                entity.EntityTypeId = reader.GetString(1);
                                entity.DateCreated = reader.GetDateTime(2).ToString(Constants.InternalDateTimeFormat, CultureInfo.InvariantCulture);
                                entity.LastModified = reader.GetDateTime(3).ToString(Constants.InternalDateTimeFormat, CultureInfo.InvariantCulture);
                                entity.Version = reader.GetInt32(4);

                                if (!reader.IsDBNull(5))
                                {
                                    entity.Locked = reader.GetString(5);
                                }

                                entity.ChangeSet = reader.GetInt32(6);

                                if (!reader.IsDBNull(7))
                                {
                                    entity.FieldSetId = reader.GetString(7);
                                }

                                entity.CreatedBy = reader.GetString(8);
                                entity.ModifiedBy = reader.GetString(9);

                                if (!reader.IsDBNull(10))
                                {
                                    entity.MainPictureId = reader.GetInt32(10);
                                }

                                if (!reader.IsDBNull(11))
                                {
                                    string data = string.Empty;

                                    if (!reader.IsDBNull(12))
                                    {
                                        data = reader.GetString(12);
                                    }

                                    entity.DisplayName = new DtoField
                                    {
                                        EntityId = entity.Id,
                                        LastModified = entity.LastModified,
                                        FieldTypeId = reader.GetString(11),
                                        Data = data
                                    };
                                }

                                if (!reader.IsDBNull(13))
                                {
                                    string data = string.Empty;

                                    if (!reader.IsDBNull(14))
                                    {
                                        data = reader.GetString(14);
                                    }

                                    entity.DisplayDescription = new DtoField
                                    {
                                        EntityId = entity.Id,
                                        LastModified = entity.LastModified,
                                        FieldTypeId = reader.GetString(13),
                                        Data = data
                                    };
                                }

                                if (!reader.IsDBNull(15))
                                {
                                    entity.Completeness = reader.GetInt32(15);
                                }

                                if (!reader.IsDBNull(16))
                                {
                                    entity.MainPictureUrl = reader.GetString(16);
                                }

                                entity.Segment = new DtoSegment() { Id = reader.GetInt32(17) };

                                entity.LoadLevel = LoadLevel.Shallow;

                                entities.Add(entity);
                            }

                            reader.Close();
                        }

                        connection.Close();
                    }
                    catch (Exception ex)
                    {
                        if (connection.State != ConnectionState.Closed)
                        {
                            connection.Close();
                        }

                        Log.Error(ex, "An unexpected error occurred when getting list of entities");
                        throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting list of entities", ex);
                    }
                }

            } while (batchCounter < totalIdsCount);

            return entities;
        }

        public int? GetEntityIdByUniqueValue(string fieldTypeId, string value)
        {
            if (string.IsNullOrWhiteSpace(fieldTypeId) || string.IsNullOrWhiteSpace(value))
            {
                return null;
            }

            int? entityId = null;
            var unicodeHash = GetMd5(value);
            var utf8Hash = GetMd5(value, useUtfEightEncoding: true);

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    entityId = connection.QuerySingleOrDefault<int?>(@"SELECT TOP 1 f.EntityId 
                                                            FROM Field f 
                                                            INNER JOIN Entity e
                                                            ON f.EntityId = e.Id
                                                            WHERE e.PendingDelete IS NULL                                                            
                                                            AND (f.UniqueHash = @UnicodeHash OR f.UniqueHash = @Utf8Hash)
                                                            AND f.Value = @Value 
                                                            AND f.FieldTypeId = @FieldTypeId",
                    new { UnicodeHash = unicodeHash, Utf8Hash = utf8Hash, Value = value, FieldTypeId = fieldTypeId });
                }
                catch (Exception ex)
                {
                    Log.Error(ex, "An unexpected error occurred when getting unique entity id");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting unique entity id", ex);
                }
            }

            return entityId;
        }

        public void UpdateEntityChangeSet(int entityId, List<Field> updatedFields)
        {
            DtoEntity entity = context.DataPersistance.GetEntity(entityId);

            DateTime modified = DateTime.UtcNow;
            int updatedChangeSet = entity.ChangeSet + 1;

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "INSERT INTO EntityHistory (EntityId, ChangeSet, ModifiedDate, Username, Action) VALUES (@EntityId, @ChangeSet, @ModifiedDate, @Username, @Action) SET @Id = SCOPE_IDENTITY()";

                    command.Parameters.AddWithValue("@EntityId", entityId);
                    command.Parameters.AddWithValue("@ChangeSet", entity.ChangeSet);
                    command.Parameters.AddWithValue("@ModifiedDate", modified);
                    command.Parameters.AddWithValue("@Username", this.context.Username);
                    command.Parameters.AddWithValue("@Action", "FieldUpdate");

                    SqlParameter idParameter = new SqlParameter("@Id", SqlDbType.Int);
                    idParameter.Direction = ParameterDirection.Output;
                    command.Parameters.Add(idParameter);

                    SqlCommand command2 = connection.CreateCommand();

                    command2.CommandText = "UPDATE Entity SET ChangeSet = @ChangeSet, LastModified = @LastModified, ModifiedBy = @ModifiedBy WHERE Id = @EntityId";
                    command2.Parameters.AddWithValue("@ChangeSet", updatedChangeSet);
                    command2.Parameters.AddWithValue("@LastModified", modified);
                    command2.Parameters.AddWithValue("@EntityId", entityId);
                    command2.Parameters.AddWithValue("@ModifiedBy", this.context.Username);

                    connection.Open();

                    command.ExecuteNonQuery();

                    int entityHistoryId = (int)idParameter.Value;

                    command2.ExecuteNonQuery();

                    connection.Close();

                    SqlCommand command3 = connection.CreateCommand();

                    StringBuilder query = new StringBuilder();

                    query.Append("INSERT INTO EntityHistoryRevisions (EntityHistoryId, FieldTypeId, Revision, EntityId) VALUES ");

                    int counter = 0;

                    foreach (Field field in updatedFields)
                    {
                        query.Append(string.Format("(@EntityHistoryId{0}, @FieldTypeId{0}, @Revision{0}, @EntityId{0}),", counter));

                        command3.Parameters.AddWithValue("@EntityHistoryId" + counter, entityHistoryId);
                        command3.Parameters.AddWithValue("@FieldTypeId" + counter, field.FieldType.Id);
                        command3.Parameters.AddWithValue("@Revision" + counter, field.Revision);
                        command3.Parameters.AddWithValue("@EntityId" + counter, field.EntityId);

                        counter++;
                    }

                    // remove trailing ','
                    command3.CommandText = query.ToString(0, query.Length - 1);

                    connection.Open();

                    command3.ExecuteNonQuery();

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when saving field revison history");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when saving field revison history", ex);
                }
            }
        }

        public DtoEntity SetEntityFieldSet(int entityId, string fieldSetId)
        {
            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();
                    command.CommandText = "UPDATE Entity SET FieldSetId = @FieldSetId WHERE Id = @EntityId";

                    command.Parameters.AddWithValue("@EntityId", entityId);

                    if (string.IsNullOrWhiteSpace(fieldSetId))
                    {
                        command.Parameters.AddWithValue("@FieldSetId", DBNull.Value);
                    }
                    else
                    {
                        command.Parameters.AddWithValue("@FieldSetId", fieldSetId);
                    }

                    connection.Open();
                    command.ExecuteNonQuery();
                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when unlocking entity");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when unlocking entity", ex);
                }
            }

            return context.DataPersistance.GetEntity(entityId);
        }

        public List<int> GetAllEntityIdsForEntityType(string entityTypeId)
            => GetAllEntityIdsForEntityType(entityTypeId, CancellationToken.None);

        public List<int> GetAllEntityIdsForEntityType(string entityTypeId, CancellationToken cancellationToken)
        {
            List<int> ids = new List<int>();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();
                    _ = cancellationToken.Register(() => {
                        command.Cancel();
                        cancellationToken.ThrowIfCancellationRequested();
                    });

                    string segmentIdsInClause = permittedSegmentIdsClause();

                    command.CommandText = $"SELECT Id FROM Entity WHERE {segmentIdsInClause} EntityTypeId = @EntityTypeId AND PendingDelete IS NULL";
                    command.Parameters.AddWithValue("@EntityTypeId", entityTypeId);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            ids.Add(reader.GetInt32(0));
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex) when (!(ex is OperationCanceledException))
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting all entity ids for entity type " + entityTypeId);
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting all entity ids for entity type " + entityTypeId, ex);
                }
            }

            return ids;
        }

        public bool SetEntityCompleteness(int entityId, int? completeness)
        {
            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();
                    command.CommandText = "UPDATE Entity SET Completeness = @Completeness WHERE Id = @EntityId";
                    command.CommandTimeout = 60;

                    if (completeness.HasValue)
                    {
                        command.Parameters.AddWithValue("@Completeness", completeness.Value);
                    }
                    else
                    {
                        command.Parameters.AddWithValue("@Completeness", DBNull.Value);
                    }

                    command.Parameters.AddWithValue("@EntityId", entityId);

                    connection.Open();
                    command.ExecuteNonQuery();
                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when setting entity completeness");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when setting entity completeness", ex);
                }
            }

            return true;
        }

        public async Task<bool> SetEntityCompletenessAsync(int entityId, int? completeness)
        {
            using (var connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    await using var command = connection.CreateCommand();
                    command.CommandText = "UPDATE Entity SET Completeness = @Completeness WHERE Id = @EntityId";
                    command.CommandTimeout = 60;

                    if (completeness.HasValue)
                    {
                        command.Parameters.AddWithValue("@Completeness", completeness.Value);
                    }
                    else
                    {
                        command.Parameters.AddWithValue("@Completeness", DBNull.Value);
                    }

                    command.Parameters.AddWithValue("@EntityId", entityId);

                    await connection.OpenAsync();
                    await command.ExecuteNonQueryAsync();
                }
                catch (Exception ex)
                {
                    Log.Error(ex, "An unexpected error occurred when setting entity completeness");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when setting entity completeness", ex);
                }
            }

            return true;
        }

        public void ReCalculateDisplayValuesForEntity(int entityId, List<Field> updatedFields)
        {
            Field displayField = updatedFields.FirstOrDefault(f => f.FieldType.IsDisplayName);

            if (displayField != null)
            {
                using (SqlConnection connection = new SqlConnection(this.ConnectionString))
                {
                    try
                    {
                        SqlCommand command = connection.CreateCommand();

                        command.CommandText = "UPDATE Entity SET DisplayNameFieldTypeId = @DisplayNameFieldTypeId, DisplayName = @DisplayName WHERE Id = @EntityId";
                        command.Parameters.AddWithValue("@EntityId", entityId);

                        command.Parameters.AddWithValue("@DisplayNameFieldTypeId", displayField.FieldType.Id);

                        if (displayField.IsEmpty())
                        {
                            command.Parameters.AddWithValue("@DisplayName", DBNull.Value);
                        }
                        else
                        {
                            if (displayField.Data is DateTime)
                            {
                                command.Parameters.AddWithValue("@DisplayName", ((DateTime)displayField.Data).ToString("yyyy-MM-dd HH:mm:ss"));
                            }
                            else if (displayField.Data is LocaleString)
                            {
                                command.Parameters.AddWithValue("@DisplayName", JsonConvert.SerializeObject(displayField.Data));
                            }
                            else
                            {
                                command.Parameters.AddWithValue("@DisplayName", displayField.Data);
                            }
                        }

                        connection.Open();

                        command.ExecuteNonQuery();

                        connection.Close();
                    }
                    catch (Exception ex)
                    {
                        if (connection.State != ConnectionState.Closed)
                        {
                            connection.Close();
                        }

                        Log.Error(ex, "An unexpected error occurred when updating display field");
                        throw ErrorUtility.GetDataAccessException("An unexpected error occurred when updating display field", ex);
                    }
                }
            }

            Field descriptionField = updatedFields.FirstOrDefault(f => f.FieldType.IsDisplayDescription);

            if (descriptionField != null)
            {
                using (SqlConnection connection = new SqlConnection(this.ConnectionString))
                {
                    try
                    {
                        SqlCommand command = connection.CreateCommand();

                        command.CommandText = "UPDATE Entity SET DisplayDescriptionFieldTypeId = @DisplayDescriptionFieldTypeId, DisplayDescription = @DisplayDescription WHERE Id = @EntityId";
                        command.Parameters.AddWithValue("@EntityId", entityId);

                        command.Parameters.AddWithValue("@DisplayDescriptionFieldTypeId", descriptionField.FieldType.Id);

                        if (descriptionField.IsEmpty())
                        {
                            command.Parameters.AddWithValue("@DisplayDescription", DBNull.Value);
                        }
                        else
                        {
                            if (descriptionField.Data is DateTime)
                            {
                                command.Parameters.AddWithValue("@DisplayDescription", ((DateTime)descriptionField.Data).ToString("yyyy-MM-dd HH:mm:ss"));
                            }
                            else if (descriptionField.Data is LocaleString)
                            {
                                command.Parameters.AddWithValue("@DisplayDescription", JsonConvert.SerializeObject(descriptionField.Data));
                            }
                            else
                            {
                                command.Parameters.AddWithValue("@DisplayDescription", descriptionField.Data);
                            }
                        }

                        connection.Open();

                        command.ExecuteNonQuery();

                        connection.Close();
                    }
                    catch (Exception ex)
                    {
                        if (connection.State != ConnectionState.Closed)
                        {
                            connection.Close();
                        }

                        Log.Error(ex, "An unexpected error occurred when updating display field");
                        throw ErrorUtility.GetDataAccessException("An unexpected error occurred when updating display field", ex);
                    }
                }
            }
        }

        public bool DeleteEntity(int id)
        {
            using var connection = new SqlConnection(this.ConnectionString);
            try
            {
                var command = connection.CreateCommand();
                command.CommandTimeout = 3000;
                command.CommandText = "DELETE FROM WorkAreaFolderEntities WITH (ROWLOCK) WHERE EntityId = @Id;" +
                                      "DELETE FROM CompletenessEntityState WITH (ROWLOCK) WHERE EntityId = @Id;" +
                                      "DELETE FROM EntityComment WITH (ROWLOCK) WHERE EntityId = @Id;" +
                                      "DELETE FROM Field WITH (ROWLOCK) WHERE EntityId = @Id;" +
                                      "DELETE FROM Link WITH (ROWLOCK) WHERE SourceEntityId = @Id OR TargetEntityId = @Id;" +
                                      "UPDATE Link WITH (ROWLOCK) SET LinkEntityId = NULL WHERE LinkEntityId = @Id;" +
                                      "DELETE FROM Entity WITH (ROWLOCK) WHERE Id = @Id;" +
                                      "DELETE FROM EntityHistoryRevisions WITH (ROWLOCK) WHERE EntityId = @Id;" +
                                      "DELETE FROM EntityHistory WITH (ROWLOCK) WHERE EntityId = @Id;" +
                                      "DELETE FROM FieldRevisionHistory WITH (ROWLOCK) WHERE EntityId = @Id";

                command.Parameters.AddWithValue("@Id", id);

                connection.Open();
                command.ExecuteNonQuery();
                connection.Close();
            }
            catch (Exception ex)
            {
                if (connection.State != ConnectionState.Closed)
                {
                    connection.Close();
                }

                Log.Error(ex, "An unexpected error occurred when deleting entity " + id);
                throw ErrorUtility.GetDataAccessException( "An unexpected error occurred when deleting  entity " + id, ex);
            }

            return true;
        }

        public bool EntityExists(int entityId)
        {
            bool result = false;

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "SELECT Id FROM [Entity] WHERE Id = @EntityId AND PendingDelete IS NULL";
                    command.Parameters.AddWithValue("@EntityId", entityId);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            result = true;
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when checking if entity " + entityId + " exists");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when checking if entity " + entityId + " exists", ex);
                }
            }

            return result;
        }

        private string permittedSegmentIdsClause()
        {
            // permitted segmentIds
            List<int> segmentIds = contentSegmentProvider?.GetPermittedSegmentIds();

            string segmentIdsInClause = "";

            if (segmentIds != null && segmentIds.Any())
            {
                string segmentIdsStr = string.Join(",", segmentIds);
                segmentIdsInClause = $"ContentSegmentationId IN ({segmentIdsStr}) AND";
            }

            return segmentIdsInClause;
        }

        public IDictionary<int, IList<SyndicationRelatedEntityFieldValue>> GetRelatedEntityFields(
            string entityName,
            string field,
            string[] linkTypeIds,
            int[] entityLinkPosition,
            string language)
        {
            var relatedEntityFields = new Dictionary<int, IList<SyndicationRelatedEntityFieldValue>>();

            using var connection = new SqlConnection(this.ConnectionString);
            try
            {
                string[] relationSelector = { "SourceEntityId", "TargetEntityId" };
                var command = connection.CreateCommand();

                for (var i = 0; i < linkTypeIds.Length; i++)
                {
                    _ = command.Parameters.Add("@LinkType" + i, SqlDbType.VarChar, 64).Value = linkTypeIds[i];
                }

                command.CommandText = $@"SELECT TargetLink0.{relationSelector[entityLinkPosition[0] == 0 ? 1 : 0]} as FinalEntityId,
                        Field.Value as Field, FieldType.DataType as FieldDataType, Entity.Id as EntityId, STRING_AGG(cvalue.[Value], ';') AS CvlValue
                        FROM Entity
                        INNER JOIN Field  ON Field.EntityId = Entity.Id And Field.FieldTypeId = @field
                        INNER JOIN FieldType ON FieldType.Id = Field.FieldTypeId
                        INNER JOIN Link TargetLink{linkTypeIds.Length - 1}
                        ON TargetLink{linkTypeIds.Length - 1}.LinkTypeId = @LinkType{linkTypeIds.Length - 1} 
                        AND TargetLink{linkTypeIds.Length - 1}.{relationSelector[entityLinkPosition[linkTypeIds.Length - 1]]} = Entity.Id 
                        LEFT JOIN CVLKey AS ckey
                            ON EXISTS (
                                SELECT 1 FROM STRING_SPLIT(CONVERT(nvarchar(max), Field.Value), ';') AS SplitValues
                                WHERE SplitValues.value = ckey.[Key]
                            )
                            AND ckey.CVLId = FieldType.CVLId
                            AND FieldType.DataType = '{DataType.CVL}'
                        LEFT JOIN CVLValue AS cvalue ON cvalue.CVLKeyId = ckey.Id ";

                if (!string.IsNullOrEmpty(language))
                {
                    command.CommandText += " AND cvalue.Language = @language ";
                    _ = command.Parameters.Add("@language", SqlDbType.VarChar, 64).Value = language;
                }

                for (var i = linkTypeIds.Length - 2; i >= 0; i--)
                {
                    command.CommandText += $" INNER JOIN Link TargetLink{i} ON TargetLink{i}.LinkTypeId = @LinkType{i} "
                                           + $"AND TargetLink{i}.{relationSelector[entityLinkPosition[i]]} = TargetLink{i + 1}.{relationSelector[entityLinkPosition[i + 1] == 0 ? 1 : 0]} ";
                }

                _ = command.Parameters.Add("@field", SqlDbType.VarChar, 64).Value = field;
                command.CommandText += $" GROUP BY TargetLink0.{relationSelector[entityLinkPosition[0] == 0 ? 1 : 0]}, Field.Value,  FieldType.DataType, Entity.Id;";
                command.CommandTimeout = (int)TimeSpan.FromMinutes(5).TotalSeconds;

                connection.Open();
                using var reader = command.ExecuteReader();
                while (reader.Read())
                {
                    if (reader.IsDBNull(reader.GetOrdinal("FinalEntityId")))
                    {
                        break;
                    }

                    if (reader.IsDBNull(reader.GetOrdinal("Field")) ||
                        reader.IsDBNull(reader.GetOrdinal("FieldDataType")) ||
                        reader.IsDBNull(reader.GetOrdinal("EntityId")))
                    {
                        continue;
                    }

                    var dataType = reader.GetString(reader.GetOrdinal("FieldDataType"));
                    var finalEntityId = reader.GetInt32(reader.GetOrdinal("FinalEntityId"));
                    var entityId = reader.GetInt32(reader.GetOrdinal("EntityId"));

                    _ = relatedEntityFields.TryGetValue(finalEntityId, out var entityFields);
                    if (entityFields is null)
                    {
                        entityFields = new List<SyndicationRelatedEntityFieldValue>();

                        relatedEntityFields.Add(finalEntityId, entityFields);
                    }

                    var value = GetRelatedEntityFieldValue(dataType, language, entityId, reader);
                    entityFields.Add(value);
                }
            }
            catch (Exception ex)
            {
                var message = $"An unexpected error occurred when populating dictionary in {nameof(this.GetRelatedEntityFields)}";
                Log.Error(ex, message);
                throw ErrorUtility.GetDataAccessException(message, ex);
            }

            return relatedEntityFields;
        }

        private static SyndicationRelatedEntityFieldValue GetRelatedEntityFieldValue(string dataType, string language, int entityId, SqlDataReader reader)
        {
            string value;

            if (dataType == DataType.LocaleString)
            {
                var fieldData = reader.GetString(reader.GetOrdinal("Field"));
                value = JsonConvert.DeserializeObject<LocaleString>(fieldData)[new CultureInfo(language)];
            }
            else if (dataType == DataType.CVL)
            {
                value = (string)Convert.ChangeType(reader["CvlValue"], typeof(string));
            }
            else
            {
                value = (string)Convert.ChangeType(reader["Field"], typeof(string));
            }

            return new SyndicationRelatedEntityFieldValue
            {
                EntityId = entityId,
                FieldValue = value
            };
        }

        #endregion

        #region Main Picture

        public void ReCalculateEntityMainPicture(int entityId, string entityTypeId)
        {
            if (entityTypeId == "Resource")
            {
                this.ReCalculateEntityMainPictureForResource(entityId);
                return;
            }

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "ReCalculateEntityMainPicture";
                    command.CommandType = CommandType.StoredProcedure;
                    command.Parameters.AddWithValue("@EntityId", entityId);
                    command.Parameters.AddWithValue("@EntityTypeId", entityTypeId);

                    command.CommandTimeout = 600;

                    connection.Open();
                    command.ExecuteNonQuery();
                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when re-calculating main picture for entity " + entityId);
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when re-calculating main picture for entity " + entityId, ex);
                }
            }
        }

        #endregion

        #region Field

        public List<FieldRevision> GetFieldRevisions(int entityId, string fieldTypeId, int maxNumberOfRevisions)
        {
            var fieldRevisions = new List<FieldRevision>();

            var topMostEntries = maxNumberOfRevisions > 0 ? $"TOP {maxNumberOfRevisions}" : string.Empty;

            using (var connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    var fieldType = this.context.DataPersistance.GetFieldType(fieldTypeId);

                    if (fieldType == null)
                    {
                        return new List<FieldRevision>();
                    }

                    var multiValue = fieldType.Multivalue;

                    var command = connection.CreateCommand();

                    var segmentIdsInClause = this.permittedSegmentIdsClause();

#pragma warning disable CA2100 // Review SQL queries for security vulnerabilities
                    command.CommandText = $@"SELECT {topMostEntries} fhr.EntityId, fhr.FieldTypeId, fhr.Revision, fhr.Modified, ISNULL(fhr.Username, ''), fhr.Value, fhr.Language
                                            FROM FieldRevisionHistory AS fhr 
                                            INNER JOIN Entity ON Entity.Id = @EntityId
                                            WHERE {segmentIdsInClause} fhr.EntityId = @EntityId AND fhr.FieldTypeId = @FieldTypeId ORDER BY fhr.Revision DESC,Modified DESC";
#pragma warning restore CA2100 // Review SQL queries for security vulnerabilities

                    _ = command.Parameters.AddWithValue("@EntityId", entityId);
                    _ = command.Parameters.AddWithValue("@FieldTypeId", fieldTypeId);

                    connection.Open();

                    using (var reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            var revision = reader.GetInt32(2);
                            var modified = reader.GetDateTime(3);
                            var modifiedBy = reader.GetString(4);

                            var value = string.Empty;

                            if (!reader.IsDBNull(5))
                            {
                                value = reader.GetString(5);
                            }

                            var language = string.Empty;

                            if (!reader.IsDBNull(6))
                            {
                                language = reader.GetString(6);
                            }

                            if (multiValue)
                            {
                                var existing = fieldRevisions.FirstOrDefault(
                                        p => p.EntityId == entityId && p.FieldTypeId == fieldTypeId && p.Revision == revision);

                                if (existing != null)
                                {
                                    value = $"{existing.Data};{value}";
                                }

                                _ = fieldRevisions.Remove(existing);
                            }

                            var fieldRevision = new FieldRevision
                            {
                                EntityId = entityId,
                                FieldTypeId = fieldTypeId,
                                Revision = revision,
                                LastModified = modified,
                                LastModifiedBy = modifiedBy,
                                Data = value,
                                Language = language
                            };

                            fieldRevisions.Add(fieldRevision);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    this.context.Log(Remoting.Log.LogLevel.Error, "An unexpected error occurred when getting field revisions", ex);
                    throw ErrorUtility.GetDataAccessException(
                        "An unexpected error occurred when getting field revisions", ex);
                }
            }

            return fieldRevisions;
        }

        public IList<string> GetSearchHintFieldTypesByEntityId(string entityId)
        {
            if (string.IsNullOrEmpty(entityId))
            {
                throw new ArgumentNullException(nameof(entityId));
            }

            var searchHintFieldTypes = new List<string>();
            using (var connection = new SqlConnection(this.ConnectionString))
            {
                connection.Open();
                var command = connection.CreateCommand();
                command.CommandText =
                    "SELECT [EntityTypeHint_FieldType].[FieldTypeId] " +
                    "FROM [EntityTypeHint_FieldType] " +
                    "JOIN [EntityTypeHint] ON [EntityTypeHint_FieldType].[EntityTypeHintId] = [EntityTypeHint].[Id] " +
                    "WHERE [EntityTypeHint].[EntityTypeId] = @EntityTypeId AND [EntityTypeHint].[Unique] = 1";
                command.Parameters.AddWithValue("@EntityTypeId", entityId);

                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        searchHintFieldTypes.Add(reader.GetString(0));
                    }

                    reader.Close();
                }

                connection.Close();
            }

            return searchHintFieldTypes;
        }

        public bool FieldValueAlreadyExistsForFieldType(string fieldTypeId, object value)
        {
            bool result = false;

            if (value == null)
            {
                return false;
            }

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "SELECT TOP 1 EntityId FROM Field WHERE UniqueHash = CONVERT(binary(16), HASHBYTES('MD5', @Value)) AND Value = @Value AND FieldTypeId = @FieldTypeId";

                    command.Parameters.AddWithValue("@Value", value.ToString());
                    command.Parameters.Add("@FieldTypeId", SqlDbType.VarChar, 64).Value = fieldTypeId;

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        if (reader.HasRows)
                        {
                            result = true;
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting field");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting field", ex);
                }
            }

            return result;
        }

        public void UpdateFields(List<Field> fields)
        {
            DateTime now = DateTime.UtcNow;

            StringBuilder sb = new StringBuilder();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    int i = 0;

                    foreach (Field field in fields)
                    {
                        field.LastModified = now;

                        if (field.FieldType.Unique && !field.IsEmpty())
                        {
                            sb.Append(string.Format("UPDATE Field WITH (ROWLOCK) SET Value = @Value{0}, Revision = @Revision{0}, LastModified = @LastModified{0}, UniqueHash = CONVERT(binary(16), HASHBYTES('MD5', @Value{0})) WHERE EntityId = @EntityId{0} AND FieldTypeId = @FieldTypeId{0};", i));
                        }
                        else
                        {
                            sb.Append(string.Format("UPDATE Field WITH (ROWLOCK) SET Value = @Value{0}, Revision = @Revision{0}, LastModified = @LastModified{0}, UniqueHash = NULL WHERE EntityId = @EntityId{0} AND FieldTypeId = @FieldTypeId{0};", i));
                        }

                        command.Parameters.AddWithValue("@FieldTypeId" + i, field.FieldType.Id);

                        if (field.Data == null)
                        {
                            command.Parameters.AddWithValue("@Value" + i, DBNull.Value);
                        }
                        else if (field.Data is DateTime)
                        {
                            command.Parameters.AddWithValue("@Value" + i, ((DateTime)field.Data).ToString(Constants.InternalDateTimeFormat));
                        }
                        else if (field.Data is LocaleString)
                        {
                            command.Parameters.AddWithValue("@Value" + i, JsonConvert.SerializeObject(field.Data));
                        }
                        else
                        {
                            command.Parameters.AddWithValue("@Value" + i, field.Data.ToString());
                        }

                        command.Parameters.AddWithValue("@EntityId" + i, field.EntityId);

                        command.Parameters.AddWithValue("@Revision" + i, field.Revision);
                        command.Parameters.AddWithValue("@LastModified" + i, now);

                        i++;
                    }

                    command.CommandText = sb.ToString();

                    connection.Open();
                    command.ExecuteNonQuery();
                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when updating field values for entity");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when updating field values for entity", ex);
                }
            }
        }

        // This method should only be used for computing a digest of a unique field value that needs a "virtual unique constraint".
        // Migration from ODL brought along the old hashes, which were calculated over UTF-8-encoded values.
        // At the same time, for new entities, we've started using UTF-16 for calculating the values
        // That caused a bug of impossibility to edit old values, when searching by unique fields combos.
        // So now we're looking for both possible hashes.
        private static byte[] GetMd5(string input, bool useUtfEightEncoding = false)
        {
            using (var md5provider = new MD5CryptoServiceProvider())
            {
                var encoding = useUtfEightEncoding ? Encoding.UTF8.GetBytes(input) : Encoding.Unicode.GetBytes(input);

                byte[] bytes = md5provider.ComputeHash(encoding);
                return bytes;
            }
        }

        public void AddFields(List<Field> fields)
        {
            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                connection.Open();

                using (SqlCommand command = connection.CreateCommand())
                {
                    AddFieldsTransactional(fields, command);
                }
            }
        }

        /**
         * This method is to accommodate adding fields with optional SQL transaction handled by the caller (via SqlCommand) 
         */
        private void AddFieldsTransactional(List<Field> fields, SqlCommand command)
        {
            DateTime now = DateTime.UtcNow;

            var dataTable = new DataTable();
            dataTable.Columns.Add("FieldTypeId", typeof(string));
            dataTable.Columns.Add("Value", typeof(string));
            dataTable.Columns.Add("EntityId", typeof(int));
            dataTable.Columns.Add("LastModified", typeof(DateTime));
            dataTable.Columns.Add("UniqueHash", typeof(byte[]));

            foreach (Field field in fields)
            {
                byte[] uniqueHash = null;
                string data = field.Data?.ToString();

                if (field.FieldType.Unique && !field.IsEmpty())
                {
                    uniqueHash = GetMd5(field.Data?.ToString());
                }

                if (field.Data is DateTime)
                {
                    data = ((DateTime)field.Data).ToString(Constants.InternalDateTimeFormat);
                }
                else if (field.Data is LocaleString)
                {
                    data = JsonConvert.SerializeObject(field.Data);
                }
                else if (field.Data is Double)
                {
                    data = (double.Parse(data)).ToString("0.##############################");
                }

                dataTable.Rows.Add(new Object[]{
                    field.FieldType.Id,
                    data,
                    field.EntityId,
                    now,
                    uniqueHash
                });

            }

            try
            {
                string sql = "MERGE Field AS target" +
                " USING(SELECT FieldTypeId, Value, EntityId, LastModified, UniqueHash FROM @FieldTableType) AS source(FieldTypeId, Value, EntityId, LastModified, UniqueHash)" +
                " ON(target.EntityId = source.EntityId AND target.FieldTypeId = source.FieldTypeId)" +
                " WHEN MATCHED THEN" +
                " UPDATE SET FieldTypeId = source.FieldTypeId, Value = source.Value, EntityId = source.EntityId," +
                " Revision = Revision + 1, LastModified = source.LastModified, UniqueHash = source.UniqueHash" +
                " WHEN NOT MATCHED THEN" +
                " INSERT(FieldTypeId, Value, EntityId, Revision, LastModified, UniqueHash)" +
                " VALUES(source.FieldTypeId, source.Value, source.EntityId, 1, source.LastModified, source.UniqueHash);";

                command.CommandText = sql;

                command.Parameters.Clear();  // it is important to clear the Parameters, since the 'command' may have been used

                var param = command.Parameters.Add("@FieldTableType", SqlDbType.Structured);

                param.Value = dataTable;
                param.TypeName = "FieldTableType";

                int rowsAffected = command.ExecuteNonQuery();
            }
            catch (Exception ex)
            {
                Log.Error(ex, "An unexpected error occurred when adding field values");
                throw;
            }
        }

        public List<DtoField> GetFieldsForEntity(DtoEntity entity)
        {
            List<DtoField> fields = new List<DtoField>();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "SELECT dbo.FieldType.Id, dbo.Field.Value, dbo.Field.LastModified, dbo.Field.Revision, dbo.FieldType.DataType "
                        + "FROM dbo.FieldType LEFT OUTER JOIN "
                        + "dbo.Field ON dbo.FieldType.Id = dbo.Field.FieldTypeId AND dbo.Field.EntityId = @EntityId "
                        + "WHERE (dbo.FieldType.EntityTypeId = @EntityTypeId)"
                        + "ORDER BY dbo.FieldType.[Index]";

                    command.Parameters.AddWithValue("@EntityId", entity.Id);
                    command.Parameters.AddWithValue("@EntityTypeId", entity.EntityTypeId);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            DtoField field = new DtoField();

                            field.FieldTypeId = reader.GetString(0);

                            if (!reader.IsDBNull(1))
                            {
                                field.Data = reader.GetString(1);
                            }

                            field.EntityId = entity.Id;

                            if (reader.IsDBNull(2))
                            {
                                field.LastModified = entity.LastModified;
                            }
                            else
                            {
                                field.LastModified = reader.GetDateTime(2).ToString(Constants.InternalDateTimeFormat, CultureInfo.InvariantCulture);
                            }

                            if (reader.IsDBNull(3))
                            {
                                field.Revision = 0;
                            }
                            else
                            {
                                field.Revision = reader.GetInt32(3);
                            }

                            field.DataType = reader.GetString(4);

                            if (reader.GetString(4) == DataType.DateTime && !string.IsNullOrEmpty(field.Data))
                            {
                                field.Data = Convert.ToDateTime(field.Data).ToString(Constants.InternalDateTimeFormat, CultureInfo.InvariantCulture);
                            }

                            fields.Add(field);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting fields for entity " + entity.Id);
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting fields for entity " + entity.Id, ex);
                }
            }

            return fields;
        }

        public List<Field> GetFullFieldsForEntity(Entity entity)
        {
            List<Field> fields = new List<Field>();

            foreach (FieldType fieldType in entity.EntityType.FieldTypes)
            {
                fields.Add(new Field { EntityId = entity.Id, FieldType = fieldType, LastModified = entity.LastModified, Revision = 0 });
            }

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "SELECT [FieldTypeId], [Value], [LastModified], [Revision] FROM Field WHERE EntityId = @EntityId";

                    command.Parameters.AddWithValue("@EntityId", entity.Id);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            string fieldTypeId = reader.GetString(0);

                            Field field = fields.FirstOrDefault(f => f.FieldType.Id.Equals(fieldTypeId));

                            if (field == null)
                            {
                                continue;
                            }

                            if (!reader.IsDBNull(1))
                            {
                                field.Data = SetFieldData(reader.GetValue(1), field.FieldType.DataType);
                            }

                            if (!reader.IsDBNull(2))
                            {
                                field.LastModified = reader.GetDateTime(2);
                            }

                            if (!reader.IsDBNull(3))
                            {
                                field.Revision = reader.GetInt32(3);
                            }
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting fields for entity " + entity.Id);
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting fields for entity " + entity.Id, ex);
                }
            }

            return fields;
        }

        public List<DtoField> GetFields(int entityId, List<string> fieldTypeIds)
        {
            List<DtoField> requestedFields = new List<DtoField>();

            foreach (string fieldTypeId in fieldTypeIds)
            {
                if (!string.IsNullOrWhiteSpace(fieldTypeId))
                {
                    DtoField field = GetField(entityId, fieldTypeId);

                    if (field != null)
                    {
                        requestedFields.Add(field);
                    }
                }
            }

            return requestedFields;
        }

        public DtoField GetField(int entityId, string fieldTypeId)
        {
            if (string.IsNullOrEmpty(fieldTypeId))
            {
                return null;
            }

            DtoField field = null;

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "SELECT Value, FieldLastModified, Revision, DateCreated, DataType FROM ViewAllEntityFields WHERE Id = @EntityId AND FieldTypeId = @FieldTypeId";
                    command.Parameters.AddWithValue("@EntityId", entityId);
                    command.Parameters.AddWithValue("@FieldTypeId", fieldTypeId);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            field = new DtoField();

                            field.FieldTypeId = fieldTypeId;
                            field.EntityId = entityId;

                            if (!reader.IsDBNull(0))
                            {
                                field.Data = reader.GetString(0);
                            }

                            if (!reader.IsDBNull(1))
                            {
                                field.LastModified = reader.GetDateTime(1).ToString(Constants.InternalDateTimeFormat, CultureInfo.InvariantCulture);
                            }
                            else
                            {
                                field.LastModified = reader.GetDateTime(3).ToString(Constants.InternalDateTimeFormat, CultureInfo.InvariantCulture);
                            }

                            if (!reader.IsDBNull(2))
                            {
                                field.Revision = reader.GetInt32(2);
                            }
                            else
                            {
                                field.Revision = 0;
                            }

                            field.DataType = reader.GetString(4);

                            if (reader.GetString(4) == DataType.DateTime && !string.IsNullOrEmpty(field.Data))
                            {
                                field.Data = Convert.ToDateTime(field.Data).ToString(Constants.InternalDateTimeFormat);
                            }
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting field");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting field", ex);
                }
            }

            return field;
        }

        public Field GetFullField(int entityId, string entityTypeId, string fieldTypeId, DateTime entityCreated)
        {
            Field field = null;

            List<CultureInfo> languages = this.GetAllLanguages();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText =
                        "SELECT FieldType.Id, FieldType.DataType, Field.Value, Field.LastModified, Field.Revision, FieldType.CategoryId, FieldType.CVLId, FieldType.Mandatory, "
                        + "FieldType.[Unique], FieldType.DefaultValue, FieldType.Hidden, FieldType.ReadOnly, FieldType.IsDisplayName, FieldType.IsDisplayDescription, FieldType.ExcludeFromDefaultView, "
                        + "FieldType.Multivalue, FieldType.Name, FieldType.[Index], FieldType.EntityTypeId "
                        + "FROM Field RIGHT OUTER JOIN "
                        + "FieldType ON Field.FieldTypeId = FieldType.Id AND Field.EntityId = @EntityId "
                        + "WHERE FieldType.EntityTypeId = @EntityTypeId AND FieldType.Id = @FieldTypeId";

                    command.Parameters.AddWithValue("@EntityId", entityId);
                    command.Parameters.AddWithValue("@EntityTypeId", entityTypeId);
                    command.Parameters.AddWithValue("@FieldTypeId", fieldTypeId);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            field = new Field();

                            field.FieldType = new FieldType();
                            field.FieldType.Id = reader.GetString(0);
                            field.FieldType.EntityTypeId = reader.GetString(18);
                            field.EntityId = entityId;
                            field.FieldType.DataType = reader.GetString(1);

                            if (!reader.IsDBNull(2))
                            {
                                field.Data = SetFieldData(reader.GetValue(2), field.FieldType.DataType);
                            }

                            if (reader.IsDBNull(3))
                            {
                                field.LastModified = entityCreated;
                            }
                            else
                            {
                                field.LastModified = reader.GetDateTime(3);
                            }

                            if (reader.IsDBNull(4))
                            {
                                field.Revision = 0;
                            }
                            else
                            {
                                field.Revision = reader.GetInt32(4);
                            }

                            field.FieldType.CategoryId = reader.GetString(5);

                            if (!reader.IsDBNull(6))
                            {
                                field.FieldType.CVLId = reader.GetString(6);
                            }

                            field.FieldType.Mandatory = reader.GetBoolean(7);
                            field.FieldType.Unique = reader.GetBoolean(8);

                            if (!reader.IsDBNull(9))
                            {
                                field.FieldType.DefaultValue = reader.GetString(9);
                            }

                            field.FieldType.Hidden = reader.GetBoolean(10);
                            field.FieldType.ReadOnly = reader.GetBoolean(11);
                            field.FieldType.IsDisplayName = reader.GetBoolean(12);
                            field.FieldType.IsDisplayDescription = reader.GetBoolean(13);
                            field.FieldType.ExcludeFromDefaultView = reader.GetBoolean(14);
                            field.FieldType.Multivalue = reader.GetBoolean(15);
                            field.FieldType.Name = Utilities.XmlToLocaleString(reader.GetSqlXml(16).Value, languages);
                            field.FieldType.Index = reader.GetInt32(17);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting field for entity " + entityId);
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting field for entity " + entityId, ex);
                }
            }

            return field;
        }

        public async Task<object> GetFieldValueAsync(int entityId, string fieldTypeId)
        {
            string value = null;
            string dataType = null;

            using (var connection = new SqlConnection(this.ConnectionString))
            {
                await using var command = connection.CreateCommand();

                command.CommandText = "SELECT Field.Value, FieldType.DataType FROM Field INNER JOIN FieldType ON Field.FieldTypeId = FieldType.Id WHERE Field.EntityId = @EntityId AND Field.FieldTypeId = @FieldTypeId";
                _ = command.Parameters.AddWithValue("@EntityId", entityId);
                _ = command.Parameters.AddWithValue("@FieldTypeId", fieldTypeId);

                await connection.OpenAsync();

                using var reader = await command.ExecuteReaderAsync();
                if (await reader.ReadAsync())
                {
                    value = reader.IsDBNull(0) ? null : reader.GetString(0);
                    dataType = reader.GetString(1);
                }
            }

            return string.IsNullOrEmpty(value)
                ? null
                : SetFieldData(value, dataType);
        }

        public object GetFieldValue(int entityId, string fieldTypeId)
        {
            string value = null;
            string dataType = null;

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "SELECT Field.Value, FieldType.DataType FROM Field INNER JOIN FieldType ON Field.FieldTypeId = FieldType.Id WHERE Field.EntityId = @EntityId AND Field.FieldTypeId = @FieldTypeId";
                    command.Parameters.AddWithValue("@EntityId", entityId);
                    command.Parameters.AddWithValue("@FieldTypeId", fieldTypeId);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            value = reader.IsDBNull(0) ? null : reader.GetString(0);
                            dataType = reader.GetString(1);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting field");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting field", ex);
                }
            }

            if (string.IsNullOrEmpty(value))
            {
                return null;
            }

            return SetFieldData(value, dataType);
        }

        #endregion

        #region Link

        public DtoLink AddLink(Link link)
        {
            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "SET @Index = (SELECT COALESCE(MAX([Index] + 1),0) FROM Link WHERE LinkTypeId = @LinkTypeId AND SourceEntityId = @SourceEntityId);" +
                                            "INSERT INTO Link (SourceEntityId, TargetEntityId, LinkTypeId, LinkEntityId, [Index], Inactive, LastModified) VALUES (@SourceEntityId, @TargetEntityId, @LinkTypeId, @LinkEntityId, @Index, @Inactive, GETUTCDATE());" +
                                            "SET @Id = SCOPE_IDENTITY()";

                    command.Parameters.AddWithValue("@SourceEntityId", link.Source.Id);
                    command.Parameters.AddWithValue("@TargetEntityId", link.Target.Id);
                    command.Parameters.AddWithValue("@LinkTypeId", link.LinkType.Id);
                    command.Parameters.AddWithValue("@Inactive", link.Inactive);

                    SqlParameter idParameter = new SqlParameter("@Id", SqlDbType.Int);
                    idParameter.Direction = ParameterDirection.Output;
                    command.Parameters.Add(idParameter);

                    SqlParameter indexParameter = new SqlParameter("@Index", SqlDbType.Int);
                    indexParameter.Direction = ParameterDirection.Output;
                    command.Parameters.Add(indexParameter);

                    if (link.LinkEntity == null)
                    {
                        command.Parameters.AddWithValue("@LinkEntityId", DBNull.Value);
                    }
                    else
                    {
                        command.Parameters.AddWithValue("@LinkEntityId", link.LinkEntity.Id);
                    }

                    connection.Open();
                    command.ExecuteNonQuery();
                    link.Id = (int)idParameter.Value;
                    link.Index = (int)indexParameter.Value;
                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when adding link");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when adding link", ex);
                }
            }

            return context.DataPersistance.GetLink(link.Id);
        }

        public bool LinkAlreadyExists(int sourceEntityId, int targetEntityId, int? linkEntityId, string linkTypeId)
        {
            bool result = false;

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "SELECT Id FROM Link WHERE SourceEntityId = @SourceEntityId AND TargetEntityId = @TargetEntityId AND LinkTypeId = @LinkTypeId";
                    command.Parameters.AddWithValue("@SourceEntityId", sourceEntityId);
                    command.Parameters.AddWithValue("@TargetEntityId", targetEntityId);
                    command.Parameters.AddWithValue("@LinkTypeId", linkTypeId);

                    if (linkEntityId != null)
                    {
                        command.CommandText += " AND LinkEntityId = @LinkEntityId";
                        command.Parameters.AddWithValue("@LinkEntityId", linkEntityId);
                    }

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        if (reader.HasRows)
                        {
                            result = true;
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when checking if link already exists");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when checking if link already exists", ex);
                }
            }

            return result;
        }

        public List<DtoLink> GetLinksForEntitiesByLinkType(IList<int> entityIds, string linkTypeId)
        {
            var links = new List<DtoLink>();
            if (entityIds == null || !entityIds.Any())
            {
                return links;
            }

            var ids = new List<int>();

            var totalIdsCount = entityIds.Count;
            var batchCounter = 0;

            using (var connection = new SqlConnection(this.ConnectionString))
            {
                var command = connection.CreateCommand();
                command.CommandTimeout = 300;
                connection.Open();

                do
                {
                    var batchIds = entityIds.Skip(batchCounter).Take(10).ToList();
                    batchCounter += batchIds.Count;

                    var entityIdString = string.Join(',', batchIds);

                    command.Parameters.Clear();
                    command.Parameters.Add("@LinkTypeId", SqlDbType.VarChar).Value = linkTypeId;
                    command.CommandText = @$"SELECT Id, SourceEntityId, TargetEntityId, LinkTypeId, [Index], LinkEntityId, Inactive, LastModified 
                                         FROM Link WHERE SourceEntityId IN ({entityIdString}) AND LinkTypeId = @LinkTypeId
                                         UNION
                                         SELECT Id, SourceEntityId, TargetEntityId, LinkTypeId, [Index], LinkEntityId, Inactive, LastModified 
                                         FROM Link WHERE TargetEntityId IN ({entityIdString}) AND LinkTypeId = @LinkTypeId;";

                    using var reader = command.ExecuteReader();
                    while (reader.Read())
                    {
                        var sourceEntityId = reader.GetInt32(reader.GetOrdinal("SourceEntityId"));
                        var targetEntityId = reader.GetInt32(reader.GetOrdinal("TargetEntityId"));
                        var link = new DtoLink
                        {
                            Id = reader.GetInt32(reader.GetOrdinal("Id")),
                            Source = new DtoEntity { Id = sourceEntityId },
                            Target = new DtoEntity { Id = targetEntityId },
                            LinkTypeId = reader.GetString(reader.GetOrdinal("LinkTypeId")),
                            Index = reader.GetInt32(reader.GetOrdinal("Index")),
                            Inactive = reader.GetBoolean(reader.GetOrdinal("Inactive")),
                            LastModified = reader.GetDateTime(reader.GetOrdinal("LastModified"))
                        };

                        if (!ids.Contains(sourceEntityId))
                        {
                            ids.Add(sourceEntityId);
                        }

                        if (!ids.Contains(targetEntityId))
                        {
                            ids.Add(targetEntityId);
                        }

                        if (!reader.IsDBNull(reader.GetOrdinal("LinkEntityId")))
                        {
                            var linkEntityId = reader.GetInt32(reader.GetOrdinal("LinkEntityId"));
                            link.LinkEntity = new DtoEntity { Id = linkEntityId };
                            if (!ids.Contains(linkEntityId))
                            {
                                ids.Add(linkEntityId);
                            }
                        }

                        links.Add(link);
                    }
                } while (batchCounter < totalIdsCount);
            }

            return this.GetAllLinksFromLinkListByEntityIds(links, ids, CancellationToken.None);
        }

        public List<DtoLink> GetLinksForEntity(int entityId, CancellationToken cancellationToken)
        {
            List<DtoLink> links = new List<DtoLink>();

            List<int> ids = new List<int> { entityId };

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();
                    cancellationToken.Register(() =>
                    {
                        command.Cancel();
                        cancellationToken.ThrowIfCancellationRequested();
                    });
                    command.CommandText = "SELECT Id, SourceEntityId, TargetEntityId, LinkTypeId, [Index], LinkEntityId, Inactive, LastModified FROM Link WHERE SourceEntityId = @SourceEntityId OR TargetEntityId = @TargetEntityId";
                    command.Parameters.AddWithValue("@TargetEntityId", entityId);
                    command.Parameters.AddWithValue("@SourceEntityId", entityId);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            cancellationToken.ThrowIfCancellationRequested();
                            DtoLink link = new DtoLink();

                            link.Id = reader.GetInt32(0);

                            if (reader.GetInt32(1) == entityId)
                            {
                                link.Source = new DtoEntity { Id = entityId };
                                link.Target = new DtoEntity { Id = reader.GetInt32(2) };

                                if (!ids.Contains(reader.GetInt32(2)))
                                {
                                    ids.Add(reader.GetInt32(2));
                                }
                            }
                            else
                            {
                                link.Target = new DtoEntity { Id = entityId };
                                link.Source = new DtoEntity { Id = reader.GetInt32(1) };

                                if (!ids.Contains(reader.GetInt32(1)))
                                {
                                    ids.Add(reader.GetInt32(1));
                                }
                            }

                            link.LinkTypeId = reader.GetString(3);
                            link.Index = reader.GetInt32(4);

                            if (!reader.IsDBNull(5))
                            {
                                link.LinkEntity = new DtoEntity { Id = reader.GetInt32(5) };

                                if (!ids.Contains(reader.GetInt32(5)))
                                {
                                    ids.Add(reader.GetInt32(5));
                                }
                            }

                            link.Inactive = reader.GetBoolean(6);
                            link.LastModified = reader.GetDateTime(7);

                            links.Add(link);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex) when (!(ex is OperationCanceledException))
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting field");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting field", ex);
                }
            }

            return GetAllLinksFromLinkListByEntityIds(links, ids, cancellationToken);
        }

        public async Task<List<DtoLink>> GetLinksForEntityAsync(int entityId, CancellationToken cancellationToken)
        {
            List<DtoLink> links = new List<DtoLink>();

            List<int> ids = new List<int> { entityId };

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();
                    cancellationToken.Register(() => {
                        command.Cancel();
                        cancellationToken.ThrowIfCancellationRequested();
                    });
                    command.CommandText = "SELECT Id, SourceEntityId, TargetEntityId, LinkTypeId, [Index], LinkEntityId, Inactive, LastModified FROM Link WHERE SourceEntityId = @SourceEntityId OR TargetEntityId = @TargetEntityId";
                    command.Parameters.AddWithValue("@TargetEntityId", entityId);
                    command.Parameters.AddWithValue("@SourceEntityId", entityId);

                    connection.Open();

                    using (SqlDataReader reader = await command.ExecuteReaderAsync())
                    {
                        while (reader.Read())
                        {
                            cancellationToken.ThrowIfCancellationRequested();
                            DtoLink link = new DtoLink();

                            link.Id = reader.GetInt32(0);

                            if (reader.GetInt32(1) == entityId)
                            {
                                link.Source = new DtoEntity { Id = entityId };
                                link.Target = new DtoEntity { Id = reader.GetInt32(2) };

                                if (!ids.Contains(reader.GetInt32(2)))
                                {
                                    ids.Add(reader.GetInt32(2));
                                }
                            }
                            else
                            {
                                link.Target = new DtoEntity { Id = entityId };
                                link.Source = new DtoEntity { Id = reader.GetInt32(1) };

                                if (!ids.Contains(reader.GetInt32(1)))
                                {
                                    ids.Add(reader.GetInt32(1));
                                }
                            }

                            link.LinkTypeId = reader.GetString(3);
                            link.Index = reader.GetInt32(4);

                            if (!reader.IsDBNull(5))
                            {
                                link.LinkEntity = new DtoEntity { Id = reader.GetInt32(5) };

                                if (!ids.Contains(reader.GetInt32(5)))
                                {
                                    ids.Add(reader.GetInt32(5));
                                }
                            }

                            link.Inactive = reader.GetBoolean(6);
                            link.LastModified = reader.GetDateTime(7);

                            links.Add(link);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex) when (!(ex is OperationCanceledException))
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting field");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting field", ex);
                }
            }

            return (await GetAllLinksFromLinkListByEntityIdsAsync(links, ids)).ToList();
        }

        public List<Link> GetFullLinksForEntity(int entityId, bool includePendingDelete = false, bool ignoreSegmentCheck = false)
        {
            List<Link> links = new List<Link>();

            List<int> ids = new List<int> { entityId };

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "SELECT Id, SourceEntityId, TargetEntityId, LinkTypeId, [Index], LinkEntityId, Inactive, LastModified FROM Link WHERE SourceEntityId = @SourceEntityId OR TargetEntityId = @TargetEntityId";
                    command.Parameters.AddWithValue("@TargetEntityId", entityId);
                    command.Parameters.AddWithValue("@SourceEntityId", entityId);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            Link link = new Link();

                            link.Id = reader.GetInt32(0);

                            if (reader.GetInt32(1) == entityId)
                            {
                                link.Source = new Entity { Id = entityId };
                                link.Target = new Entity { Id = reader.GetInt32(2) };

                                if (!ids.Contains(reader.GetInt32(2)))
                                {
                                    ids.Add(reader.GetInt32(2));
                                }
                            }
                            else
                            {
                                link.Target = new Entity { Id = entityId };
                                link.Source = new Entity { Id = reader.GetInt32(1) };

                                if (!ids.Contains(reader.GetInt32(1)))
                                {
                                    ids.Add(reader.GetInt32(1));
                                }
                            }

                            link.LinkType = new LinkType { Id = reader.GetString(3) };

                            link.Index = reader.GetInt32(4);

                            if (!reader.IsDBNull(5))
                            {
                                link.LinkEntity = new Entity { Id = reader.GetInt32(5) };

                                if (!ids.Contains(reader.GetInt32(5)))
                                {
                                    ids.Add(reader.GetInt32(5));
                                }
                            }

                            link.Inactive = reader.GetBoolean(6);
                            link.LastModified = reader.GetDateTime(7);

                            links.Add(link);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting field");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting field", ex);
                }
            }

            List<LinkType> fullLinkTypes = this.GetAllLinkTypes();

            List<Entity> entities = new List<Entity>();

            List<EntityType> entityTypes = new List<EntityType>();

            foreach (int id in ids)
            {
                entities.Add(context.DataPersistance.GetFullEntity(id, null, true, includePendingDelete, ignoreSegmentCheck));
            }

            foreach (Link link in links)
            {
                link.Source = entities.FirstOrDefault(e => e.Id == link.Source.Id);
                link.Target = entities.FirstOrDefault(e => e.Id == link.Target.Id);

                if (link.LinkEntity != null)
                {
                    link.LinkEntity = entities.FirstOrDefault(e => e.Id == link.LinkEntity.Id);
                }

                link.LinkType = fullLinkTypes.FirstOrDefault(lt => lt.Id == link.LinkType.Id);
            }

            return links;
        }

        public Link GetFullLink(int id)
        {
            Link link = null;

            using (var connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    var command = connection.CreateCommand();

                    command.CommandText = "SELECT Id, SourceEntityId, TargetEntityId, LinkTypeId, [Index], LinkEntityId, Inactive, LastModified FROM Link WHERE Id = @Id";
                    command.Parameters.AddWithValue("@Id", id);

                    connection.Open();

                    using (var reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            link = new Link
                            {
                                Id = reader.GetInt32(0),
                                Source = new Entity { Id = reader.GetInt32(1) },
                                Target = new Entity { Id = reader.GetInt32(2) },
                                LinkType = new LinkType { Id = reader.GetString(3) },
                                Index = reader.GetInt32(4)
                            };

                            if (!reader.IsDBNull(5))
                            {
                                link.LinkEntity = new Entity { Id = reader.GetInt32(5) };
                            }

                            link.Inactive = reader.GetBoolean(6);
                            link.LastModified = reader.GetDateTime(7);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting link with id " + id);
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting link with id " + id, ex);
                }
            }

            if (link == null)
            {
                return null;
            }

            link.LinkType = this.context.DataPersistance.GetLinkType(link.LinkType.Id);
            link.Source = this.context.DataPersistance.GetFullEntity(link.Source.Id, null);
            link.Target = this.context.DataPersistance.GetFullEntity(link.Target.Id, null);

            if (link.LinkEntity != null)
            {
                link.LinkEntity = this.context.DataPersistance.GetFullEntity(link.LinkEntity.Id, null);
            }

            return link;
        }

        public List<DtoLink> GetResourceLinksForEntities(IList<int> entityIds)
        {
            var links = new List<DtoLink>();

            if (entityIds == null || !entityIds.Any())
            {
                return links;
            }

            var ids = new List<int>();
            var totalIdsCount = entityIds.Count;
            var batchCounter = 0;

            using (var connection = new SqlConnection(this.ConnectionString))
            {
                var command = connection.CreateCommand();
                command.CommandTimeout = 300;
                connection.Open();

                do
                {
                    var batchIds = entityIds.Skip(batchCounter).Take(10).ToList();
                    batchCounter += batchIds.Count;

                    var entityIdString = string.Join(',', batchIds);

                    command.CommandText = @$"SELECT Link.Id, Link.SourceEntityId, Link.TargetEntityId, Link.LinkTypeId,
                                                    Link.[Index], Link.LinkEntityId, Link.Inactive, Link.LastModified
                                             FROM Link INNER JOIN LinkType ON Link.LinkTypeId = LinkType.Id
                                             WHERE Link.SourceEntityId IN ({entityIdString})
                                             AND LinkType.TargetEntityTypeId = 'Resource';";

                    using var reader = command.ExecuteReader();
                    while (reader.Read())
                    {
                        var sourceEntityId = reader.GetInt32(reader.GetOrdinal("SourceEntityId"));
                        var targetEntityId = reader.GetInt32(reader.GetOrdinal("TargetEntityId"));
                        var link = new DtoLink
                        {
                            Id = reader.GetInt32(reader.GetOrdinal("Id")),
                            Source = new DtoEntity { Id = sourceEntityId },
                            Target = new DtoEntity { Id = targetEntityId },
                            LinkTypeId = reader.GetString(reader.GetOrdinal("LinkTypeId")),
                            Index = reader.GetInt32(reader.GetOrdinal("Index")),
                            Inactive = reader.GetBoolean(reader.GetOrdinal("Inactive")),
                            LastModified = reader.GetDateTime(reader.GetOrdinal("LastModified")),
                        };

                        if (!ids.Contains(sourceEntityId))
                        {
                            ids.Add(sourceEntityId);
                        }

                        if (!ids.Contains(targetEntityId))
                        {
                            ids.Add(targetEntityId);
                        }

                        if (!reader.IsDBNull(reader.GetOrdinal("LinkEntityId")))
                        {
                            var linkEntityId = reader.GetInt32(reader.GetOrdinal("LinkEntityId"));
                            link.LinkEntity = new DtoEntity { Id = linkEntityId };

                            if (!ids.Contains(linkEntityId))
                            {
                                ids.Add(linkEntityId);
                            }
                        }

                        links.Add(link);
                    }
                } while (batchCounter < totalIdsCount);
            }

            return this.GetAllLinksFromLinkListByEntityIds(links, ids, CancellationToken.None);
        }

        public List<DtoLink> GetResourceLinksForEntity(int entityId)
        {
            List<DtoLink> links = new List<DtoLink>();

            List<int> ids = new List<int> { entityId };

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "SELECT Link.Id, Link.SourceEntityId, Link.TargetEntityId, Link.LinkTypeId, Link.[Index], Link.LinkEntityId, Link.Inactive, Link.LastModified " +
                        "FROM Link INNER JOIN LinkType ON Link.LinkTypeId = LinkType.Id WHERE Link.SourceEntityId = @SourceEntityId AND LinkType.TargetEntityTypeId = 'Resource'";

                    command.Parameters.AddWithValue("@SourceEntityId", entityId);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            DtoLink link = new DtoLink();

                            link.Id = reader.GetInt32(0);

                            link.Source = new DtoEntity { Id = entityId };
                            link.Target = new DtoEntity { Id = reader.GetInt32(2) };

                            if (!ids.Contains(reader.GetInt32(2)))
                            {
                                ids.Add(reader.GetInt32(2));
                            }

                            link.LinkTypeId = reader.GetString(3);
                            link.Index = reader.GetInt32(4);

                            if (!reader.IsDBNull(5))
                            {
                                link.LinkEntity = new DtoEntity { Id = reader.GetInt32(5) };

                                if (!ids.Contains(reader.GetInt32(5)))
                                {
                                    ids.Add(reader.GetInt32(5));
                                }
                            }

                            link.Inactive = reader.GetBoolean(6);
                            link.LastModified = reader.GetDateTime(7);

                            links.Add(link);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting field");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting field", ex);
                }
            }

            return GetAllLinksFromLinkListByEntityIds(links, ids, CancellationToken.None);
        }

        public DtoLink GetLink(int linkId)
        {
            DtoLink link = null;

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "SELECT Id, SourceEntityId, TargetEntityId, LinkTypeId, [Index], LinkEntityId, Inactive, LastModified FROM Link WHERE Id = @Id";
                    command.Parameters.AddWithValue("@Id", linkId);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            link = ReadLink(reader);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting link with id " + linkId);
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting link with id " + linkId, ex);
                }
            }

            // Re-map all entities for the links
            if (link != null)
            {
                link.Source = GetEntity(link.Source.Id);
                link.Target = GetEntity(link.Target.Id);
                link.LinkEntity = link.LinkEntity == null ? null : GetEntity(link.LinkEntity.Id);
            }

            return link;
        }

        private DtoLink ReadLink(SqlDataReader reader)
        {
            return new DtoLink
            {
                Id = reader.GetInt32(0),
                Source = new DtoEntity { Id = reader.GetInt32(1) },
                Target = new DtoEntity { Id = reader.GetInt32(2) },
                LinkTypeId = reader.GetString(3),
                Index = reader.GetInt32(4),
                LinkEntity = reader.IsDBNull(5) ? null : new DtoEntity { Id = reader.GetInt32(5) },
                Inactive = reader.GetBoolean(6),
                LastModified = reader.GetDateTime(7)
            };
        }

        public List<DtoLink> GetOutboundLinksForEntity(int entityId, CancellationToken cancellationToken)
        {
            List<DtoLink> links = new List<DtoLink>();

            List<int> ids = new List<int> { entityId };

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();
                    _ = cancellationToken.Register(() => {
                        command.Cancel();
                        cancellationToken.ThrowIfCancellationRequested();
                    });
                    command.CommandText = "SELECT Id, SourceEntityId, TargetEntityId, LinkTypeId, [Index], LinkEntityId, Inactive, LastModified FROM Link WHERE SourceEntityId = @SourceEntityId";
                    command.Parameters.AddWithValue("@SourceEntityId", entityId);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            cancellationToken.ThrowIfCancellationRequested();

                            DtoLink link = new DtoLink();

                            link.Id = reader.GetInt32(0);

                            link.Source = new DtoEntity { Id = entityId };
                            link.Target = new DtoEntity { Id = reader.GetInt32(2) };

                            if (!ids.Contains(reader.GetInt32(2)))
                            {
                                ids.Add(reader.GetInt32(2));
                            }

                            link.LinkTypeId = reader.GetString(3);
                            link.Index = reader.GetInt32(4);

                            if (!reader.IsDBNull(5))
                            {
                                link.LinkEntity = new DtoEntity { Id = reader.GetInt32(5) };

                                if (!ids.Contains(reader.GetInt32(5)))
                                {
                                    ids.Add(reader.GetInt32(5));
                                }
                            }

                            link.Inactive = reader.GetBoolean(6);
                            link.LastModified = reader.GetDateTime(7);

                            links.Add(link);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex) when (!(ex is OperationCanceledException))
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting outbound links for entity " + entityId);
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting outbound links for entity " + entityId, ex);
                }
            }

            return this.GetAllLinksFromLinkListByEntityIds(links, ids, cancellationToken);
        }

        public List<DtoLink> GetInboundLinksForEntity(int entityId)
        {
            List<DtoLink> links = new List<DtoLink>();

            List<int> ids = new List<int> { entityId };

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "SELECT Id, SourceEntityId, TargetEntityId, LinkTypeId, [Index], LinkEntityId, Inactive, LastModified FROM Link WHERE TargetEntityId = @TargetEntityId";
                    command.Parameters.AddWithValue("@TargetEntityId", entityId);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            DtoLink link = new DtoLink();

                            link.Id = reader.GetInt32(0);

                            link.Source = new DtoEntity { Id = reader.GetInt32(1) };
                            link.Target = new DtoEntity { Id = entityId };

                            if (!ids.Contains(reader.GetInt32(1)))
                            {
                                ids.Add(reader.GetInt32(1));
                            }

                            link.LinkTypeId = reader.GetString(3);
                            link.Index = reader.GetInt32(4);

                            if (!reader.IsDBNull(5))
                            {
                                link.LinkEntity = new DtoEntity { Id = reader.GetInt32(5) };

                                if (!ids.Contains(reader.GetInt32(5)))
                                {
                                    ids.Add(reader.GetInt32(5));
                                }
                            }

                            link.Inactive = reader.GetBoolean(6);
                            link.LastModified = reader.GetDateTime(7);

                            links.Add(link);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting inbound links for entity " + entityId);
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting inbound links for entity " + entityId, ex);
                }
            }

            return GetAllLinksFromLinkListByEntityIds(links, ids, CancellationToken.None);
        }

        public int GetLinkCountForOutboundLinkType(string linkTypeId, int sourceEntityId)
        {
            int count = 0;

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "SELECT COUNT(*) FROM Link WHERE LinkTypeId = @LinkTypeId AND SourceEntityId = @SourceEntityId";
                    command.Parameters.AddWithValue("@LinkTypeId", linkTypeId);
                    command.Parameters.AddWithValue("@SourceEntityId", sourceEntityId);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            count = reader.GetInt32(0);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting outbound link count for entity");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting outbound link count for entity", ex);
                }
            }

            return count;
        }

        public async Task<int> GetLinkCountForOutboundLinkTypeAsync(string linkTypeId, int sourceEntityId)
        {
            var count = 0;

            using (var connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    await using var command = connection.CreateCommand();

                    command.CommandText = "SELECT COUNT(*) FROM Link WHERE LinkTypeId = @LinkTypeId AND SourceEntityId = @SourceEntityId";
                    _ = command.Parameters.AddWithValue("@LinkTypeId", linkTypeId);
                    _ = command.Parameters.AddWithValue("@SourceEntityId", sourceEntityId);

                    await connection.OpenAsync();

                    using var reader = await command.ExecuteReaderAsync();
                    if (await reader.ReadAsync())
                    {
                        count = reader.GetInt32(0);
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, "An unexpected error occurred when getting outbound link count for entity");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting outbound link count for entity", ex);
                }
            }

            return count;
        }

        public List<DtoLink> GetOutboundLinksForEntityAndLinkType(int entityId, string linkTypeId)
        {
            List<DtoLink> links = new List<DtoLink>();

            List<int> ids = new List<int> { entityId };

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "SELECT l.Id, l.SourceEntityId, l.TargetEntityId, l.LinkTypeId, l.[Index], l.LinkEntityId, l.Inactive, l.LastModified, " +
                                          " sourceEntity.ContentSegmentationId sourceSegmentId, targetEntity.ContentSegmentationId targetSegmentId " +
                                          " FROM Link l WITH (NOLOCK) , Entity sourceEntity WITH (NOLOCK), Entity targetEntity WITH (NOLOCK) " +
                                          " WHERE l.sourceEntityId = sourceEntity.Id and l.TargetEntityId = targetEntity.Id " +
                                          " AND SourceEntityId = @SourceEntityId " +
                                          " AND LinkTypeId = @LinkTypeId";

                    command.Parameters.AddWithValue("@SourceEntityId", entityId);
                    command.Parameters.AddWithValue("@LinkTypeId", linkTypeId);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            DtoLink link = new DtoLink();

                            link.Id = reader.GetInt32(0);

                            link.Source = new DtoEntity { Id = entityId, Segment = new DtoSegment() { Id = reader.GetInt32(8) } };
                            link.Target = new DtoEntity { Id = reader.GetInt32(2), Segment = new DtoSegment() { Id = reader.GetInt32(9) } };

                            if (!ids.Contains(reader.GetInt32(2)))
                            {
                                ids.Add(reader.GetInt32(2));
                            }

                            link.LinkTypeId = reader.GetString(3);
                            link.Index = reader.GetInt32(4);

                            if (!reader.IsDBNull(5))
                            {
                                link.LinkEntity = new DtoEntity { Id = reader.GetInt32(5) };

                                if (!ids.Contains(reader.GetInt32(5)))
                                {
                                    ids.Add(reader.GetInt32(5));
                                }
                            }

                            link.Inactive = reader.GetBoolean(6);
                            link.LastModified = reader.GetDateTime(7);

                            links.Add(link);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    const string Message = "An unexpected error occurred when getting outbound links for entity and link type";
                    Log.Error(ex, Message);
                    throw ErrorUtility.GetDataAccessException(Message, ex);
                }
            }

            return GetAllLinksFromLinkListByEntityIds(links, ids, CancellationToken.None);
        }

        public async Task<IEnumerable<DtoLink>> GetOutboundLinksForEntityAndLinkTypeAsync(int entityId, string linkTypeId)
        {
            var links = new List<DtoLink>();
            var ids = new List<int> { entityId };

            using (var connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    await using var command = connection.CreateCommand();

                    command.CommandText = "SELECT l.Id, l.SourceEntityId, l.TargetEntityId, l.LinkTypeId, l.[Index], l.LinkEntityId, l.Inactive, l.LastModified, " +
                                          " sourceEntity.ContentSegmentationId sourceSegmentId, targetEntity.ContentSegmentationId targetSegmentId " +
                                          " FROM Link l WITH (NOLOCK) , Entity sourceEntity WITH (NOLOCK), Entity targetEntity WITH (NOLOCK) " +
                                          " WHERE l.sourceEntityId = sourceEntity.Id and l.TargetEntityId = targetEntity.Id " +
                                          " AND SourceEntityId = @SourceEntityId " +
                                          " AND LinkTypeId = @LinkTypeId";

                    _ = command.Parameters.AddWithValue("@SourceEntityId", entityId);
                    _ = command.Parameters.AddWithValue("@LinkTypeId", linkTypeId);

                    await connection.OpenAsync();

                    using var reader = await command.ExecuteReaderAsync();
                    while (await reader.ReadAsync())
                    {
                        var targetEntityId = reader.GetInt32(reader.GetOrdinal("TargetEntityId"));
                        if (!ids.Contains(targetEntityId))
                        {
                            ids.Add(targetEntityId);
                        }

                        var link = new DtoLink
                        {
                            Id = reader.GetInt32(reader.GetOrdinal("Id")),
                            Source = new DtoEntity { Id = entityId, Segment = new DtoSegment() { Id = reader.GetInt32(reader.GetOrdinal("sourceSegmentId")) } },
                            Target = new DtoEntity { Id = targetEntityId, Segment = new DtoSegment() { Id = reader.GetInt32(reader.GetOrdinal("targetSegmentId")) } },
                            LinkTypeId = reader.GetString(reader.GetOrdinal("LinkTypeId")),
                            Index = reader.GetInt32(reader.GetOrdinal("Index")),
                            Inactive = reader.GetBoolean(reader.GetOrdinal("Inactive")),
                            LastModified = reader.GetDateTime(reader.GetOrdinal("LastModified"))
                        };

                        if (!reader.IsDBNull(reader.GetOrdinal("LinkEntityId")))
                        {
                            var linkEntityId = reader.GetInt32(reader.GetOrdinal("LinkEntityId"));
                            link.LinkEntity = new DtoEntity { Id = linkEntityId };

                            if (!ids.Contains(linkEntityId))
                            {
                                ids.Add(linkEntityId);
                            }
                        }

                        links.Add(link);
                    }
                }
                catch (Exception ex)
                {
                    const string Message = "An unexpected error occurred when getting outbound links for entity and link type";
                    Log.Error(ex, Message);
                    throw ErrorUtility.GetDataAccessException(Message, ex);
                }
            }

            return await this.GetAllLinksFromLinkListByEntityIdsAsync(links, ids);
        }

        public List<DtoLink> GetInboundLinksForEntityAndLinkType(int entityId, string linkTypeId)
        {
            List<DtoLink> links = new List<DtoLink>();

            List<int> ids = new List<int> { entityId };

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "SELECT Id, SourceEntityId, TargetEntityId, LinkTypeId, [Index], LinkEntityId, Inactive, LastModified FROM Link WHERE TargetEntityId = @TargetEntityId AND LinkTypeId = @LinkTypeId";
                    command.Parameters.AddWithValue("@TargetEntityId", entityId);
                    command.Parameters.AddWithValue("@LinkTypeId", linkTypeId);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            DtoLink link = new DtoLink();

                            link.Id = reader.GetInt32(0);

                            link.Source = new DtoEntity { Id = reader.GetInt32(1) };
                            link.Target = new DtoEntity { Id = entityId };

                            if (!ids.Contains(reader.GetInt32(1)))
                            {
                                ids.Add(reader.GetInt32(1));
                            }

                            link.LinkTypeId = reader.GetString(3);
                            link.Index = reader.GetInt32(4);

                            if (!reader.IsDBNull(5))
                            {
                                link.LinkEntity = new DtoEntity { Id = reader.GetInt32(5) };

                                if (!ids.Contains(reader.GetInt32(5)))
                                {
                                    ids.Add(reader.GetInt32(5));
                                }
                            }

                            link.Inactive = reader.GetBoolean(6);
                            link.LastModified = reader.GetDateTime(7);

                            links.Add(link);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting inbound links for entity and link type");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting inbound links for entity and link type", ex);
                }
            }

            return GetAllLinksFromLinkListByEntityIds(links, ids, CancellationToken.None);
        }

        public DtoLink GetValidationLink(Link link)
        {
            DtoLink dtoLink = null;

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "SELECT dbo.Link.Id AS LinkId, dbo.Entity.Id AS SourceId, dbo.Entity.EntityTypeId AS SourceType, Entity_1.Id AS TargetId, Entity_1.EntityTypeId AS TargetType, dbo.LinkType.Id AS LinkTypeId " +
                                            "FROM dbo.Entity INNER JOIN " +
                                            "dbo.LinkType ON dbo.Entity.EntityTypeId = dbo.LinkType.SourceEntityTypeId INNER JOIN " +
                                            "dbo.Entity AS Entity_1 ON dbo.LinkType.TargetEntityTypeId = Entity_1.EntityTypeId LEFT OUTER JOIN " +
                                            "dbo.Link ON dbo.LinkType.Id = dbo.Link.LinkTypeId AND dbo.Entity.Id = dbo.Link.SourceEntityId AND Entity_1.Id = dbo.Link.TargetEntityId " +
                                            "WHERE(dbo.LinkType.Id = @LinkTypeId) AND(dbo.Entity.Id = @SourceId) AND(Entity_1.Id = @TargetId)";

                    command.Parameters.AddWithValue("@LinkTypeId", link.LinkType.Id);
                    command.Parameters.AddWithValue("@SourceId", link.Source.Id);
                    command.Parameters.AddWithValue("@TargetId", link.Target.Id);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        if (reader.Read())
                        {
                            dtoLink = new DtoLink();

                            if (!reader.IsDBNull(0))
                            {
                                dtoLink.Id = reader.GetInt32(0);
                            }

                            dtoLink.Source = new DtoEntity { Id = reader.GetInt32(1), EntityTypeId = reader.GetString(2) };
                            dtoLink.Target = new DtoEntity { Id = reader.GetInt32(3), EntityTypeId = reader.GetString(4) };

                            dtoLink.LinkTypeId = reader.GetString(5);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    const string Message = "An unexpected error occurred when getting validation link";
                    Log.Error(ex, Message);
                    throw ErrorUtility.GetDataAccessException(Message, ex);
                }
            }

            return dtoLink;
        }

        public Dictionary<int, List<int>> GetInboundParentsForEntitiesAndLinkType(List<int> entityIds, string linkTypeId, CancellationToken cancellationToken)
        {
            Dictionary<int, List<int>> result = new Dictionary<int, List<int>>();

            int totalIdsCount = entityIds.Count;
            int batchCounter = 0;
            do
            {
                cancellationToken.ThrowIfCancellationRequested();
                List<int> batchIds = entityIds.Skip(batchCounter).Take(10000).ToList();
                batchCounter += batchIds.Count;

                string selectIn = string.Join(",", batchIds);

                using (SqlConnection connection = new SqlConnection(this.ConnectionString))
                {
                    try
                    {
                        SqlCommand command = connection.CreateCommand();
                        cancellationToken.Register(() =>
                        {
                            command.Cancel();
                            cancellationToken.ThrowIfCancellationRequested();
                        });
                        command.CommandText = $"SELECT SourceEntityId, TargetEntityId FROM Link WHERE TargetEntityId IN ({selectIn}) AND LinkTypeId = @LinkTypeId";

                        command.Parameters.AddWithValue("@LinkTypeId", linkTypeId);

                        connection.Open();

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                int source = reader.GetInt32(0);
                                int target = reader.GetInt32(1);

                                if (!result.ContainsKey(source))
                                {
                                    result.Add(source, new List<int>());
                                }

                                if (!result[source].Contains(target))
                                {
                                    result[source].Add(target);
                                }
                            }

                            reader.Close();
                        }

                        connection.Close();

                    }
                    catch (Exception ex) when (!(ex is OperationCanceledException))
                    {
                        if (connection.State != ConnectionState.Closed)
                        {
                            connection.Close();
                        }

                        const string Message = "An unexpected error occurred when getting inbound links for entities";
                        Log.Error(ex, Message);
                        throw ErrorUtility.GetDataAccessException(Message, ex);
                    }
                }
            } while (batchCounter < totalIdsCount);

            return result;
        }

        public Dictionary<int, List<int>> GetOutboundChildrenForEntitiesAndLinkType(List<int> entityIds, string linkTypeId, CancellationToken cancellationToken)
        {
            Dictionary<int, List<int>> result = new Dictionary<int, List<int>>();

            int totalIdsCount = entityIds.Count;
            int batchCounter = 0;
            do
            {
                cancellationToken.ThrowIfCancellationRequested();
                List<int> batchIds = entityIds.Skip(batchCounter).Take(10000).ToList();
                batchCounter += batchIds.Count;

                string selectIn = string.Join(",", batchIds);

                using (SqlConnection connection = new SqlConnection(this.ConnectionString))
                {
                    try
                    {
                        SqlCommand command = connection.CreateCommand();
                        cancellationToken.Register(() =>
                        {
                            command.Cancel();
                            cancellationToken.ThrowIfCancellationRequested();
                        });
                        command.CommandText = $"SELECT SourceEntityId, TargetEntityId FROM Link WHERE SourceEntityId IN ({selectIn}) AND LinkTypeId = @LinkTypeId";

                        command.Parameters.AddWithValue("@LinkTypeId", linkTypeId);

                        connection.Open();

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                int source = reader.GetInt32(0);
                                int target = reader.GetInt32(1);

                                if (!result.ContainsKey(source))
                                {
                                    result.Add(source, new List<int>());
                                }

                                if (!result[source].Contains(target))
                                {
                                    result[source].Add(target);
                                }
                            }

                            reader.Close();
                        }

                        connection.Close();

                    }
                    catch (Exception ex) when (!(ex is OperationCanceledException))
                    {
                        if (connection.State != ConnectionState.Closed)
                        {
                            connection.Close();
                        }

                        const string Message = "An unexpected error occurred when getting outbound links for entities";
                        Log.Error(ex, Message);
                        throw ErrorUtility.GetDataAccessException(Message, ex);
                    }
                }

            } while (batchCounter < totalIdsCount);

            return result;
        }

        #endregion

        #region Link Rules

        public bool HasLinkRuleDefinition(int entityId, string linkTypeId)
        {
            bool result = false;

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "SELECT [Id] FROM LinkRuleDefinition WHERE EntityId = @EntityId AND LinkTypeId = @LinkTypeId";
                    command.Parameters.AddWithValue("@EntityId", entityId);
                    command.Parameters.AddWithValue("@LinkTypeId", linkTypeId);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        if (reader.HasRows)
                        {
                            result = true;
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    var message = "An unexpected error occurred when checking if link rule definition exists for entity " + entityId;
                    Log.Error(ex, message);
                    throw ErrorUtility.GetDataAccessException(message, ex);
                }
            }

            return result;
        }

        #endregion

        #region Search

        public List<int> Search(Criteria criteria, Join? joinOperator = null)
        {
            List<int> ids = new List<int>();
            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "QueryEntityData";
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.AddWithValue("@FieldTypeId", criteria.FieldTypeId);
                    command.Parameters.AddWithValue("@Operator", criteria.Operator.ToString());

                    if (criteria.Value == null)
                    {
                        command.Parameters.AddWithValue("@Data", DBNull.Value);
                    }
                    else
                    {
                        var value = criteria.Value.ToString();

                        var fieldType = context.DataPersistance.GetFieldType(criteria.FieldTypeId);

                        if (fieldType == null)
                        {
                            return new List<int>();
                        }

                        if (fieldType.DataType == DataType.DateTime && criteria.Operator != Operator.NotEmpty)
                        {
                            value = Convert.ToDateTime(criteria.Value.ToString())
                                .ToString(Constants.InternalDateTimeFormat, CultureInfo.InvariantCulture);
                        }

                        if (fieldType.DataType == DataType.LocaleString)
                        {
                            value = JsonConvert.SerializeObject(value);

                            // Remove first and last " char
                            value = value.Substring(1, value.Length - 1);
                            value = value.Substring(0, value.Length - 1);
                        }

                        command.Parameters.AddWithValue("@Data", value);
                    }

                    if (string.IsNullOrEmpty(criteria.Language))
                    {
                        command.Parameters.AddWithValue("@Language", DBNull.Value);
                    }
                    else
                    {
                        command.Parameters.AddWithValue("@Language", criteria.Language);
                    }

                    command.CommandTimeout = 9000;

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            ids.Add(reader.GetInt32(0));
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when querying entity data");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when querying entity data", ex);
                }
            }
            return ids;
        }

        public List<int> Search(Query query, List<int> searchHits = null)
        {
            var ids = GetSearchResultsBasedOnCriteria(query);
            if (searchHits != null)
            {
                ids = ids.Intersect(searchHits).ToList();
            }
            if (query.SubQuery == null)
            {
                return ids;
            }
            return this.Search(query.SubQuery, ids);
        }

        private List<int> GetSearchResultsBasedOnCriteria(Query query)
        {
            return context.DataPersistance.SearchForEntitiesBasedOnCriteria(query);
        }

        public List<int> SearchForEntitiesBasedOnCriteria(Query query)
        {
            List<int> searchHits = new List<int>();
            bool firstIteration = true;

            foreach (Criteria criteria in query.Criteria)
            {
                List<int> ids = this.SearchByCriteria(criteria);

                if (query.Join == Join.And)
                {
                    if (firstIteration)
                    {
                        searchHits.AddRange(ids);
                        firstIteration = false;
                        continue;
                    }

                    searchHits = searchHits.Intersect(ids).ToList();
                }

                if (query.Join == Join.Or)
                {
                    if (firstIteration)
                    {
                        searchHits.AddRange(ids);
                        firstIteration = false;
                        continue;
                    }

                    searchHits = searchHits.Union(ids).ToList();
                }
            }
            return searchHits;
        }

        public List<int> LinkSearch(LinkQuery linkQuery)
        {
            // Both source and target
            if (!string.IsNullOrEmpty(linkQuery.SourceEntityTypeId) && !string.IsNullOrEmpty(linkQuery.TargetEntityTypeId))
            {
                // No criteria
                if ((linkQuery.SourceCriteria == null || linkQuery.SourceCriteria.Count == 0) && (linkQuery.TargetCriteria == null || linkQuery.TargetCriteria.Count == 0))
                {
                    return this.GetExistingLinks(linkQuery.LinkTypeId, linkQuery.Direction);
                }

                // Source criteria
                if (linkQuery.SourceCriteria != null && linkQuery.SourceCriteria.Count > 0)
                {
                    var tempResult = this.Search(new Query { Join = Join.And, Criteria = linkQuery.SourceCriteria });

                    if (!tempResult.Any())
                    {
                        return new List<int>();
                    }

                    if (linkQuery.Direction == LinkDirection.OutBound)
                    {
                        List<int> existingLinks = this.GetExistingLinks(linkQuery.LinkTypeId, linkQuery.Direction);

                        return existingLinks.Intersect(tempResult).ToList();
                    }
                    else
                    {
                        return this.GetTargetsForSources(linkQuery.LinkTypeId, tempResult);
                    }
                }

                // Target criteria
                if (linkQuery.TargetCriteria != null && linkQuery.TargetCriteria.Count > 0)
                {
                    var tempResult = this.Search(new Query { Join = Join.And, Criteria = linkQuery.TargetCriteria });

                    if (!tempResult.Any())
                    {
                        return new List<int>();
                    }

                    if (linkQuery.Direction == LinkDirection.OutBound)
                    {
                        return this.GetSourcesForTargets(linkQuery.LinkTypeId, tempResult);
                    }
                    else
                    {
                        List<int> existingLinks = this.GetExistingLinks(linkQuery.LinkTypeId, linkQuery.Direction);

                        return existingLinks.Intersect(tempResult).ToList();
                    }
                }
            }

            // Search outbound
            if (string.IsNullOrEmpty(linkQuery.TargetEntityTypeId))
            {
                List<int> missingTargets = this.GetMissingTargets(linkQuery.SourceEntityTypeId, linkQuery.LinkTypeId);

                if (linkQuery.SourceCriteria != null && linkQuery.SourceCriteria.Count > 0)
                {
                    var tempResult = this.Search(new Query { Join = Join.And, Criteria = linkQuery.SourceCriteria });

                    return missingTargets.Intersect(tempResult).ToList();
                }

                return missingTargets;
            }

            if (string.IsNullOrEmpty(linkQuery.SourceEntityTypeId))
            {
                List<int> missingSources = this.GetMissingSources(linkQuery.TargetEntityTypeId, linkQuery.LinkTypeId);

                if (linkQuery.TargetCriteria != null && linkQuery.TargetCriteria.Count > 0)
                {
                    var tempResult = this.Search(new Query { Join = Join.And, Criteria = linkQuery.TargetCriteria });

                    return missingSources.Intersect(tempResult).ToList();
                }

                return missingSources;
            }

            return new List<int>();
        }

        private DateTime CheckSearchDate(DateTime dateTime)
        {
            if (dateTime.Year < 1753)
                return new DateTime(1753, 1, 1);
            else if (dateTime.Year > 9999)
                return DateTime.MaxValue;
            else
                return dateTime;
        }

        public List<int> SystemSearch(SystemQuery systemQuery)
        {
            List<int> ids = new List<int>();

            TimeIntervalQueryUtil.GetSystemQueryValueFromDatetimeInterval(systemQuery);

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    string segmentIdsInClause = permittedSegmentIdsClause();

                    StringBuilder query = new StringBuilder($"SELECT Id FROM Entity WHERE {segmentIdsInClause} PendingDelete IS NULL");

                    if (systemQuery.EntityIdsList.Count == 0 && systemQuery.EntityId.HasValue)
                    {
                        systemQuery.EntityIdsList.Add(systemQuery.EntityId);
                    }

                    // EntityIdsList 
                    if (systemQuery.EntityIdsList.Count > 0)
                    {
                        if (systemQuery.EntityIdOperator == Operator.Equal)
                        {
                            ids.AddRange(from i in systemQuery.EntityIdsList where i != null select (int)i);

                            return ids;
                        }
                    }

                    #region Entity Type

                    if (!string.IsNullOrEmpty(systemQuery.EntityTypeId))
                    {
                        if (systemQuery.EntityTypeIdOperator == Operator.Equal)
                        {
                            query.Append(" AND EntityTypeId = @EntityTypeId");
                        }
                        else
                        {
                            query.Append(" AND EntityTypeId <> @EntityTypeId");
                        }

                        command.Parameters.AddWithValue("@EntityTypeId", systemQuery.EntityTypeId);
                    }

                    #endregion

                    #region Field Set

                    if (!string.IsNullOrEmpty(systemQuery.FieldSetId) || systemQuery.FieldSetIdOperator == Operator.Empty || systemQuery.FieldSetIdOperator == Operator.NotEmpty)
                    {
                        if (systemQuery.FieldSetIdOperator == Operator.Equal)
                        {
                            query.Append(" AND FieldSetId = @FieldSetId");

                            command.Parameters.AddWithValue("@FieldSetId", systemQuery.FieldSetId);
                        }

                        if (systemQuery.FieldSetIdOperator == Operator.NotEqual)
                        {
                            query.Append(" AND (FieldSetId <> @FieldSetId OR LEN(ISNULL(FieldSetId,'')) = 0)");

                            command.Parameters.AddWithValue("@FieldSetId", systemQuery.FieldSetId);
                        }

                        if (systemQuery.FieldSetIdOperator == Operator.Empty)
                        {
                            query.Append(" AND LEN(ISNULL(FieldSetId,'')) = 0");
                        }

                        if (systemQuery.FieldSetIdOperator == Operator.NotEmpty)
                        {
                            query.Append(" AND LEN(ISNULL(FieldSetId,'')) > 0");
                        }

                        if (systemQuery.FieldSetIdOperator == Operator.ContainsAny || systemQuery.FieldSetIdOperator == Operator.NotContainsAny)
                        {
                            var condition = systemQuery.FieldSetIdOperator == Operator.ContainsAny ? "IN" : "NOT IN";
                            var fieldSetIdsArray = systemQuery.FieldSetId.Split(';');
                            var parameterNames = fieldSetIdsArray.Select((s, i) => "@id" + i.ToString()).ToArray();

                            query.Append($" AND FieldSetId {condition} ({string.Join(",", parameterNames)})");

                            for (var i = 0; i < fieldSetIdsArray.Length; i++)
                            {
                                command.Parameters.AddWithValue(parameterNames[i], fieldSetIdsArray[i]);
                            }
                        }
                    }

                    #endregion

                    #region Last Modified

                    if (systemQuery.LastModified.HasValue)
                    {
                        if (systemQuery.LastModifiedOperator == Operator.Equal)
                        {
                            query.Append(" AND LastModified = @LastModified");
                        }

                        if (systemQuery.LastModifiedOperator == Operator.NotEqual)
                        {
                            query.Append(" AND LastModified <> @LastModified");
                        }

                        if (systemQuery.LastModifiedOperator == Operator.LessThan)
                        {
                            query.Append(" AND LastModified < @LastModified");
                        }

                        if (systemQuery.LastModifiedOperator == Operator.LessThanOrEqual)
                        {
                            query.Append(" AND LastModified <= @LastModified");
                        }

                        if (systemQuery.LastModifiedOperator == Operator.GreaterThan)
                        {
                            query.Append(" AND LastModified > @LastModified");
                        }

                        if (systemQuery.LastModifiedOperator == Operator.GreaterThanOrEqual)
                        {
                            query.Append(" AND LastModified >= @LastModified");
                        }

                        DateTime validUtcDate = CheckSearchDate(systemQuery.LastModified.Value.ToUniversalTime());
                        command.Parameters.AddWithValue("@LastModified", validUtcDate);
                    }

                    #endregion

                    #region Created

                    if (systemQuery.Created.HasValue)
                    {
                        if (systemQuery.CreatedOperator == Operator.Equal)
                        {
                            query.Append(" AND DateCreated = @DateCreated");
                        }

                        if (systemQuery.CreatedOperator == Operator.NotEqual)
                        {
                            query.Append(" AND DateCreated <> @DateCreated");
                        }

                        if (systemQuery.CreatedOperator == Operator.LessThan)
                        {
                            query.Append(" AND DateCreated < @DateCreated");
                        }

                        if (systemQuery.CreatedOperator == Operator.LessThanOrEqual)
                        {
                            query.Append(" AND DateCreated <= @DateCreated");
                        }

                        if (systemQuery.CreatedOperator == Operator.GreaterThan)
                        {
                            query.Append(" AND DateCreated > @DateCreated");
                        }

                        if (systemQuery.CreatedOperator == Operator.GreaterThanOrEqual)
                        {
                            query.Append(" AND DateCreated >= @DateCreated");
                        }

                        DateTime validUtcDate = CheckSearchDate(systemQuery.Created.Value.ToUniversalTime());
                        command.Parameters.AddWithValue("@DateCreated", validUtcDate);
                    }

                    #endregion

                    #region Created By

                    if (!string.IsNullOrEmpty(systemQuery.CreatedBy))
                    {
                        if (systemQuery.CreatedByOperator == Operator.Equal)
                        {
                            query.Append(" AND CreatedBy = @CreatedBy");
                        }
                        else
                        {
                            query.Append(" AND CreatedBy <> @CreatedBy");
                        }

                        command.Parameters.AddWithValue("@CreatedBy", systemQuery.CreatedBy);
                    }

                    #endregion

                    #region Modified By

                    if (!string.IsNullOrEmpty(systemQuery.ModifiedBy))
                    {
                        if (systemQuery.ModifiedByOperator == Operator.Equal)
                        {
                            query.Append(" AND ModifiedBy = @ModifiedBy");
                        }
                        else
                        {
                            query.Append(" AND ModifiedBy <> @ModifiedBy");
                        }

                        command.Parameters.AddWithValue("@ModifiedBy", systemQuery.ModifiedBy);
                    }

                    #endregion

                    #region Locked By

                    if (!string.IsNullOrEmpty(systemQuery.LockedBy))
                    {
                        if (systemQuery.LockedByOperator == Operator.Equal)
                        {
                            query.Append(" AND Locked = @LockedBy");
                        }
                        else
                        {
                            query.Append(" AND Locked <> @LockedBy");
                        }

                        command.Parameters.AddWithValue("@LockedBy", systemQuery.LockedBy);
                    }

                    #endregion

                    #region Completeness

                    if (systemQuery.Completeness.HasValue)
                    {
                        if (systemQuery.CompletenessOperator == Operator.Equal)
                        {
                            query.Append(" AND Completeness = @Completeness");
                        }

                        if (systemQuery.CompletenessOperator == Operator.NotEqual)
                        {
                            query.Append(" AND Completeness <> @Completeness");
                        }

                        if (systemQuery.CompletenessOperator == Operator.LessThan)
                        {
                            query.Append(" AND Completeness < @Completeness");
                        }

                        if (systemQuery.CompletenessOperator == Operator.LessThanOrEqual)
                        {
                            query.Append(" AND Completeness <= @Completeness");
                        }

                        if (systemQuery.CompletenessOperator == Operator.GreaterThan)
                        {
                            query.Append(" AND Completeness > @Completeness");
                        }

                        if (systemQuery.CompletenessOperator == Operator.GreaterThanOrEqual)
                        {
                            query.Append(" AND Completeness >= @Completeness");
                        }

                        command.Parameters.AddWithValue("@Completeness", systemQuery.Completeness.Value);
                    }

                    #endregion

                    #region Content Segmentation

                    if (systemQuery.SegmentIds != null && systemQuery.SegmentIds.Any())
                    {
                        var theIds = string.Join(",", systemQuery.SegmentIds);

                        if (systemQuery.SegmentIdsOperator == Operator.Contains || systemQuery.SegmentIdsOperator == Operator.ContainsAny)
                        {
                            query.Append($" AND ContentSegmentationId IN ({theIds})");
                        }
                        else if (systemQuery.SegmentIdsOperator == Operator.NotContains || systemQuery.SegmentIdsOperator == Operator.NotContainsAny)
                        {
                            query.Append($" AND ContentSegmentationId NOT IN ({theIds})");
                        }
                    }

                    #endregion

                    command.CommandText = query.ToString();

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            ids.Add(reader.GetInt32(0));
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when doing system query");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when doing system query", ex);
                }
            }

            return ids;
        }

        public List<int> SearchSpecification(SpecificationQuery query)
        {
            List<int> searchHits = new List<int>();
            bool firstIteration = true;

            foreach (Criteria criteria in query.Criteria)
            {
                List<int> ids = this.ParseSpecificationQuery(criteria);

                if (query.Join == Join.And)
                {
                    if (firstIteration)
                    {
                        searchHits.AddRange(ids);
                        firstIteration = false;
                        continue;
                    }

                    searchHits = searchHits.Intersect(ids).ToList();
                }

                if (query.Join == Join.Or)
                {
                    if (firstIteration)
                    {
                        searchHits.AddRange(ids);
                        firstIteration = false;
                        continue;
                    }

                    searchHits = searchHits.Union(ids).ToList();
                }
            }

            return searchHits.Distinct().ToList();
        }

        #endregion

        #region Comment

        #endregion

        #region Private Methods

        #region Link Search

        private List<int> GetExistingLinks(string linkTypeId, LinkDirection direction)
        {
            List<int> ids = new List<int>();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    if (direction == LinkDirection.OutBound)
                    {
                        command.CommandText = "SELECT DISTINCT Entity.Id FROM Entity INNER JOIN Link ON Entity.Id = Link.SourceEntityId WHERE (Link.LinkTypeId = @LinkTypeId) AND Entity.PendingDelete IS NULL";
                    }
                    else
                    {
                        command.CommandText = "SELECT DISTINCT Entity.Id FROM Entity INNER JOIN Link ON Entity.Id = Link.TargetEntityId WHERE (Link.LinkTypeId = @LinkTypeId) AND Entity.PendingDelete IS NULL";
                    }

                    command.Parameters.AddWithValue("@LinkTypeId", linkTypeId);
                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            ids.Add(reader.GetInt32(0));
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting missing link targets");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting missing link targets", ex);
                }
            }

            return ids;
        }

        private List<int> GetMissingTargets(string entityTypeId, string linkTypeId)
        {
            List<int> ids = new List<int>();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "SELECT Id FROM Entity WHERE EntityTypeId = @EntityTypeId AND Id NOT IN (SELECT DISTINCT SourceEntityId FROM Link WHERE LinkTypeId = @LinkTypeId) AND PendingDelete IS NULL";
                    command.Parameters.AddWithValue("@EntityTypeId", entityTypeId);
                    command.Parameters.AddWithValue("@LinkTypeId", linkTypeId);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            ids.Add(reader.GetInt32(0));
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting missing link targets");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting missing link targets", ex);
                }
            }

            return ids;
        }

        private List<int> GetMissingSources(string entityTypeId, string linkTypeId)
        {
            List<int> ids = new List<int>();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "SELECT Id from Entity where EntityTypeId = @EntityTypeId AND Id not in (Select DISTINCT TargetEntityId FROM Link WHERE LinkTypeId = @LinkTypeId) AND PendingDelete IS NULL";
                    command.Parameters.AddWithValue("@EntityTypeId", entityTypeId);
                    command.Parameters.AddWithValue("@LinkTypeId", linkTypeId);

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            ids.Add(reader.GetInt32(0));
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when getting missing link sources");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting missing link sources", ex);
                }
            }

            return ids;
        }

        private List<int> GetSourcesForTargets(string linkTypeId, List<int> targets)
        {
            List<int> result = new List<int>();
            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();
                    command.Parameters.AddWithValue("@LinkTypeId", linkTypeId);

                    List<List<int>> splitList = Core.Util.Utilities.SplitListIntoBatches(targets);
                    foreach (var batch in splitList)
                    {
                        command.CommandText =
                            $"SELECT SourceEntityId FROM Link WHERE (Link.LinkTypeId = @LinkTypeId) AND TargetEntityId IN ({string.Join(",", batch)})";
                        connection.Open();

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                result.Add(reader.GetInt32(0));
                            }
                        }

                        connection.Close();
                    }
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    const string Message = "An unexpected error occurred when getting source entitites for targets";
                    Log.Error(ex, Message);
                    throw ErrorUtility.GetDataAccessException(Message, ex);
                }
            }

            return result;
        }

        private List<int> GetTargetsForSources(string linkTypeId, List<int> sources)
        {
            List<int> ids = new List<int>();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();
                    command.Parameters.AddWithValue("@LinkTypeId", linkTypeId);
                    List<List<int>> splitList = Core.Util.Utilities.SplitListIntoBatches(sources);
                    foreach (var batch in splitList)
                    {
                        command.CommandText =
                            $"SELECT TargetEntityId FROM Link WHERE (Link.LinkTypeId = @LinkTypeId) AND SourceEntityId IN ({string.Join(",", batch)})";
                        connection.Open();

                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                ids.Add(reader.GetInt32(0));
                            }

                            reader.Close();
                        }

                        connection.Close();
                    }
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    const string Message = "An unexpected error occurred when getting target entitites for sources";
                    Log.Error(ex, Message);
                    throw ErrorUtility.GetDataAccessException(Message, ex);
                }
            }

            return ids;
        }

        #endregion

        private void ReCalculateEntityMainPictureForResource(int entityId)
        {
            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "UPDATE Entity SET [MainPicture] = [Field].Value, MainPictureUrl = [ResourceFile].Url FROM Field WITH (NOLOCK) " +
                                            " INNER JOIN Entity WITH (NOLOCK) ON Field.EntityId = Entity.Id " +
                                            " INNER JOIN ResourceFile WITH (NOLOCK) ON Field.Value = dbo.ResourceFile.Id " +
                                            " WHERE Field.FieldTypeId = 'ResourceFileId' AND Entity.Id = @EntityId";

                    command.Parameters.AddWithValue("@EntityId", entityId);

                    connection.Open();
                    command.ExecuteNonQuery();
                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when re-calculating main picture id for Resource " + entityId);
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when re-calculating main picture id for Resource " + entityId, ex);
                }
            }
        }

        private List<int> ParseSpecificationQuery(Criteria criteria)
        {
            if (criteria == null || string.IsNullOrEmpty(criteria.FieldTypeId))
            {
                return new List<int>();
            }

            List<int> ids = new List<int>();
            bool firstIteration = true;

            if (criteria.Operator == Operator.ContainsAll)
            {
                if (criteria.Value == null)
                {
                    return new List<int>();
                }

                foreach (string value in this.SplitCriteraValues(criteria.Value.ToString()))
                {
                    List<int> hits = this.ExecuteSpecificationQuery(new Criteria { FieldTypeId = criteria.FieldTypeId, Operator = Operator.Contains, Value = value });

                    if (firstIteration)
                    {
                        ids.AddRange(hits);
                        firstIteration = false;
                        continue;
                    }

                    ids = ids.Intersect(hits).ToList();
                }

                return ids;
            }

            if (criteria.Operator == Operator.ContainsAny)
            {
                if (criteria.Value == null)
                {
                    return new List<int>();
                }

                foreach (string value in this.SplitCriteraValues(criteria.Value.ToString()))
                {
                    List<int> hits = this.ExecuteSpecificationQuery(new Criteria { FieldTypeId = criteria.FieldTypeId, Operator = Operator.Contains, Value = value });

                    if (firstIteration)
                    {
                        ids.AddRange(hits);
                        firstIteration = false;
                        continue;
                    }

                    ids = ids.Union(hits).ToList();
                }

                return ids;
            }

            if (criteria.Operator == Operator.NotContainsAll)
            {
                if (criteria.Value == null)
                {
                    return new List<int>();
                }

                foreach (string value in this.SplitCriteraValues(criteria.Value.ToString()))
                {
                    List<int> hits = this.ExecuteSpecificationQuery(new Criteria { FieldTypeId = criteria.FieldTypeId, Operator = Operator.NotContains, Value = value });

                    if (firstIteration)
                    {
                        ids.AddRange(hits);
                        firstIteration = false;
                        continue;
                    }

                    ids = ids.Intersect(hits).ToList();
                }

                return ids;
            }

            if (criteria.Operator == Operator.NotContainsAny)
            {
                if (criteria.Value == null)
                {
                    return new List<int>();
                }

                foreach (string value in this.SplitCriteraValues(criteria.Value.ToString()))
                {
                    List<int> hits = this.ExecuteSpecificationQuery(new Criteria { FieldTypeId = criteria.FieldTypeId, Operator = Operator.NotContains, Value = value });

                    if (firstIteration)
                    {
                        ids.AddRange(hits);
                        firstIteration = false;
                        continue;
                    }

                    ids = ids.Union(hits).ToList();
                }

                return ids;
            }

            return this.ExecuteSpecificationQuery(criteria);
        }

        private List<string> SplitCriteraValues(string value)
        {
            if (string.IsNullOrEmpty(value))
            {
                return new List<string>();
            }

            return value.Split(new[] { ';' }, StringSplitOptions.RemoveEmptyEntries).ToList();
        }

        private List<int> ExecuteSpecificationQuery(Criteria criteria)
        {
            List<int> ids = new List<int>();
            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "QuerySpecificationData";
                    command.CommandType = CommandType.StoredProcedure;

                    command.Parameters.AddWithValue("@FieldTypeId", criteria.FieldTypeId);
                    command.Parameters.AddWithValue("@Operator", criteria.Operator.ToString());

                    if (criteria.Value == null)
                    {
                        command.Parameters.AddWithValue("@Data", DBNull.Value);
                    }
                    else
                    {
                        var fieldType = this.GetSpecificationFieldType(criteria.FieldTypeId);

                        // If query on DateTime, secure that it is in correct internal format.
                        if (fieldType?.DataType == "DateTime")
                        {
                            if (criteria.Value is string)
                            {
                                criteria.Value = DateTime.Parse((string)criteria.Value)
                                    .ToString(Constants.InternalDateTimeFormat, CultureInfo.InvariantCulture);
                            }
                        }

                        command.Parameters.AddWithValue("@Data", criteria.Value);
                    }

                    if (string.IsNullOrEmpty(criteria.Language))
                    {
                        command.Parameters.AddWithValue("@Language", DBNull.Value);
                    }
                    else
                    {
                        command.Parameters.AddWithValue("@Language", criteria.Language);
                    }

                    command.CommandTimeout = 9000;

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            ids.Add(reader.GetInt32(0));
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when querying specification data");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when querying specification data", ex);
                }
            }

            return ids;
        }

        private List<int> SearchByCriteria(Criteria criteria)
        {
            List<int> searchHits = new List<int>();

            if ((this.FieldTypeIsMultivalue(criteria.FieldTypeId) || criteria.Operator == Operator.ContainsAny) ||
                (this.FieldTypeIsCVL(criteria.FieldTypeId) && criteria.Operator == Operator.NotContainsAny))
            {
                List<string> values = this.SplitCriteraValues((string)criteria.Value);

                if (criteria.Operator == Operator.ContainsAny)
                {
                    foreach (string value in values)
                    {
                        Criteria crit = new Criteria();
                        crit.FieldTypeId = criteria.FieldTypeId;
                        crit.Language = criteria.Language;
                        crit.Operator = Operator.Contains;
                        crit.Value = value;

                        List<int> ids = context.DataPersistance.Search(crit);

                        searchHits = searchHits.Union(ids).ToList();
                    }

                    return searchHits;
                }

                if (criteria.Operator == Operator.ContainsAll)
                {
                    bool first = true;
                    foreach (string value in values)
                    {
                        Criteria crit = new Criteria();
                        crit.FieldTypeId = criteria.FieldTypeId;
                        crit.Language = criteria.Language;
                        crit.Operator = Operator.Contains;
                        crit.Value = value;

                        List<int> ids = context.DataPersistance.Search(crit);
                        if (first)
                        {
                            first = false;
                            searchHits = ids;
                        }
                        else
                        {
                            searchHits = searchHits.Intersect(ids).ToList();
                        }
                    }

                    return searchHits;
                }

                if (criteria.Operator == Operator.NotContainsAll)
                {
                    foreach (string value in values)
                    {
                        Criteria crit = new Criteria();
                        crit.FieldTypeId = criteria.FieldTypeId;
                        crit.Language = criteria.Language;
                        crit.Operator = Operator.NotEqual;
                        crit.Value = value;

                        List<int> ids = context.DataPersistance.Search(crit);

                        searchHits = searchHits.Union(ids).ToList();
                    }

                    return searchHits;
                }

                if (criteria.Operator == Operator.NotContainsAny)
                {
                    bool first = true;
                    foreach (string value in values)
                    {
                        Criteria crit = new Criteria();
                        crit.FieldTypeId = criteria.FieldTypeId;
                        crit.Language = criteria.Language;
                        crit.Operator = Operator.NotEqual;
                        crit.Value = value;

                        List<int> ids = context.DataPersistance.Search(crit);

                        if (first)
                        {
                            first = false;
                            searchHits = ids;
                        }
                        else
                        {
                            searchHits = searchHits.Intersect(ids).ToList();
                        }
                    }

                    return searchHits;
                }
            }

            if (criteria.Interval.HasValue && criteria.Interval.Value)
            {
                TimeIntervalQueryUtil.GetCriteriaValueFromDatetimeInterval(criteria, useUTC: false);
            }

            searchHits = context.DataPersistance.Search(criteria);

            return searchHits.Distinct().ToList();
        }

        private bool FieldTypeIsMultivalue(string fieldTypeId)
        {
            if (string.IsNullOrEmpty(fieldTypeId))
            {
                return false;
            }

            FieldType fieldType = context.DataPersistance.GetFieldType(fieldTypeId);

            if (fieldType == null)
            {
                return false;
            }

            return fieldType.Multivalue;
        }

        private bool FieldTypeIsCVL(string fieldTypeId)
        {
            if (string.IsNullOrEmpty(fieldTypeId))
            {
                return false;
            }

            FieldType fieldType = context.DataPersistance.GetFieldType(fieldTypeId);

            if (fieldType == null)
            {
                return false;
            }

            return !string.IsNullOrEmpty(fieldType.CVLId);
        }

        private LocaleString GetLatestLanguagesFromFieldHistory(int entityId, string fieldTypeId)
        {
            var result = new LocaleString();
            var languageValueRevision = this.GetLatestLanguageRevisions(entityId, fieldTypeId);
            foreach (var revision in languageValueRevision)
            {
                var ci = new CultureInfo(revision.Language.Name);
                result.Languages.Add(ci);
                result[ci] = revision.Value;
            }

            return result;
        }

        private List<RevisionModel> GetLatestRevisionValue(int entityId, string fieldTypeId)
        {
            using (var connection = new SqlConnection(this.ConnectionString))
            {

                try
                {
                    var revisions = connection.Query<RevisionModel>(
                        @"SELECT [Value], [Revision]
                        FROM [dbo].[FieldRevisionHistory]
                        WHERE [EntityId] = @EntityId AND [FieldTypeId] = @FieldTypeId AND [Revision] = (SELECT MAX([Revision]) FROM [dbo].[FieldRevisionHistory] WHERE [EntityId] = @EntityId AND [FieldTypeId] = @FieldTypeId)",
                        new { EntityId = entityId, FieldTypeId = fieldTypeId });

                    return revisions.ToList();
                }
                catch (Exception exception)
                {
                    Log.Error(exception, "An unexpected error occurred when get latest languages from field history");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when get latest languages from field history", exception);
                }
            }
        }

        private List<LanguageValueRevision> GetLatestLanguageRevisions(int entityId, string fieldTypeId)
        {
            var result = new List<LanguageValueRevision>();
            using (var connection = new SqlConnection(this.ConnectionString))
            {

                try
                {
                    // using this query because it is faster than using JOIN of max revision per language
                    var revisions = connection.Query(@"SELECT [Language], [Value] COLLATE Latin1_General_BIN as [Value], MAX(Revision) as [Revision]
                                            FROM FieldRevisionHistory
                                            WHERE EntityId = @EntityId AND FieldTypeId = @FieldTypeId
                                            GROUP BY [Language], [Value] COLLATE Latin1_General_BIN
                                            ORDER BY 3 DESC", new { EntityId = entityId, FieldTypeId = fieldTypeId });

                    // filter out the result to minimize loop
                    var maxRevisionByLanguage = revisions
                        .Where(r => !string.IsNullOrWhiteSpace(r.Language))
                        .GroupBy(r => r.Language)
                        .Select(r => r.First())
                        .ToList();

                    foreach (var language in maxRevisionByLanguage)
                    {
                        result.Add(new LanguageValueRevision()
                        {
                            Revision = language.Revision,
                            Language = new CultureInfo(language.Language),
                            Value = language.Value?.ToString() ?? string.Empty
                        });
                    }
                }
                catch (Exception exception)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(exception, "An unexpected error occurred when get latest languages from field history");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when get latest languages from field history", exception);
                }

                return result;
            }
        }
        private Dictionary<string, List<string>> GetAllFieldTypeIdsForEntityTypes(List<string> entityTypeIds)
        {
            Dictionary<string, List<string>> fieldTypeIds = new Dictionary<string, List<string>>();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = $"SELECT DISTINCT Id, EntityTypeId FROM FieldType WHERE EntityTypeId IN ('{string.Join("','", entityTypeIds)}')";

                    connection.Open();

                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            string fieldTypeId = reader.GetString(0);
                            string entityTypeId = reader.GetString(1);

                            if (!fieldTypeIds.ContainsKey(entityTypeId))
                            {
                                fieldTypeIds.Add(entityTypeId, new List<string>());
                            }

                            fieldTypeIds[entityTypeId].Add(fieldTypeId);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception exception)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(exception, "An unexpected error occurred when get field type ids for entity types");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when get field type ids for entity types", exception);
                }
            }

            return fieldTypeIds;
        }

        private async Task<Dictionary<string, List<string>>> GetAllFieldTypeIdsForEntityTypesAsync(List<string> entityTypeIds)
        {
            Dictionary<string, List<string>> fieldTypeIds = new Dictionary<string, List<string>>();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = $"SELECT DISTINCT Id, EntityTypeId FROM FieldType WHERE EntityTypeId IN ('{string.Join("','", entityTypeIds)}')";

                    connection.Open();

                    using (SqlDataReader reader = await command.ExecuteReaderAsync())
                    {
                        while (reader.Read())
                        {
                            string fieldTypeId = reader.GetString(0);
                            string entityTypeId = reader.GetString(1);

                            if (!fieldTypeIds.ContainsKey(entityTypeId))
                            {
                                fieldTypeIds.Add(entityTypeId, new List<string>());
                            }

                            fieldTypeIds[entityTypeId].Add(fieldTypeId);
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception exception)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(exception, "An unexpected error occurred when get field type ids for entity types");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when get field type ids for entity types", exception);
                }
            }

            return fieldTypeIds;
        }

        #endregion

        #region FieldRevisionHistory

        public void SaveEntityFieldRevisionHistory(int entityId, List<Field> changedFields, bool entityIsNew,
            string userName = "system")
        {
            if (!changedFields.Any())
            {
                return;
            }

            var fieldsToUpdate = changedFields.FindAll(f => f.FieldType.TrackChanges);

            if (!fieldsToUpdate.Any())
            {
                return;
            }
            
            Task.Run(() => this.StoreEntityFieldRevisionHistory(entityId, fieldsToUpdate, entityIsNew, userName));
        }

        public void SaveEntityFieldRevisionHistorySynchronous(int entityId, List<Field> changedFields, bool entityIsNew)
        {
            if (!changedFields.Any())
            {
                return;
            }

            List<Field> fieldsToUpdate = changedFields.FindAll(f => f.FieldType.TrackChanges);

            if (!fieldsToUpdate.Any())
            {
                return;
            }

            this.StoreEntityFieldRevisionHistory(entityId, fieldsToUpdate, entityIsNew, this.context.Username);
        }

        private void StoreEntityFieldRevisionHistory(int entityId, List<Field> fieldsToUpdate, bool entityIsNew, string username)
        {
            if (!fieldsToUpdate.Any())
            {
                return;
            }

            using (var stringWriter = new System.IO.StringWriter())
            using (var xmlWriter = XmlWriter.Create(stringWriter))
            {
                var changeSet = this.GetEntityChangeSet(entityId, fieldsToUpdate, entityIsNew);
                var serializer = new XmlSerializer(typeof(EntityChangeSet));

                serializer.Serialize(xmlWriter, changeSet);
                var entityChangeSetXml = stringWriter.ToString();

                using (var connection = new SqlConnection(this.ConnectionString))
                {
                    var command = connection.CreateCommand();

                    command.CommandType = CommandType.StoredProcedure;
                    command.CommandText = "[dbo].[SaveEntityFieldRevisionHistory]";
                    var xmlParameter = new SqlParameter("@EntityChangeSetXml", SqlDbType.Xml) { Value = entityChangeSetXml };
                    command.Parameters.Add(xmlParameter);
                    command.Parameters.AddWithValue("@UserName", username);
                    connection.Open();
                    command.ExecuteNonQuery();
                    connection.Close();
                }
            }
        }

        /// <summary>
        /// Stores the updated entity field revision history. Do not use for new entities. 
        /// </summary>
        public void SaveUpdatedEntityFieldRevisionHistory(int entityId, List<Field> changedFields, List<Field> persistedFields, string userName = "system")
        {
            var (revisionFields, historyFields) = FieldRevisionHistoryHelper.GetUpdatedFieldsForFieldRevisionHistory(changedFields, persistedFields);

            Task.Run(() =>
                this.StoreUpdatedEntityFieldRevisionHistory(
                    entityId,
                    revisionFields,
                    historyFields,
                    userName));
        }

        /// <summary>
        /// Stores the updated entity field revision history. Do not use for new entities. 
        /// </summary>
        private void StoreUpdatedEntityFieldRevisionHistory(int entityId, List<Field> fieldsToUpdate, List<Field> persistedFields, string username)
        {
            // add untracked values in history
            this.StoreEntityFieldRevisionHistory(entityId, persistedFields, entityIsNew: false, "system (untracked)");

            // add new values in history
            this.StoreEntityFieldRevisionHistory(entityId, fieldsToUpdate, entityIsNew: false, username);
        }

        #endregion

        private EntityChangeSet GetEntityChangeSet(int entityId, IEnumerable<Field> changedFields, bool entityIsNew)
        {
            var entityChangeSet = new EntityChangeSet();
            foreach (var field in changedFields)
            {
                if (entityIsNew)
                {
                    field.Revision = 1;
                }

                if (field.FieldType.DataType == DataType.LocaleString)
                {
                    entityChangeSet.Values.AddRange(entityIsNew
                        ? this.GetInitialFieldChangeSetListForLocaleString(entityId, field)
                        : this.GetUpdatedFieldChangeSetListForLocaleString(entityId, field));
                }
                else if (!entityIsNew || field.Data != null)
                {
                    var changeSet = this.GetFieldChangeSet(entityId, field);
                    if (changeSet != null)
                    {
                        entityChangeSet.Values.Add(changeSet);
                    }
                }
            }

            var indexCounter = 1;
            foreach (var fieldChangeSet in entityChangeSet.Values)
            {
                fieldChangeSet.Index = indexCounter;
                indexCounter++;
            }
            return entityChangeSet;
        }

        private FieldChangeSet GetFieldChangeSet(int entityId, Field field)
        {
            var value = string.Empty;
            var revisions = this.GetLatestRevisionValue(entityId, field.FieldType.Id);

            if (field.FieldType.DataType == DataType.DateTime)
            {
                if (field.Data != null)
                {
                    value = ((DateTime)field.Data).ToString(Constants.InternalDateTimeFormat, CultureInfo.InvariantCulture);
                }
            }
            else
            {
                value = field.Data?.ToString();
            }

            if ((revisions.Count == 0 && field.Data == null) || revisions.Any(x => (x.Value ?? string.Empty) == (value ?? string.Empty)))
            {
                return null;
            }

            var revision = field.Revision;
            if (revisions.Count > 0)
            {
                revision = field.Revision > revisions[0].Revision ? field.Revision : field.Revision + 1;
            }

            var fieldChangeSet = new FieldChangeSet(field.FieldType.DataType)
            {
                EntityId = entityId,
                FieldTypeId = field.FieldType.Id,
                Value = value,
                Language = string.Empty,
                Revision = revision
            };

            return fieldChangeSet;
        }

        private List<FieldChangeSet> GetInitialFieldChangeSetListForLocaleString(int entityId, Field field)
        {
            var fieldChangeSetList = new List<FieldChangeSet>();
            if (field.FieldType.DataType != DataType.LocaleString)
            {
                return fieldChangeSetList;
            }

            if (!(field.Data is LocaleString fieldData))
            {
                return fieldChangeSetList;
            }

            if (Utility.StringIsInriverExpression(field.FieldType.ExpressionSupport, field.Data as string))
            {
                fieldChangeSetList.Add(new FieldChangeSet(field.FieldType.DataType)
                {
                    EntityId = entityId,
                    FieldTypeId = field.FieldType.Id,
                    Value = field.Data as string,
                    Revision = field.Revision
                });

                return fieldChangeSetList;
            }

            foreach (var culture in fieldData.Languages)
            {
                var fieldText = fieldData[culture];
                if (string.IsNullOrWhiteSpace(fieldText))
                {
                    continue;
                }

                var fieldChangeSet = new FieldChangeSet(field.FieldType.DataType)
                {
                    EntityId = entityId,
                    FieldTypeId = field.FieldType.Id,
                    Value = fieldText,
                    Language = culture.Name,
                    Revision = 1 // revision should be 1 for new entity
                };

                fieldChangeSetList.Add(fieldChangeSet);
            }

            return fieldChangeSetList;
        }

        private List<FieldChangeSet> GetUpdatedFieldChangeSetListForLocaleString(int entityId, Field field)
        {
            var fieldChangeSetList = new List<FieldChangeSet>();

            if (field.FieldType.DataType != DataType.LocaleString)
            {
                return fieldChangeSetList;
            }

            FieldChangeSet fieldChangeSet;
            var languageRevisions = this.GetLatestLanguageRevisions(entityId, field.FieldType.Id);
            var maxLanguageRevisionNumber = languageRevisions.Count > 0 ? languageRevisions.Max(l => l.Revision) : 0;
            //the revision should always increase. This is to handle NDL not updating the revision number when not being tracked.
            var revision = field.Revision > maxLanguageRevisionNumber ? field.Revision : field.Revision + 1;

            if (Utility.StringIsInriverExpression(field.FieldType.ExpressionSupport, field.Data as string))
            {
                fieldChangeSetList.Add(new FieldChangeSet(field.FieldType.DataType)
                {
                    EntityId = entityId,
                    FieldTypeId = field.FieldType.Id,
                    Value = field.Data as string,
                    Revision = revision
                });

                return fieldChangeSetList;
            }

            if (field.Data is LocaleString fieldData)
            {
                
                foreach (var culture in fieldData.Languages)
                {
                    var fieldText = fieldData[culture] ?? string.Empty;
                    var languageRevisionValue = languageRevisions.FirstOrDefault(l => l.Language.Name == culture.Name)?.Value ?? string.Empty;

                    if (fieldText == languageRevisionValue)
                    {
                        continue;
                    }

                    fieldChangeSet = new FieldChangeSet(field.FieldType.DataType)
                    {
                        EntityId = entityId,
                        FieldTypeId = field.FieldType.Id,
                        Value = fieldText,
                        Language = culture.Name,
                        Revision = revision
                    };

                    fieldChangeSetList.Add(fieldChangeSet);
                }
            }
            else
            {
                if (maxLanguageRevisionNumber > 0)
                {
                    fieldChangeSet = new FieldChangeSet(field.FieldType.DataType)
                    {
                        EntityId = entityId,
                        FieldTypeId = field.FieldType.Id,
                        Value = string.Empty,
                        Language = string.Empty,
                        Revision = revision
                    };
                    fieldChangeSetList.Add(fieldChangeSet);
                }
            }
            return fieldChangeSetList;
        }

        public void InsertRowIntoStagingTable(DataRow row)
        {

            using (SqlConnection connection = new SqlConnection(context.ConnectionString))
            {
                try
                {
                    connection.Open();
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "Insert Into ExcelImportStagingTable (BatchId, inRiverImportColumnId, sys_id, ImportType, EntityType, EntityAsJson, ImportState, ImportStatus, ImportMessage, PostStatus, PostMessage)" +
                                          " VALUES (@BatchId, @inRiverImportColumnId, @sys_id, @ImportType, @EntityType, @EntityAsJson,  @ImportState, @ImportStatus, @ImportMessage, @PostStatus, @PostMessage)";

                    command.Parameters.Clear();
                    command.Parameters.AddWithValue("@BatchId", row["BatchId"]);
                    command.Parameters.AddWithValue("@inRiverImportColumnId", row["inRiverImportColumnId"]);
                    command.Parameters.AddWithValue("@sys_id", row["sys_id"]);
                    command.Parameters.AddWithValue("@ImportType", row["ImportType"]);
                    command.Parameters.AddWithValue("@EntityType", row["entityType"]);
                    command.Parameters.AddWithValue("@EntityAsJson", row["EntityAsJson"]);
                    command.Parameters.AddWithValue("@ImportState", row["ImportState"]);
                    command.Parameters.AddWithValue("@ImportStatus", row["ImportStatus"]);
                    command.Parameters.AddWithValue("@ImportMessage", row["ImportMessage"]);
                    command.Parameters.AddWithValue("@PostStatus", row["PostStatus"]);
                    command.Parameters.AddWithValue("@PostMessage", row["PostMessage"]);

                    command.ExecuteNonQuery();

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, $"An unexpected error occurred when inserting batch into staging table.");

                    throw;
                }

            }

        }

        public DataTable GetTableWithRowToProcessFromStagingTable(string batchId)
        {
            var dataTable = new DataTable();

            using (SqlConnection connection = new SqlConnection(context.ConnectionString))
            {
                try
                {
                    connection.Open();
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = "Select top (1) * from ExcelImportStagingTable WHERE (BatchId=@BatchId and ImportStatus is NULL)";
                    command.Parameters.Clear();
                    command.Parameters.AddWithValue("@BatchId", batchId);

                    using (SqlDataAdapter da = new SqlDataAdapter(command))
                    {
                        // this will query your database and return the result to your datatable
                        da.Fill(dataTable);
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, $"An unexpected error occurred when inserting batch into staging table.");
                }
            }

            return dataTable;
        }

        public void SetStagingRow(DataRow row)
        {
            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();
                    command.CommandText =
                        "UPDATE ExcelImportStagingTable set sys_id= @sys_id, ImportType=@ImportType, EntityAsJson=@EntityAsJson, ImportState=@ImportState, ImportStatus=@ImportStatus, ImportMessage=@ImportMessage, PostStatus=@PostStatus, PostMessage=@PostMessage where inRiverImportColumnId = @inRiverImportColumnId";
                    command.Parameters.AddWithValue("@sys_id", row["sys_id"]);
                    command.Parameters.AddWithValue("@ImportType", row["ImportType"]);
                    command.Parameters.AddWithValue("@EntityAsJson", row["EntityAsJson"]);
                    command.Parameters.AddWithValue("@ImportState", row["ImportState"]);
                    command.Parameters.AddWithValue("@ImportStatus", row["ImportStatus"]);
                    command.Parameters.AddWithValue("@ImportMessage", row["ImportMessage"]);
                    command.Parameters.AddWithValue("@postStatus", row["postStatus"]);
                    command.Parameters.AddWithValue("@postMessage", row["postMessage"]);
                    command.Parameters.AddWithValue("@inRiverImportColumnId", row["inRiverImportColumnId"]);
                    connection.Open();
                    command.ExecuteNonQuery();
                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }
                    Log.Error(ex, $"An unexpected error occurred when updating excel import status. inRiverImportId:{row["inRiverImportId"].ToString()}.");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when updating excel import status.", ex);
                }
            }
        }

        public void SetImportStatus(string inRiverImportId, string status, string message)
        {
            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText =
                        "UPDATE ExcelImportStagingTable set ImportStatus= @importStatus, ImportMessage=@importMessage where inRiverImportColumnId = @inRiverImportId";

                    command.Parameters.AddWithValue("@importStatus", status);
                    command.Parameters.AddWithValue("@ImportMessage", message);
                    command.Parameters.AddWithValue("@inRiverImportId", inRiverImportId);

                    connection.Open();
                    command.ExecuteNonQuery();
                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, $"An unexpected error occurred when updating excel import status. inRiverImportId:{inRiverImportId}.");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when updating excel import status.", ex);
                }
            }
        }

        public void SetPostProcessStatus(string inRiverImportId, string status, string message)
        {
            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText =
                        "UPDATE ExcelImportStagingTable set PostStatus= @postStatus, PostMessage=@postMessage where inRiverImportColumnId = @inRiverImportId";

                    command.Parameters.AddWithValue("@postStatus", status);
                    command.Parameters.AddWithValue("@postMessage", message);
                    command.Parameters.AddWithValue("@inRiverImportId", inRiverImportId);

                    connection.Open();
                    command.ExecuteNonQuery();
                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, $"An unexpected error occurred when updating excel import status. inRiverImportId:{inRiverImportId}.");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when updating excel import status.", ex);
                }
            }
        }

        public List<int> GetSysIdsForImportJob(string jobId)
        {
            List<int> sysIds = new List<int>();

            using (SqlConnection connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    SqlCommand command = connection.CreateCommand();

                    command.CommandText = @"select Sys_Id
                        from ExcelImportStagingTable
                        where BatchId = (select Identifier from longrunningjob where id=@jobId)
                        AND ImportStatus=@imported";

                    command.Parameters.AddWithValue("@jobId", jobId);
                    command.Parameters.AddWithValue("@imported", ImportState.Imported.ToString());

                    connection.Open();
                    using (SqlDataReader reader = command.ExecuteReader())
                    {
                        while (reader.Read())
                        {
                            if (!reader.IsDBNull(0))
                            {
                                var entityId = reader.GetInt32(0);
                                sysIds.Add(entityId);
                            }
                        }

                        reader.Close();
                    }

                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, $"An unexpected error occurred when retrieving excel import sys ids. jobId:{jobId}.");
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when retrieving excel import sys ids.", ex);
                }
            }

            return sysIds;
        }

        public string GetExcelModel(string username, string batchId, bool configData)
        {
            string data = "";
            if (configData)
            {
                using (SqlConnection connection = new SqlConnection(this.ConnectionString))
                {
                    try
                    {
                        SqlCommand command = connection.CreateCommand();

                        command.CommandText =
                            $"SELECT Username, Timestamp, ConfigurationData FROM ExcelImportData WHERE Username = @username and BatchId=@batchId";

                        command.Parameters.AddWithValue("@Username", username);
                        command.Parameters.AddWithValue("@batchId", batchId);

                        command.CommandTimeout = 360;

                        connection.Open();
                        using (SqlDataReader reader = command.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                data = reader.GetString(2);
                            }

                            reader.Close();
                        }

                        connection.Close();
                    }
                    catch (Exception ex)
                    {
                        if (connection.State != ConnectionState.Closed)
                        {
                            connection.Close();
                        }

                        Log.Error(ex, "An unexpected error occurred when getting excel data");
                        throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting excel data",
                            ex);
                    }
                }
            }
            else
            {
                CloudBlobManager cloudBlobManager = new CloudBlobManager(this.context, "excelimport");
                var compressedData = cloudBlobManager.GetFile(batchId);
                var importData = CompressionHandler.GZip.Decompress(compressedData);
                return Encoding.UTF8.GetString(importData, 0, importData.Length);

            }

            return data;
        }

        public IList<ResourceFile> GetResourceFiles(IList<int> resourceFileIds)
        {
            var resourceFiles = new List<ResourceFile>();
            if (resourceFileIds == null || !resourceFileIds.Any())
            {
                return resourceFiles;
            }

            const int batchSize = 4000;
            var totalIdsCount = resourceFileIds.Count;
            var batchCounter = 0;
            do
            {
                var batchIds = resourceFileIds.Skip(batchCounter).Take(batchSize).ToList();
                batchCounter += batchIds.Count;

                using var connection = new SqlConnection(this.ConnectionString);
                using var command = connection.CreateCommand();
                command.CommandText = @$"
                    SELECT Id,
                        Filename,
                        FileSize,
                        Extension,
                        DateCreated,
                        Url
                    FROM [ResourceFile] WHERE Id IN ({string.Join(",", batchIds)})";

                connection.Open();
                using var reader = command.ExecuteReader();
                var idOrdinal = reader.GetOrdinal("Id");
                var fileNameOrdinal = reader.GetOrdinal("Filename");
                var fileSizeOrdinal = reader.GetOrdinal("FileSize");
                var extensionOrdinal = reader.GetOrdinal("Extension");
                var dateCreatedOrdinal = reader.GetOrdinal("DateCreated");
                var urlOrdinal = reader.GetOrdinal("Url");

                while (reader.Read())
                {
                    var resourceField = new ResourceFile
                    {
                        Id = reader.GetInt32(idOrdinal)
                    };

                    if (!reader.IsDBNull(fileNameOrdinal))
                    {
                        resourceField.FileName = reader.GetString(fileNameOrdinal);
                    }

                    if (!reader.IsDBNull(fileSizeOrdinal))
                    {
                        resourceField.FileSize = reader.GetInt64(fileSizeOrdinal);
                    }

                    if (!reader.IsDBNull(extensionOrdinal))
                    {
                        resourceField.Extension = reader.GetString(extensionOrdinal);
                    }

                    if (!reader.IsDBNull(dateCreatedOrdinal))
                    {
                        resourceField.DateCreated = reader.GetDateTime(dateCreatedOrdinal);
                    }

                    if (!reader.IsDBNull(urlOrdinal))
                    {
                        resourceField.Url = reader.GetString(urlOrdinal);
                    }

                    resourceFiles.Add(resourceField);
                }
            } while (batchCounter < totalIdsCount);

            return resourceFiles;
        }

        public (DtoLink, bool) AddLinkIfNotExists(Remoting.Objects.Link link) => throw new System.NotImplementedException();

        public DtoLink AddLinkAt(Remoting.Objects.Link link, int index) => throw new System.NotImplementedException();

        public List<DtoLink> AddLinksToNewTask(List<Remoting.Objects.Link> links) => throw new System.NotImplementedException();

        public bool DeleteLink(int linkId)
        {
            var linkEntityChannels = this.GetLinkEntitiesToDelete(linkId);

            using (var connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    var command = connection.CreateCommand();

                    command.CommandText = "DELETE FROM Link WITH (ROWLOCK) WHERE Id = @Id;";

                    command.Parameters.AddWithValue("@Id", linkId);

                    connection.Open();
                    command.ExecuteNonQuery();
                    connection.Close();
                }
                catch (Exception ex)
                {
                    if (connection.State != ConnectionState.Closed)
                    {
                        connection.Close();
                    }

                    Log.Error(ex, "An unexpected error occurred when deleting link " + linkId);
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when deleting  link " + linkId, ex);
                }
            }

            this.DeleteLinkEntities(linkEntityChannels);

            return true;
        }

        public Comment AddComment(Comment comment)
        {
            var now = DateTime.UtcNow;

            using (var connection = new SqlConnection(this.ConnectionString))
            {
                try
                {
                    var command = connection.CreateCommand();

                    command.CommandText = "INSERT INTO EntityComment (EntityId, Author, Created, Text) VALUES (@EntityId, @Author, @Created, @Text) SET @Id = SCOPE_IDENTITY()";

                    _ = command.Parameters.AddWithValue("@EntityId", comment.EntityId);
                    _ = command.Parameters.AddWithValue("@Created", now);
                    _ = command.Parameters.AddWithValue("@Text", comment.Text);

                    var idParameter = new SqlParameter("@Id", SqlDbType.Int)
                    {
                        Direction = ParameterDirection.Output
                    };
                    _ = command.Parameters.Add(idParameter);

                    if (string.IsNullOrWhiteSpace(comment.Author))
                    {
                        _ = command.Parameters.AddWithValue("@Author", DBNull.Value);
                    }
                    else
                    {
                        _ = command.Parameters.AddWithValue("@Author", comment.Author);
                    }

                    connection.Open();
                    _ = command.ExecuteNonQuery();

                    comment.Id = (int)idParameter.Value;

                    comment.Created = now;
                }
                catch (FaultException)
                {
                    throw;
                }
                catch (Exception ex)
                {
                    Log.Error("An unexpected error occurred when adding comment", ex);
                    throw ErrorUtility.GetDataAccessException("An unexpected error occurred when adding comment", ex);
                }
            }

            return comment;
        }

        private Dictionary<int, List<int>> GetLinkEntitiesToDelete(int linkId)
        {
            var linkEntityIds = this.GetLinkEntityIdsToDelete(linkId);
            return this.GetLinkEntityChannels(linkEntityIds);
        }

        private List<int> GetLinkEntityIdsToDelete(int linkId)
        {
            var ids = new List<int>();
            var isLinkEntity = false;

            using var connection = new SqlConnection(this.ConnectionString);
            try
            {
                var command = connection.CreateCommand();

                command.CommandText = "SELECT LinkEntityId FROM Link WITH (NOLOCK) WHERE Id = @LinkId";

                command.Parameters.AddWithValue("@LinkId", linkId);

                connection.Open();

                using (var reader = command.ExecuteReader())
                {
                    if (reader.Read() && !reader.IsDBNull(0))
                    {
                        isLinkEntity = true;
                    }

                    reader.Close();
                }

                if (!isLinkEntity)
                {
                    connection.Close();
                    return new List<int>();
                }

                command.CommandText = "SELECT li1.LinkEntityId, Count(li1.LinkEntityId) AS [count] " +
                                      "FROM Link li1 WITH (NOLOCK) INNER JOIN Link li2 WITH (NOLOCK) ON li1.LinkEntityId = li2.LinkEntityId " +
                                      "WHERE li1.Id = @LinkId " +
                                      "GROUP BY li1.LinkEntityId";

                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        var count = reader.GetInt32(1);

                        if (count == 1)
                        {
                            ids.Add(reader.GetInt32(0));
                        }
                    }

                    reader.Close();
                }

                connection.Close();
            }
            catch (Exception ex)
            {
                if (connection.State != ConnectionState.Closed)
                {
                    connection.Close();
                }

                Log.Error(ex, "An unexpected error occurred when finding link entity ids");
                throw ErrorUtility.GetDataAccessException("An unexpected error occurred when finding link entity ids", ex);
            }

            return ids;
        }

        private Dictionary<int, List<int>> GetLinkEntityChannels(List<int> linkEntityIds)
        {
            var linkEntityChannels = new Dictionary<int, List<int>>();

            if (!linkEntityIds.Any())
            {
                return linkEntityChannels;
            }

            foreach (var linkEntityId in linkEntityIds)
            {
                var channelIds = this.GetChannelLinkEntity(linkEntityId);

                if (!linkEntityChannels.ContainsKey(linkEntityId))
                {
                    linkEntityChannels.Add(linkEntityId, new List<int>());
                }

                foreach (var channelId in channelIds.Keys)
                {
                    if (channelIds[channelId] != "Channel")
                    {
                        continue;
                    }

                    linkEntityChannels[linkEntityId].Add(channelId);
                }
            }

            return linkEntityChannels;
        }

        private Dictionary<int, string> GetChannelLinkEntity(int linkEntityId)
        {
            var channelIds = new Dictionary<int, string>();

            using var connection = new SqlConnection(this.ConnectionString);
            try
            {
                var command = connection.CreateCommand();

                command.CommandText = "SELECT DISTINCT ChannelId, ChannelType FROM ChannelStructure WITH (NOLOCK) WHERE LinkEntityId = @EntityId";
                command.Parameters.AddWithValue("@EntityId", linkEntityId);

                connection.Open();

                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        var channelId = reader.GetInt32(0);

                        if (!channelIds.ContainsKey(channelId))
                        {
                            channelIds.Add(channelId, reader.GetString(1));
                        }
                    }

                    reader.Close();
                }

                connection.Close();
            }
            catch (Exception ex)
            {
                if (connection.State != ConnectionState.Closed)
                {
                    connection.Close();
                }

                Log.Error(ex, "An unexpected error occurred when getting channels with entity ids");
                throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting channels with entity ids", ex);
            }

            return channelIds;
        }

        private void DeleteLinkEntities(Dictionary<int, List<int>> linkEntityChannels)
        {
            foreach (var linkEntityId in linkEntityChannels.Keys)
            {
                var linkEntity = this.context.DataPersistance.GetFullEntity(linkEntityId, null);

                if (linkEntity == null)
                {
                    continue;
                }

                linkEntity.Fields = this.context.DataPersistance.GetFullFieldsForEntity(linkEntity);
                linkEntity.LoadLevel = LoadLevel.DataOnly;

                var entityXml = Utilities.EntityToMinimalXml(linkEntity);

                this.context.DataPersistance.DeleteEntity(linkEntityId);

                EventPublisher.NotifyEntityDeleted(this.context, linkEntity.Id, linkEntity.EntityType.Id, entityXml);

                foreach (var channelId in linkEntityChannels[linkEntityId])
                {
                    EventPublisher.NotifyChannelEntityDelete(this.context, channelId, linkEntityId, entityXml);
                }
            }
        }

        public bool DeleteLinksAndUpdateLinksSortOrder(int entityId, string[] linkTypeIdsToIgnore) => throw new System.NotImplementedException();

        public DtoLink UpdateLinkSortOrder(int linkId, int index) => throw new System.NotImplementedException();

        public void UpdateLinkSortOrderOnSourceEntity(int sourceEntityId, int index, string linkTypeId)
        {
            using var connection = new SqlConnection(this.ConnectionString);
            try
            {
                var command = connection.CreateCommand();

                command.CommandText = "UPDATE Link WITH (ROWLOCK) SET [Index] = [Index] - 1, LastModified = GETUTCDATE() WHERE [Index] > @Index AND [SourceEntityId] = @SourceEntityId AND LinkTypeId = @LinkTypeId;";

                command.Parameters.AddWithValue("@SourceEntityId", sourceEntityId);
                command.Parameters.AddWithValue("@Index", index);
                command.Parameters.AddWithValue("@LinkTypeId", linkTypeId);

                connection.Open();
                command.ExecuteNonQuery();
                connection.Close();
            }
            catch (Exception ex)
            {
                if (connection.State != ConnectionState.Closed)
                {
                    connection.Close();
                }

                Log.Error(ex, $"An unexpected error occurred when updating link sort order for source entity with id {sourceEntityId}");
                throw ErrorUtility.GetDataAccessException($"An unexpected error occurred when updating link sort order for source entity with id {sourceEntityId}", ex);
            }
        }

        public bool Inactivate(int id) => throw new System.NotImplementedException();

        public bool Activate(int id) => throw new System.NotImplementedException();

        public List<int> GetAllLinkIdsForEntityAndLinkType(int entityId, string linkTypeId, LinkDirection linkDirection)
        {
            List<int> linkIds;

            using var connection = new SqlConnection(this.ConnectionString);
            try
            {
                var direction = linkDirection == LinkDirection.InBound ? "TargetEntityId" : "SourceEntityId";

                linkIds = connection
                    .Query<int>(
                        $"SELECT Id FROM Link WHERE {direction} = @EntityId AND LinkTypeId = @LinkTypeId",
                        new { LinkTypeId = linkTypeId, EntityId = entityId })
                    .ToList();
            }
            catch (Exception ex)
            {

                Log.Error(ex, "An unexpected error occurred when getting all link ids for source entity and link type" + linkTypeId);
                throw ErrorUtility.GetDataAccessException("An unexpected error occurred when getting all link ids for source entity and link type" + linkTypeId, ex);
            }

            return linkIds;
        }

        public bool EntityHasExpression(int entityId, string target, string targetType)
        {
            using var connection = new SqlConnection(this.ConnectionString);
            try
            {
                var command = connection.CreateCommand();

                command.CommandText = "SELECT COUNT(1) FROM [dbo].[Expression] WHERE EntityId = @EntityId AND [Target] = @Target AND TargetType = @TargetType";

                command.Parameters.AddWithValue("@EntityId", entityId);
                command.Parameters.AddWithValue("@Target", target);
                command.Parameters.AddWithValue("@TargetType", targetType);

                connection.Open();
                using (var reader = command.ExecuteReader())
                {
                    if (reader.Read())
                    {
                        return reader.GetInt32(0) > 0;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                Log.Error(ex, $"An unexpected error occurred when checking if Entity {entityId} had an Expression for {target}");
                throw ErrorUtility.GetDataAccessException($"An unexpected error occurred when checking if Entity {entityId} had an Expression for {target}", ex);
            }
        }

        public List<int> GetEntityIdsForEntityTypeWithExpressions(string entityTypeId, string target, string targetType)
        {
            using var connection = new SqlConnection(this.ConnectionString);
            try
            {
                var command = connection.CreateCommand();

                command.CommandText = @"SELECT expr.[EntityId]
  FROM [dbo].[Expression] expr
  INNER JOIN [dbo].[Entity] e ON e.[Id] = expr.[EntityId]
  WHERE [Target] = @Target AND [TargetType] = @TargetType AND e.[EntityTypeId] = @EntityTypeId";

                command.Parameters.AddWithValue("@EntityTypeId", entityTypeId);
                command.Parameters.AddWithValue("@Target", target);
                command.Parameters.AddWithValue("@TargetType", targetType);
                var result = new List<int>();
                connection.Open();
                using (var reader = command.ExecuteReader())
                {
                    while (reader.Read())
                    {
                        result.Add(reader.GetInt32(0));
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                Log.Error(ex, $"An unexpected error occurred when fetching Entities of EntityType {entityTypeId} that have an Expression for {target}");
                throw ErrorUtility.GetDataAccessException($"An unexpected error occurred when fetching Entities of EntityType {entityTypeId} that have an Expression for {target}", ex);
            }
        }
    }

    [XmlRoot("EntityChangeSet")]
    public class EntityChangeSet
    {
        public EntityChangeSet()
        {
            this.Values = new List<FieldChangeSet>();
        }

        [XmlArray("FieldChangeSets")]
        [XmlArrayItem("FieldChangeSet")]
        public List<FieldChangeSet> Values { get; set; }
    }

    [XmlRoot("FieldChangeSet")]
    public class FieldChangeSet
    {
        private const string InternalDateTimeFormat = "yyyy-MM-dd HH\\:mm\\:ss";

        public FieldChangeSet(string dataType)
        {
            this.DataType = dataType;
        }

        private FieldChangeSet()
        {
        }

        [XmlIgnore]
        public DateTime? DateTimeValue { get; set; }

        [XmlIgnore]
        public string StringValue { get; set; }

        [XmlIgnore]
        public string DataType { get; }

        [XmlElement("Index")]
        public int Index { get; set; }

        [XmlElement("EntityId")]
        public int EntityId { get; set; }

        [XmlElement("FieldTypeId")]
        public string FieldTypeId { get; set; }

        [XmlElement("Value")]
        public string Value
        {
            get {
                if (this.DataType.Equals("DateTime"))
                {
                    var dateTimeValue = this.DateTimeValue;

                    if (!dateTimeValue.HasValue)
                    {
                        return null;
                    }

                    return dateTimeValue.Value.ToString("yyyy-MM-dd HH:mm:ss");
                }

                return this.StringValue;
            }

            set {
                if (this.DataType.Equals("DateTime"))
                {
                    if (string.IsNullOrEmpty(value))
                    {
                        this.DateTimeValue = null;
                        return;
                    }

                    this.DateTimeValue = DateTime.Parse(value);
                }
                else
                {
                    this.StringValue = value;
                }
            }
        }

        [XmlElement("Language")]
        public string Language { get; set; }

        [XmlElement("Revision")]
        public int Revision { get; set; }
    }
}
