{"$schema": "http://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:50665", "sslPort": 0}}, "profiles": {"IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "launchUrl": "/", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}, "LongRunningJobQueueService": {"commandName": "Project", "launchBrowser": true, "launchUrl": "/", "applicationUrl": "http://localhost:50666", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}}}