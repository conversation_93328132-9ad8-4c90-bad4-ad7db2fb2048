namespace inRiver.Core.Models.inRiver.Workflow
{
    using System.Collections.Generic;

    public class WorkflowInspireTranslateModel
    {
        public List<string> InspireTargetLanguages { get; set; } = new List<string>();

        public string InspireSourceLanguage { get; set; }

        public List<string> InspireFieldTypeIds { get; set; } = new List<string>();

        public bool Overwrite { get; set; }

        public bool IncludeContext { get; set; }
    }
}
