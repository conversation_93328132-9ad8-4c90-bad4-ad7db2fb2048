namespace LongRunningJobQueueService.AzureQueue.Extensions
{
    using Azure.Storage.Queues;
    using Configuration;
    using inRiver.Configuration.Core.Service;
    using Interfaces;
    using Microsoft.Extensions.Configuration;
    using Microsoft.Extensions.DependencyInjection;
    using Services;

    public static class ServiceCollectionExtensions
    {
        public static IServiceCollection AddAzureQueue(this IServiceCollection services, IConfiguration config, string keyVaultBaseUrl)
        {
            var storageConfig = GetStorageConfiguration(keyVaultBaseUrl);
            if (storageConfig == null)
            {
                return services;
            }

            services.Configure<StorageOptions>(options => {
                options.QueueName = storageConfig?.QueueName;
                options.ConnectionString = storageConfig?.ConnectionString;
            });
            services.Configure<QueueOptions>(config.GetSection("Queue"));

            return services
                .AddQueueService(storageConfig?.ConnectionString, storageConfig?.QueueName)
                .AddJobStatusQueueService();
        }

        private static (string ConnectionString, string QueueName)? GetStorageConfiguration(string keyVaultBaseUrl)
        {
            try
            {
                var queueName = KeyVaultSecretReader
                    .GetSecretValueAsync(keyVaultBaseUrl, "output-adapter-storage-queue-name").GetAwaiter()
                    .GetResult();
                var connectionString = KeyVaultSecretReader.GetSecretValueAsync(keyVaultBaseUrl, "output-adapter-storage-connection-string")
                    .GetAwaiter().GetResult();

                if (string.IsNullOrEmpty(connectionString) || string.IsNullOrEmpty(queueName))
                {
                    return null;
                }
                return (connectionString, queueName);

            }
            catch
            {
                return null;
            }
        }

        private static IServiceCollection AddJobStatusQueueService(this IServiceCollection services)
            => services.AddScoped<IJobStatusQueueService, JobStatusQueueService>();

        private static IServiceCollection AddQueueService(this IServiceCollection services, string connectionsString, string queueName)
        {
            var queueServiceClient = new QueueServiceClient(connectionsString);
            var queueClient = queueServiceClient.GetQueueClient(queueName);

            return services.AddSingleton(queueClient);
        }
    }
}
