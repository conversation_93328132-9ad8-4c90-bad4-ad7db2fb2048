namespace LongRunningJobQueueService.BackgroundServices
{
    using System;
    using System.Text.Json;
    using System.Threading;
    using System.Threading.Tasks;
    using AzureQueue.Configuration;
    using AzureQueue.Interfaces;
    using global::LongRunningJobQueueService.AzureQueue.Extensions;
    using global::LongRunningJobQueueService.Data.Exceptions;
    using global::LongRunningJobQueueService.Data.Services;
    using Microsoft.Extensions.Hosting;
    using Microsoft.Extensions.Options;
    using Serilog;

    public class JobStatusQueueBackgroundService : BackgroundService
    {
        private const string ServiceName = nameof(JobStatusQueueBackgroundService);
        private const int MaxMessages = 32;
        private const int MessageVisibilityTimeoutMinutes = 1;
        private const int MaxMessageReceiveFailures = 3;
        private readonly ILongRunningJobService longRunningJobService;
        private readonly IJobStatusQueueService jobStatusQueueService;
        private readonly StorageOptions storageOptions;
        private readonly QueueOptions queueOptions;

        public JobStatusQueueBackgroundService(
            ILongRunningJobService longRunningJobService = null,
            IJobStatusQueueService jobStatusQueueService = null,
            IOptions<StorageOptions> storageOptions = null,
            IOptions<QueueOptions> queueOptions = null)
        {
            this.longRunningJobService = longRunningJobService;
            this.jobStatusQueueService = jobStatusQueueService;
            this.storageOptions = storageOptions?.Value;
            this.queueOptions = queueOptions?.Value;
        }

        protected override async Task ExecuteAsync(CancellationToken cancellationToken)
        {
            Log.Information("{ServiceName} started", ServiceName);

            if (this.jobStatusQueueService == null
                || this.storageOptions == null
                || this.queueOptions == null
                || string.IsNullOrEmpty(this.storageOptions.ConnectionString))
            {
                Log.Warning("{ServiceName} is disabled due to missing queue configuration", ServiceName);
                return;
            }

            var isQueueExist = await this.jobStatusQueueService.ExistsAsync(cancellationToken);
            if (!isQueueExist)
            {
                Log.Error("Queue {QueueName} does not exist, {ServiceName} stopped", this.storageOptions.QueueName, ServiceName);
                return;
            }

            var messageReceiveFailuresCount = 0;
            while (!cancellationToken.IsCancellationRequested)
            {
                try
                {
                    var messages = await this.jobStatusQueueService.ReceiveMessagesAsync(MaxMessages, visibilityTimeout: TimeSpan.FromMinutes(MessageVisibilityTimeoutMinutes), cancellationToken);
                    messageReceiveFailuresCount = 0;

                    foreach (var message in messages)
                    {
                        try
                        {
                            var deserializedMessage = message.GetMessageObject();
                            await this.longRunningJobService.UpdateJobStatusAsync(deserializedMessage.CustomerSafeName,
                                deserializedMessage.EnvironmentSafeName, deserializedMessage.JobId,
                                deserializedMessage.Status);
                            await this.jobStatusQueueService.DeleteMessageAsync(message.MessageId, message.PopReceipt, cancellationToken);
                        }
                        catch (Exception ex) when (ex is EnvironmentConfigurationNotFoundException || ex is JsonException)
                        {
                            Log.Error(ex, ex.Message);
                            await this.jobStatusQueueService.DeleteMessageAsync(message.MessageId, message.PopReceipt, cancellationToken);
                        }
                        catch (Exception ex)
                        {
                            messageReceiveFailuresCount++;
                            if (messageReceiveFailuresCount <= MaxMessageReceiveFailures)
                            {
                                Log.Error(ex, "Error processing queue message {MessageText}", message.MessageText);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    messageReceiveFailuresCount++;
                    if (messageReceiveFailuresCount <= MaxMessageReceiveFailures)
                    {
                        Log.Error(ex, "Error in {ServiceName} when receiving messages from queue", ServiceName);
                    }

                    await Task.Delay(this.queueOptions.Delay, cancellationToken);
                }

                await Task.Delay(this.queueOptions.Delay, cancellationToken);
            }
        }
    }
}
