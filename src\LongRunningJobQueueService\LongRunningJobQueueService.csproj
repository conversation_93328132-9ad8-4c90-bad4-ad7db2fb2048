﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
    <IsServiceFabricServiceProject>True</IsServiceFabricServiceProject>
    <ServerGarbageCollection>True</ServerGarbageCollection>
    <RuntimeIdentifier>win-x64</RuntimeIdentifier>
    <SelfContained>True</SelfContained>
    <UserSecretsId>8f80eaf8-2299-450b-9c8f-196e100a88de</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Identity" Version="1.10.4" />
    <PackageReference Include="Inriver.StackEssentials" Version="6.0.0" />
    <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.19.0" />
    <PackageReference Include="Microsoft.ApplicationInsights.WorkerService" Version="2.19.0" />
    <PackageReference Include="Microsoft.ServiceFabric.AspNetCore.Kestrel" Version="7.1.1541" />
    <PackageReference Include="Serilog" Version="2.8.0" />
    <PackageReference Include="Serilog.Sinks.ApplicationInsights" Version="3.1.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\inRiver.Server\inRiver.Server.csproj" />
    <ProjectReference Include="..\LongRunningJobQueueService.AzureQueue\LongRunningJobQueueService.AzureQueue.csproj" />
    <ProjectReference Include="..\LongRunningJobQueueService.Data\LongRunningJobQueueService.Data.csproj" />
    <ProjectReference Include="..\Telemetry\Telemetry.csproj" />
    <ProjectReference Include="..\Telemetry.Metrics\Telemetry.Metrics.csproj" />
  </ItemGroup>


</Project>
