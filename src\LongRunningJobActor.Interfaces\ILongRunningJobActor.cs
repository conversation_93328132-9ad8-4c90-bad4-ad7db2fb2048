[assembly: Microsoft.ServiceFabric.Actors.Remoting.FabricTransport.FabricTransportActorRemotingProvider(
    RemotingListenerVersion = Microsoft.ServiceFabric.Services.Remoting.RemotingListenerVersion.V2_1,
    RemotingClientVersion = Microsoft.ServiceFabric.Services.Remoting.RemotingClientVersion.V2_1)]

namespace LongRunningJobActor.Interfaces
{
    using System;
    using System.Threading.Tasks;
    using inRiver.Core.Models.inRiver.ApplyExpressions;
    using inRiver.Core.Models.inRiver.DeleteAllLinksForLinkType;
    using inRiver.Core.Models.inRiver.ExcelExport;
    using inRiver.Core.Models.inRiver.ExcelImport;
    using inRiver.Core.Models.inRiver.MassExcludeTypes;
    using inRiver.Core.Models.inRiver.MassUpdate;
    using inRiver.Core.Models.inRiver.Workflow;
    using inRiver.Server.Request;
    using inRiver.Server.Syndication;
    using Microsoft.ServiceFabric.Actors;

    /// <summary>
    /// This interface defines the methods exposed by an actor.
    /// Clients use this interface to interact with the actor that implements it.
    /// </summary>
    public interface ILongRunningJobActor : IActor
    {
        Task ExecuteCalculateCompletenessAsync(EnvironmentContextData environmentContextData, int longRunningJobId, string entitytypeid);

        Task ExecuteRebuildChannel(EnvironmentContextData environmentContextData, int longRunningJobId, int channelId);

        Task ExecuteSynchronizeChannel(EnvironmentContextData environmentContextData, int longRunningJobId, int channelId);

        Task ExcelExportAsync(EnvironmentContextData environmentContextData, int longRunningJobId, string userEmail, ExcelExportModel excelExportModel);

        Task ExcelExportHistory(EnvironmentContextData environmentContextData, int longRunningJobId, string userEmail, ExcelExportHistoryModel excelExportHistoryModel);

        Task ExcelImport(EnvironmentContextData environmentContextData, int longRunningJobId, string userEmail, ExcelImportModel excelImportModel);

        Task RunSyndicateAsync(EnvironmentContextData environmentContextData, int longRunningJobId, SyndicationModel syndicationModel, string username);

        Task RunReviewAsync(EnvironmentContextData environmentContextData, SyndicationModel syndicationModel, Guid reviewId);

        Task MassUpdate(EnvironmentContextData environmentContextData, string username, int longRunningJobId, MassUpdateModel massUpdateModel);

        Task ContentStoreMassExcludeTypesAsync(EnvironmentContextData environmentContextData, int longRunningJobId, MassExcludeTypeModel massExcludeTypeModel);
        
        Task DeleteAllLinksForLinkType(EnvironmentContextData environmentContextData, string username, int longRunningJobId, DeleteAllLinksForLinkTypeModel deleteAllLinksForLinkTypeModel);

        Task ApplyExpressions(EnvironmentContextData environmentContextData, string username, int longRunningJobId, ApplyExpressionsModel applyExpressionsModel);

        Task WorkflowInspireAsync(EnvironmentContextData environmentContextData, string username, int longRunningJobId, WorkflowInspireModel model);
    }
}
