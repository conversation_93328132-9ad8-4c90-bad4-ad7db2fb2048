namespace inRiver.Server.Util.Workflow
{
    using System;
    using System.Collections.Generic;
    using System.Diagnostics;
    using System.Globalization;
    using System.Linq;
    using System.Net.Http;
    using System.Net.Http.Headers;
    using System.Text;
    using System.Text.RegularExpressions;
    using System.Threading.Tasks;
    using inRiver.Core.Models.inRiver.Workflow;
    using inRiver.Remoting.Objects;
    using inRiver.Remoting.Settings;
    using inRiver.Server.Repository;
    using inRiver.Server.Request;
    using JWT.Algorithms;
    using JWT.Builder;
    using Newtonsoft.Json;
    using ProtobufSupport;

    public class InspireHelper
    {
        private readonly CultureInfo modelLanguage;
        private readonly CultureInfo dataLanguage;
        private readonly RequestContext requestContext;
        private readonly ModelRepository modelRepository;
        private readonly DataRepository dataRepository;
        private readonly UtilityRepository utilRepository;
        private readonly GlossariesRepository glossariesRepository;

        private static HttpClient httpClient;
        private static readonly object InitLock = new object();

        public InspireHelper(RequestContext requestContext, string inspireUrl, string displayLanguage = null)
        {
            this.modelRepository = new ModelRepository(requestContext);
            this.dataRepository = new DataRepository(requestContext);
            this.utilRepository = new UtilityRepository(requestContext);

            // Fallback to master language as default language
            // TODO: Validate displayLanguage
            var masterLanguage = displayLanguage ?? this.GetMasterLanguage() ?? "en";
            this.modelLanguage = new CultureInfo(masterLanguage);
            this.dataLanguage = new CultureInfo(masterLanguage);
            this.requestContext = requestContext;

            this.glossariesRepository = new GlossariesRepository(requestContext);

            if (httpClient == null)
            {
                lock (InitLock)
                {
                    if (httpClient == null)
                    {
                        httpClient = new HttpClient() { BaseAddress = new Uri(inspireUrl) };
                    }
                }
            }
        }

        #region Model Conversion

        public InspireEntityModel EntityToInspireEntityModel(Entity entity) => this.EntityToInspireEntityModel(entity, true);

        private InspireEntityModel EntityToInspireEntityModel(Entity entity, bool includeLinks = true)
        {
            // Entity summary
            var inspireEntitySummary = new InspireEntitySummaryModel()
            {
                Id = entity.Id,
                EntityTypeId = entity.EntityType.Id,
                EntityTypeDisplayName = this.GetDisplayValueFromLocaleString(entity.EntityType.Name, entity.EntityType.Id),
                DisplayName = this.GetStringValue(entity.DisplayName),
                DisplayDescription = this.GetStringValue(entity.DisplayDescription),
                Version = entity.Version.ToString(),
                LockedBy = entity.Locked,
                CreatedBy = entity.CreatedBy,
                CreatedDate = entity.DateCreated.ToString("O"),
                FormattedCreatedDate = entity.DateCreated.ToString(this.dataLanguage),
                ModifiedBy = entity.ModifiedBy,
                ModifiedDate = entity.LastModified.ToString("O"),
                FormattedModifiedDate = entity.LastModified.ToString(this.dataLanguage),
                ResourceUrl = entity.MainPictureUrl,
                Completeness = entity.Completeness,
                FieldSetId = entity.FieldSetId,
                SegmentId = entity.Segment.Id,
                SegmentName = entity.Segment.Name
            };

            if (!string.IsNullOrEmpty(entity.FieldSetId))
            {
                var entityFieldSets = entity.EntityType.FieldSets;

                // Fallback: Directly fetch the FieldSets
                if (entityFieldSets == null)
                {
                    entityFieldSets = this.GetFieldSetsForEntityType(entity.EntityType.Id);
                }

                var fieldSets =
                    entityFieldSets.Where(fs => fs.Id == entity.FieldSetId)
                        .Select(fs => fs.Name[this.dataLanguage]);

                inspireEntitySummary.FieldSetName = fieldSets.FirstOrDefault();
            }

            // Field Summary
            var inspireFields = entity.Fields
                                .OrderBy(f => f.FieldType.Index)
                                .Select(f => this.FieldToInspireEntityFieldModel(f))
                                .ToList();

            // Field values
            var inspireFieldValues = entity.Fields
                                .OrderBy(f => f.FieldType.Index)
                                .Select(f => this.FieldToInspireFieldValueModel(f))
                                .ToList();


            var inspireEntity = new InspireEntityModel()
            {
                EntityId = entity.Id,
                Summary = inspireEntitySummary,
                Fields = inspireFields,
                FieldValues = inspireFieldValues,
            };

            // Convert specifications
            var specificationFields = this.GetSpecificationFieldsForEntity(entity.Id);

            if (specificationFields != null && specificationFields.Any())
            {
                inspireEntity.Specification = specificationFields
                    .Select(s => this.SpecificationFieldToInspireSpecificationModel(s))
                    .ToList();

            }

            // Convert inbound and outbound links
            if (includeLinks)
            {
                inspireEntity.Inbound = entity.InboundLinks
                    .Select(l => this.LinkToInspireLinkedEntity(l, LinkDirection.Inbound))
                    .ToList();

                inspireEntity.Outbound = entity.OutboundLinks
                    .Select(l => this.LinkToInspireLinkedEntity(l, LinkDirection.Outbound))
                    .ToList();
            }

            return inspireEntity;
        }

        public InspireLinkedEntityModel LinkToInspireLinkedEntity(Link link, LinkDirection direction)
        {
            var entity = LinkDirection.Inbound == direction ? link.Source : link.Target;
            var inspireEntity = this.EntityToInspireEntityModel(entity, false);
            var inspireLink = new InspireLinkedEntityModel()
            {
                EntityId = entity.Id,
                LinkIndex = link.Index,
                LinkTypeId = link.LinkType.Id,
                Summary = inspireEntity.Summary,
                Fields = inspireEntity.Fields,
                FieldValues = inspireEntity.FieldValues,
                Specification = inspireEntity.Specification,
            };

            return inspireLink;
        }

        public InspireEntityFieldsModel FieldToInspireEntityFieldModel(Field field)
        {
            var categoryName = this.modelRepository.GetCategory(field.FieldType.CategoryId).Name;
            var inspireField = new InspireEntityFieldsModel()
            {
                FieldTypeId = field.FieldType.Id,
                EntityId = field.EntityId,
                FieldTypeDisplayName = this.GetDisplayValueFromLocaleString(field.FieldType.Name, field.FieldType.Id),
                FieldTypeDescription = this.GetDisplayValueFromLocaleString(field.FieldType.Description),
                FieldDataType = field.FieldType.DataType,
                Value = this.GetFieldValue(field),
                DisplayValue = this.GetStringValue(field),
                IsMultiValue = field.FieldType.Multivalue,
                IsHidden = field.FieldType.Hidden,
                IsReadOnly = field.FieldType.ReadOnly,
                IsMandatory = field.FieldType.Mandatory,
                IsUnique = field.FieldType.Unique,
                IsExcludedFromDefaultView = field.FieldType.ExcludeFromDefaultView,
                CategoryId = field.FieldType.CategoryId,
                CategoryName = this.GetDisplayValueFromLocaleString(categoryName, field.FieldType.CategoryId),
                Index = field.FieldType.Index,
                Revision = field.Revision,
                Settings = field.FieldType.Settings,
            };

            if (field.FieldType.DataType == DataType.CVL)
            {
                inspireField.CvlId = field.FieldType.CVLId;
                inspireField.ParentCvlId = this.modelRepository.GetCVL(field.FieldType.CVLId)?.ParentId;
            }

            if (inspireField.IsExcludedFromDefaultView)
            {
                List<FieldSet> fsl;
                fsl = this.GetFieldSetsForEntityType(
                        field.FieldType.EntityTypeId);

                inspireField.IncludedInFieldSets = fsl.Where(fs => fs.FieldTypes.Contains(field.FieldType.Id)).Select(fs => fs.Id).ToList();
            }

            return inspireField;
        }

        public InspireEntityFieldValuesModel FieldToInspireFieldValueModel(Field field)
        {
            var model = new InspireEntityFieldValuesModel()
            {
                FieldTypeId = field.FieldType.Id,
                Value = this.GetFieldValue(field),
            };

            return model;
        }

        public InspireEntityDefinitionModel EntityTypeToInspireEntityDefinitionModel(EntityType entityType)
        {
            var model = new InspireEntityDefinitionModel()
            {
                Id = entityType.Id,
                Name = this.GetDisplayValueFromLocaleString(entityType.Name, entityType.Id),
                InboundLinkTypes = entityType.GetInboundLinkTypes().OrderBy(lt => lt.Index).Select(lt => lt.Id).ToList(),
                OutboundLinkTypes = entityType.GetOutboundLinkTypes().OrderBy(lt => lt.Index).Select(lt => lt.Id).ToList(),
                IsLinkEntityType = entityType.IsLinkEntityType,
                FieldSetIds = entityType.FieldSets.Select(y => y.Id).ToList(),
                DisplayNameFieldTypeId = entityType.GetDisplayNameFieldTypeId,
                DisplayDescriptionFieldTypeId = entityType.GetDisplayDescriptionFieldTypeId,
                // Expressions won't be used by Inspire so no need to fetch them
            };

            var inspireFieldDefinitions = entityType.FieldTypes
                                .OrderBy(ft => ft.Index)
                                .Select(ft => this.FieldTypeToInspireFieldDefinitionsModel(ft))
                                .ToList();

            model.FieldTypes = inspireFieldDefinitions;

            return model;
        }

        public InspireFieldDefinitionsModel FieldTypeToInspireFieldDefinitionsModel(FieldType fieldType)
        {
            var categoryName = this.modelRepository.GetCategory(fieldType.CategoryId).Name;
            var inspireField = new InspireFieldDefinitionsModel()
            {
                FieldTypeId = fieldType.Id,
                FieldTypeDisplayName = this.GetDisplayValueFromLocaleString(fieldType.Name, fieldType.Id),
                FieldTypeDescription = this.GetDisplayValueFromLocaleString(fieldType.Description),
                FieldDataType = fieldType.DataType,
                IsMultiValue = fieldType.Multivalue,
                IsHidden = fieldType.Hidden,
                IsReadOnly = fieldType.ReadOnly,
                IsMandatory = fieldType.Mandatory,
                IsUnique = fieldType.Unique,
                TrackChanges = fieldType.TrackChanges,
                DefaultValue = fieldType.DefaultValue,
                IsExcludedFromDefaultView = fieldType.ExcludeFromDefaultView,
                CategoryId = fieldType.CategoryId,
                Index = fieldType.Index,
                Settings = fieldType.Settings,
            };

            if (fieldType.DataType == DataType.CVL)
            {
                inspireField.CvlId = fieldType.CVLId;
                inspireField.ParentCvlId = this.modelRepository.GetCVL(fieldType.CVLId)?.ParentId;
            }

            if (inspireField.IsExcludedFromDefaultView)
            {
                List<FieldSet> fsl;
                fsl = this.GetFieldSetsForEntityType(
                        fieldType.EntityTypeId);

                inspireField.IncludedInFieldSets = fsl.Where(fs => fs.FieldTypes.Contains(fieldType.Id)).Select(fs => fs.Id).ToList();
            }

            return inspireField;
        }

        public InspireCvlIdValuesModel CvlIdValuesModelToInspireCvlIdValuesModel(CVLValue cvlValue)
        {
            var inspireCvlIdValues = new InspireCvlIdValuesModel()
            {
                Value = cvlValue.Key,
                ParentKey = cvlValue.ParentKey,
                Label = this.GetCVLValueForLanguage(cvlValue.CVLId, cvlValue.Key, this.dataLanguage),
            };

            return inspireCvlIdValues;
        }

        public InspireSpecificationModel SpecificationFieldToInspireSpecificationModel(SpecificationField specificationField)
        {
            var isFormatted = !string.IsNullOrEmpty(specificationField.SpecificationFieldType.Format);
            var inspireSpecification = new InspireSpecificationModel()
            {
                EntityId = specificationField.EntityId,
                AdditionalData = specificationField.SpecificationFieldType.AdditionalData,
                Name = this.GetDisplayValueFromLocaleString(specificationField.SpecificationFieldType.Name,
                        specificationField.SpecificationFieldType.Id),
                CategoryId = specificationField.SpecificationFieldType.CategoryId,
                CvlId = specificationField.SpecificationFieldType.CVLId,
                IsMultiValue = specificationField.SpecificationFieldType.Multivalue,
                Index = specificationField.SpecificationFieldType.Index,
                IsFormatted = isFormatted,
                SpecificationFieldTypeId = specificationField.SpecificationFieldType.Id,
                Unit = specificationField.SpecificationFieldType.Unit,
                SpecificationDataType = specificationField.SpecificationFieldType.DataType,
                Mandatory = specificationField.SpecificationFieldType.Mandatory,
                IsReadOnly = isFormatted,
                // NOTE: IsHidden is part of the model but its value is never set on the REST API fetchdata response
            };

            if (specificationField.SpecificationFieldType.DataType == DataType.CVL)
            {
                if (specificationField.SpecificationFieldType.Multivalue)
                {
                    inspireSpecification.IsMultiValue = true;
                    if (specificationField.Data != null)
                    {
                        var keys = specificationField.Data.ToString().Split(';');
                        inspireSpecification.Value = keys;
                        inspireSpecification.DisplayValue = this.GetStringValue(specificationField);
                    }
                }
                else
                {
                    if (specificationField.Data != null)
                    {
                        var key = specificationField.Data.ToString();
                        inspireSpecification.Value = key;
                        inspireSpecification.DisplayValue = this.GetStringValue(specificationField);
                    }
                }

                inspireSpecification.CvlId = specificationField.SpecificationFieldType.CVLId;
                inspireSpecification.ParentCvlId = this.GetCVLModel(inspireSpecification.CvlId)?.ParentId;
            }
            else if (specificationField.SpecificationFieldType.DataType == DataType.LocaleString)
            {
                var localeString = specificationField.Data as LocaleString;
                inspireSpecification.Value = this.CreateLocaleStringModel(localeString);
                inspireSpecification.DisplayValue = this.GetStringValue(specificationField);
            }
            else if (specificationField.SpecificationFieldType.DataType == DataType.String)
            {
                if (inspireSpecification.IsFormatted)
                {
                    var formattedValue = this.GetFormattedValue(
                                specificationField.SpecificationFieldType,
                                specificationField.EntityId);
                    inspireSpecification.DisplayValue = formattedValue;
                    inspireSpecification.Value = formattedValue;
                }
                else
                {
                    var value = this.GetStringValue(specificationField);
                    inspireSpecification.Value = value;
                    inspireSpecification.DisplayValue = value;
                }
            }
            else if (specificationField.SpecificationFieldType.DataType == DataType.DateTime)
            {
                if (specificationField.Data == null)
                {
                    inspireSpecification.Value = null;
                    inspireSpecification.DisplayValue = null;
                }
                else
                {
                    inspireSpecification.Value = ((DateTime)specificationField.Data).ToString("O");
                    inspireSpecification.DisplayValue = this.DateTimeToDisplayValue((DateTime)specificationField.Data);
                }
            }
            else if (specificationField.SpecificationFieldType.DataType == DataType.Integer)
            {
                inspireSpecification.Value = this.GetStringValue(specificationField);
                inspireSpecification.DisplayValue = this.GetStringValue(specificationField);
            }
            else if (specificationField.SpecificationFieldType.DataType == DataType.Double)
            {
                inspireSpecification.Value = this.GetStringValue(specificationField);
                inspireSpecification.DisplayValue = this.GetStringValue(specificationField);
            }
            else if (specificationField.SpecificationFieldType.DataType == DataType.Xml)
            {
                inspireSpecification.Value = this.GetStringValue(specificationField);
                inspireSpecification.DisplayValue = this.GetStringValue(specificationField).Replace("<", "&lt;").Replace(">", "&gt");
            }
            else
            {
                inspireSpecification.Value = this.GetStringValue(specificationField);
                inspireSpecification.DisplayValue = this.GetStringValue(specificationField);
            }

            if (!string.IsNullOrEmpty(inspireSpecification.Unit))
            {
                inspireSpecification.DisplayValue += " " + inspireSpecification.Unit;
            }

            return inspireSpecification;
        }

        #endregion

        #region Repository Methods

        private string GetDisplayValueFromLocaleString(LocaleString localeString, string fallbackString = null,
        CultureInfo cultureInfo = null)
        {
            cultureInfo = cultureInfo ?? this.dataLanguage;
            var displayValue = localeString?[cultureInfo];

            if (string.IsNullOrEmpty(displayValue))
            {
                displayValue = string.IsNullOrEmpty(fallbackString) ? string.Empty : $"[{fallbackString}]";
            }

            return displayValue;
        }

        private string GetStringValue(Field field, CultureInfo ci = null)
        {
            ci = ci ?? this.dataLanguage;
            if (field == null)
            {
                return string.Empty;
            }

            if (field.Data == null)
            {
                return string.Empty;
            }

            if (string.IsNullOrEmpty(field.Data.ToString()))
            {
                return string.Empty;
            }

            if (field.FieldType.DataType == DataType.CVL)
            {
                var text = string.Empty;
                if (!field.FieldType.Multivalue)
                {
                    text = this.GetCVLValueForLanguage(field.FieldType.CVLId, field.Data.ToString(), ci);
                }
                else
                {
                    var array = field.Data.ToString().Split(new char[1] { ';' });
                    for (var i = 0; i < array.Length; i++)
                    {
                        if (i > 0)
                        {
                            text += ";";
                        }

                        text += this.GetCVLValueForLanguage(field.FieldType.CVLId, array[i], ci);
                    }
                }

                return text;
            }

            if (field.FieldType.DataType != DataType.LocaleString)
            {
                return field.Data.ToString();
            }

            LocaleString localeString;
            try
            {
                localeString = (LocaleString)field.Data;
            }
            catch (Exception)
            {
                localeString = DeserializeLocaleString(field.ToString());
            }

            if (LocaleString.IsNullOrEmpty(localeString))
            {
                return string.Empty;
            }

            if (!localeString.ContainsCulture(ci))
            {
                return string.Empty;
            }

            var text2 = localeString[ci];
            if (string.IsNullOrEmpty(text2))
            {
                return string.Empty;
            }

            text2 = Regex.Replace(text2, "<.*?>", string.Empty);
            if (!string.IsNullOrEmpty(text2))
            {
                return text2;
            }

            return string.Empty;
        }

        public string GetStringValue(SpecificationField specificationField)
        {
            return this.GetStringValue(specificationField.Data, this.dataLanguage,
                specificationField.SpecificationFieldType.DataType, specificationField.SpecificationFieldType.Multivalue,
                specificationField.SpecificationFieldType.CVLId);
        }

        public string GetStringValue(object data, CultureInfo ci, string dataType, bool multivalue, string cvlId)
        {
            if (data == null || data.ToString() == string.Empty)
            {
                return string.Empty;
            }

            string value;
            if (dataType == DataType.CVL)
            {
                if (!multivalue)
                {
                    value = this.GetCVLValueForLanguage(cvlId, data.ToString(), ci);
                }
                else
                {
                    value = string.Join(";",
                        data.ToString()
                            .Split(';')
                            .Select(cvlKey => this.GetCVLValueForLanguage(cvlId, cvlKey, ci)));
                }
            }
            else if (dataType == DataType.DateTime)
            {
                value = this.DateTimeToDisplayValue((DateTime)data);
            }
            else if (dataType == DataType.LocaleString)
            {
                value = this.GetDisplayValueFromLocaleString(data as LocaleString, string.Empty, ci);
            }
            else if (dataType == DataType.Double)
            {
                value = ((decimal)double.Parse(data.ToString())).ToString(ci);
            }
            else
            {
                value = data.ToString();
            }

            return value;
        }

        private string GetCVLValueForLanguage(string cvlId, string cvlKey, CultureInfo ci)
        {
            var cvlValue = this.modelRepository.GetCVLValueByKey(cvlKey, cvlId);

            if (cvlValue == null)
            {
                return "[" + cvlKey + "]";
            }

            if (cvlValue.Value == null)
            {
                return "[" + cvlKey + "]";
            }

            if (cvlValue.Value.GetType() == typeof(LocaleString))
            {
                var value = (LocaleString)cvlValue.Value;

                var localeStringValue = value[ci];
                if (string.IsNullOrEmpty(localeStringValue))
                {
                    return "[" + cvlKey + "]";
                }

                return localeStringValue;
            }

            if (string.IsNullOrEmpty(cvlValue.Value.ToString()))
            {
                return "[" + cvlKey + "]";
            }

            return cvlValue.Value.ToString();
        }

        private static LocaleString DeserializeLocaleString(string fieldData)
        {
            var localeString = new LocaleString();
            var dictionary = JsonConvert.DeserializeObject<Dictionary<string, Dictionary<string, string>>>(fieldData);
            var dictionary2 = new Dictionary<string, string>();
            if (dictionary.ContainsKey("stringMap"))
            {
                dictionary2 = dictionary["stringMap"];
            }
            else if (dictionary.ContainsKey("StringMap"))
            {
                dictionary2 = dictionary["StringMap"];
            }

            foreach (var item in dictionary2)
            {
                localeString[new CultureInfo(item.Key)] = item.Value;
            }

            return localeString;
        }

        private object GetFieldValue(Field field)
        {
            var data = field.Data;
            var dataType = field.FieldType.DataType.ToString();
            var fieldTypeIsMultivalue = field.FieldType.Multivalue;

            object value = null;

            try
            {
                if (dataType == DataType.CVL)
                {
                    if (data != null)
                    {
                        if (fieldTypeIsMultivalue)
                        {
                            value = data.ToString().Split(';');
                        }
                        else
                        {
                            value = data.ToString();
                        }
                    }
                }
                else if (dataType == DataType.LocaleString)
                {
                    var ls = data as LocaleString;
                    value = this.CreateLocaleStringModel(ls);
                }
                else if (dataType == DataType.String)
                {
                    value = this.GetStringValue(field);
                }
                else if (dataType == DataType.DateTime)
                {
                    if (data == null)
                    {
                        value = null;
                    }
                    else
                    {
                        DateTime time;

                        if (DateTime.TryParse(data.ToString(), out time))
                        {
                            value = time.ToString("O");
                        }
                    }
                }
                else if (dataType == DataType.Integer)
                {
                    value = data != null ? int.Parse(data.ToString()) : (int?)null;
                }
                else if (dataType == DataType.Double)
                {
                    value = data != null ? double.Parse(data.ToString()) : (double?)null;
                }
                else if (dataType == DataType.Boolean)
                {
                    value = data != null ? bool.Parse(data.ToString()) : (bool?)null;
                }
                else if (dataType == DataType.Xml)
                {
                    value = this.GetStringValue(field);
                }
                else
                {
                    value = this.GetStringValue(field);
                }
            }
            catch (FormatException e)
            {
                // It seems we have a data integrity problem in the database
                // All stored values not complying to the field data type will be returned as null
                value = null;
            }

            return value;
        }

        public string GetFormattedValue(SpecificationFieldType specificationFieldType, int entityId) => this.dataRepository.GetFormattedValue(specificationFieldType, entityId);

        public string DateTimeToDisplayValue(DateTime dateTime)
        {
            var ci = this.modelLanguage;
            return dateTime.ToString("g", ci);
        }

        public Entity GetFullEntityWithLinks(int entityId)
        {
            var entityWithLinks = this.dataRepository.GetFullEntity(entityId, LoadLevel.DataAndLinks);

            // Fetch fields for all Inbound links
            foreach (var link in entityWithLinks.InboundLinks)
            {
                var linkedEntity = this.dataRepository.GetFullEntity(link.Source.Id, LoadLevel.DataOnly);
                link.Source.Fields = linkedEntity.Fields;
            }

            // Fetch fields for all Outbound links
            foreach (var link in entityWithLinks.OutboundLinks)
            {
                var linkedEntity = this.dataRepository.GetFullEntity(link.Target.Id, LoadLevel.DataOnly);
                link.Target.Fields = linkedEntity.Fields;
            }

            return entityWithLinks;
        }

        private Dictionary<string, string> CreateLocaleStringModel(LocaleString ls, List<CultureInfo> languages = null)
        {
            var allLangs = languages ?? this.utilRepository.GetAllLanguages();
            var d = new Dictionary<string, string>(allLangs.Count);
            foreach (var lang in allLangs)
            {
                var value = ls != null && ls.ContainsCulture(lang) ? ls[lang] : string.Empty;
                d.Add(lang.Name, value);
            }

            return d;
        }

        private List<FieldSet> GetFieldSetsForEntityType(string entityTypeId)
        {
            var fieldsets = this.modelRepository.GetAllFieldSets();

            return fieldsets.FindAll(
                fs => fs.EntityTypeId.Equals(entityTypeId, StringComparison.InvariantCultureIgnoreCase));
        }

        public List<InspireEntityDefinitionModel> GetAllEntityTypes()
        {
            var entityTypes = this.modelRepository.GetAllEntityTypes();
            var models = entityTypes.Select(e => this.EntityTypeToInspireEntityDefinitionModel(e))
                                    .ToList();
            return models;
        }

        public List<InspireLanguageModel> GetAllLanguages()
        {
            var languages = this.utilRepository.GetAllLanguages();
            var models = languages.Select(l => new InspireLanguageModel()
            {
                Name = l.Name,
                DisplayName = l.DisplayName
            })
            .ToList();
            return models;
        }

        public string GetMasterLanguage() => this.utilRepository.GetServerSetting(ServerConstants.MASTER_LANGUAGE);

        public CVL GetCVLModel(string cvlId) => this.modelRepository.GetCVL(cvlId);

        public List<SpecificationField> GetSpecificationFieldsForEntity(int entityId) => this.dataRepository.GetSpecificationFieldsForEntity(entityId);

        public InspireCvlParamsModel GetCvlParamsForField(Field field, Field parentField = null)
        {
            Debug.Assert(field.FieldType.DataType == DataType.CVL, "Field type is not CVL");
            var cvl = this.GetCVLModel(field.FieldType.CVLId);
            if (cvl == null)
            {
                return null;
            }

            var parentKeys = new List<string>();
            if (parentField != null)
            {
                Debug.Assert(parentField.FieldType.DataType == DataType.CVL, "Parent field type is not CVL");
                if (parentField.Data != null)
                {
                    if (parentField.FieldType.Multivalue)
                    {
                        parentKeys = parentField.Data.ToString().Split(';').ToList();
                    }
                    else
                    {
                        parentKeys.Add(parentField.Data.ToString());
                    }
                }
            }

            var cvlValues = this.modelRepository.GetCVLValuesForCVL(cvl.Id);
            // Filter based on value of parent CVL field
            if (parentKeys.Any())
            {
                cvlValues = cvlValues.Where(cv => parentKeys.Contains(cv.ParentKey)).ToList();
            }

            var inspireCvlValues = new List<InspireCvlIdValuesModel>();
            if (cvlValues != null)
            {
                inspireCvlValues = cvlValues.Select(cv => this.CvlIdValuesModelToInspireCvlIdValuesModel(cv))
                                            .ToList();
            }

            var cvlParams = new InspireCvlParamsModel()
            {
                FieldTypeId = field.FieldType.Id,
                CvlValues = inspireCvlValues
            };

            return cvlParams;
        }
        #endregion

        #region Utility

        public List<string> GetLinkedEntityTypes(InspireEntityModel inspireEntity)
        {
            return inspireEntity.Inbound.Select(e => e.Summary.EntityTypeId)
                        .Concat(inspireEntity.Outbound.Select(e => e.Summary.EntityTypeId))
                        .ToList();
        }

        public Dictionary<string, Dictionary<string, string>> BuildFieldToCvlValueToKeyMapping(List<InspireCvlParamsModel> inspireCvls)
        {
            var mapping = new Dictionary<string, Dictionary<string, string>>();
            foreach (var cvl in inspireCvls)
            {
                mapping[cvl.FieldTypeId] = cvl.CvlValues.ToDictionary(cv => cv.Label, cv => cv.Value);
            }

            return mapping;
        }

        public List<InspireCvlParamsModel> GetInspireCvlParamsFromEntity(Entity entity, List<Field> targetFields = null)
        {
            var fieldsToProcess = targetFields ?? entity.Fields;
            var cvlParams = new List<InspireCvlParamsModel>();
            foreach (var field in fieldsToProcess)
            {
                if (field.FieldType.DataType == DataType.CVL)
                {
                    var cvl = this.GetCVLModel(field.FieldType.CVLId);
                    var parentCvlId = cvl?.ParentId;
                    var parentField = String.IsNullOrEmpty(parentCvlId) ? null
                        : entity.Fields.FirstOrDefault(f => f.FieldType.CVLId == parentCvlId);

                    var inspireCvlParams = this.GetCvlParamsForField(field, parentField);
                    if (inspireCvlParams != null)
                    {
                        cvlParams.Add(inspireCvlParams);
                    }
                }
            }

            return cvlParams;
        }

        private Comment CreateCommentForInspire(string text, int entityId, string author = "Inspire")
        {
            var comment = new Comment()
            {
                Text = text,
                EntityId = entityId,
                Author = author,
            };
            return comment;
        }

        private string GetFieldDisplayNameForComment(Field field, CultureInfo? ci = null)
        {
            if (field == null || field.FieldType == null)
            {
                return "Unknown Field";
            }

            var displayName = field.FieldType.Name[this.dataLanguage];
            if (string.IsNullOrEmpty(displayName))
            {
                displayName = $"[{field.FieldType.Id}]";
            }

            if (ci != null)
            {
                displayName += $" ({ci.DisplayName})";
            }

            return displayName;
        }

        public Comment CreateCommentForInspireGenerate(
            List<(Field Field, CultureInfo Cultureinfo)> successful,
            List<(Field Field, CultureInfo Cultureinfo)> failed,
            List<(Field Field, CultureInfo Cultureinfo)> skipped,
            int entityId,
            string author = "Inspire")
        {
            var text = new StringBuilder();
            if (successful != null && successful.Any())
            {
                text.AppendLine("Successfully generated the following fields:");
                foreach (var f in successful)
                {
                    text.AppendLine($"- {this.GetFieldDisplayNameForComment(f.Field, f.Cultureinfo)}");
                }
            }

            if (failed != null && failed.Any())
            {
                text.AppendLine("Failed to generate the following fields:");
                foreach (var f in failed)
                {
                    text.AppendLine($"- {this.GetFieldDisplayNameForComment(f.Field, f.Cultureinfo)}");
                }
            }

            if (skipped != null && skipped.Any())
            {
                text.AppendLine("Skipped generating the following fields:");
                foreach (var f in skipped)
                {
                    text.AppendLine($"- {this.GetFieldDisplayNameForComment(f.Field, f.Cultureinfo)}");
                }
            }

            return this.CreateCommentForInspire(text.ToString().Trim(), entityId, author);
        }

        public Comment CreateCommentForInspireTranslate(
            List<(Field Field, CultureInfo Cultureinfo)> successful,
            List<(Field Field, CultureInfo Cultureinfo)> failed,
            List<(Field Field, CultureInfo Cultureinfo)> skipped,
            int entityId,
            string author = "Inspire")
        {
            var text = new StringBuilder();
            if (successful != null && successful.Any())
            {
                text.AppendLine("Successfully translated the following fields:");
                foreach (var f in successful)
                {
                    text.AppendLine($"- {this.GetFieldDisplayNameForComment(f.Field, f.Cultureinfo)}");
                }
            }

            if (failed != null && failed.Any())
            {
                text.AppendLine("Failed to translate the following fields:");
                foreach (var f in failed)
                {
                    text.AppendLine($"- {this.GetFieldDisplayNameForComment(f.Field, f.Cultureinfo)}");
                }
            }

            if (skipped != null && skipped.Any())
            {
                text.AppendLine("Skipped translating the following fields:");
                foreach (var f in skipped)
                {
                    text.AppendLine($"- {this.GetFieldDisplayNameForComment(f.Field, f.Cultureinfo)}");
                }
            }

            return this.CreateCommentForInspire(text.ToString().Trim(), entityId, author);
        }

        #endregion

        #region Inspire Backend

        public string GetInspireToken(string username = "system")
        {
            var builder = JwtBuilder.Create()
                .WithAlgorithm(new HMACSHA256Algorithm()) // symmetric
                .WithSecret("$svb7HhF@74B5u!dCi^npgGcP2hgwThKEhJEoG4W")
                .AddClaim("exp", DateTimeOffset.UtcNow.AddHours(8).ToUnixTimeSeconds())
                .AddClaim("username", username)
                .AddClaim("userIsInspireAdmin", true)
                .AddClaim("customerSafeName", this.requestContext.CustomerSafeName)
                .AddClaim("environmentSafeName", this.requestContext.EnvironmentSafeName);

            return builder.Encode();
        }

        public async Task<List<InspireGenerateResponseModel>> GenerateFieldsAsync(string token, string entityTypeId, InspireGenerateFieldOptionsModel options)
        {
            var url = $@"/v2/inspire/batch/generate/{entityTypeId}";

            var jsonData = JsonConvert.SerializeObject(options);
            var content = new StringContent(jsonData, Encoding.UTF8, "application/json")
            {
                Headers =
                {
                    ContentType = new MediaTypeHeaderValue("application/json")
                    {
                        CharSet = "utf-8"
                    }
                }
            };

            using var request = new HttpRequestMessage(HttpMethod.Post, url)
            {
                Content = content,
                Headers =
                {
                    Authorization = new AuthenticationHeaderValue("Bearer", token)
                }
            };

            var response = await httpClient.SendAsync(request);

            response.EnsureSuccessStatusCode();

            var responseBody = await response.Content.ReadAsStringAsync();
            var inspireResponse = JsonConvert.DeserializeObject<List<InspireGenerateResponseModel>>(responseBody);

            return inspireResponse;
        }

        public async Task<List<InspireTranslateResponseModel>> TranslateFieldsAsync(string token, string entityTypeId, InspireTranslateFieldOptionsModel options)
        {
            var url = $@"/v2/inspire/batch/translate/{entityTypeId}";

            var jsonData = JsonConvert.SerializeObject(options);
            var content = new StringContent(jsonData, Encoding.UTF8, "application/json")
            {
                Headers =
                {
                    ContentType = new MediaTypeHeaderValue("application/json")
                    {
                        CharSet = "utf-8"
                    }
                }
            };

            using var request = new HttpRequestMessage(HttpMethod.Post, url)
            {
                Content = content,
                Headers =
                {
                    Authorization = new AuthenticationHeaderValue("Bearer", token)
                }
            };

            var response = await httpClient.SendAsync(request);

            response.EnsureSuccessStatusCode();

            var responseBody = await response.Content.ReadAsStringAsync();
            var inspireResponse = JsonConvert.DeserializeObject<List<InspireTranslateResponseModel>>(responseBody);

            return inspireResponse;
        }

        #endregion

        #region Glossary search

        /// <summary>
        /// Search for relevant glossary terms based on description and language pair
        /// </summary>
        /// <param name="description">The text to search for glossary matches</param>
        /// <param name="sourceLocale">Source language locale</param>
        /// <param name="targetLocale">Target language locale</param>
        /// <param name="glossaryId">ID of the glossary to search in</param>
        /// <param name="maxResults">Maximum number of results to return (default: 15)</param>
        /// <returns>List of matching glossary terms</returns>
        public async Task<List<GlossaryMatchModel>> SearchGlossaryTermsAsync(
           string description,
           string sourceLocale,
           string targetLocale,
           int glossaryId,
           int maxResults = 15)
        {
            if (this.glossariesRepository == null)
            {
                throw new InvalidOperationException("GlossariesRepository is not available. Please provide GlossariesRepository instance when creating InspireHelper.");
            }

            return await this.glossariesRepository.GetRelevantTermsBasicAsync(description, sourceLocale, targetLocale, glossaryId, maxResults);
        }

        /// <summary>
        /// Get glossary configuration from Inspire configuration
        /// </summary>
        /// <returns>List of segment glossary mapping configurations</returns>
        public async Task<List<SegmentGlossaryMappingConfig>> GetGlossaryConfigAsync()
        {
            if (this.glossariesRepository == null)
            {
                throw new InvalidOperationException("GlossariesRepository is not available. Please provide GlossariesRepository instance when creating InspireHelper.");
            }

            return await this.glossariesRepository.GetGlossaryConfigFromInspireConfigurationAsync();
        }

        #endregion
    }
}
