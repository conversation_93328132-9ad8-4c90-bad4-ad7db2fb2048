namespace inRiver.Core.UnitTests
{
    using System;
    using System.Collections.Generic;
    using System.Threading.Tasks;
    using inRiver.Core.Util;
    using Xunit;

    public class JobProgressUpdaterTests
    {
        [Fact]
        public void Constructor_ZeroTotalCount_ThrowsArgumentException()
        {
            Assert.Throws<ArgumentException>(() => new JobProgressUpdater(0));
        }

        [Fact]
        public void Increment_WithoutCallback_DoesNotThrow()
        {
            var updater = new JobProgressUpdater(5);
            updater.Increment();
        }

        [Fact]
        public async Task Increment_WithCallback_InvokesCallbackOnProgressChange()
        {
            var progressList = new List<int>();
            var updater = new JobProgressUpdater(2, percent => { progressList.Add(percent); return Task.CompletedTask; });
            updater.Increment(); // 50%
            updater.Increment(); // 100%
            await updater.EnsureFinalUpdateAsync();
            Assert.Contains(50, progressList);
            Assert.Contains(100, progressList);
        }

        [Fact]
        public async Task Increment_MultipleTimes_OnlyUpdatesOnPercentChange()
        {
            var progressList = new List<int>();
            var updater = new JobProgressUpdater(4, percent => { progressList.Add(percent); return Task.CompletedTask; });
            updater.Increment(); // 25
            updater.Increment(); // 50
            updater.Increment(); // 75
            updater.Increment(); // 100
            await updater.EnsureFinalUpdateAsync();
            Assert.Equal(new List<int> { 25, 50, 75, 100 }, progressList);
        }

        [Fact]
        public async Task EnsureFinalUpdateAsync_WaitsForLastUpdate()
        {
            var tcs = new TaskCompletionSource<bool>();
            var updater = new JobProgressUpdater(1, percent => tcs.Task);
            updater.Increment();
            var ensureTask = updater.EnsureFinalUpdateAsync();
            Assert.False(ensureTask.IsCompleted);
            tcs.SetResult(true);
            await ensureTask;
        }

        [Fact]
        public async Task Increment_CallbackThrowsException_DoesNotThrowFromIncrement()
        {
            var updater = new JobProgressUpdater(1, percent => throw new InvalidOperationException("Test exception"));
            // Should not throw, exception is caught and logged internally
            updater.Increment();
            await updater.EnsureFinalUpdateAsync();
        }
    }
}
