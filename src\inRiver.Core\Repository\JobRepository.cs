namespace inRiver.Core.Repository
{
    using System;
    using System.Collections.Generic;
    using System.Threading.Tasks;
    using inRiver.Core.Models;
    using inRiver.Core.Models.inRiver;
    using inRiver.Core.Persistance;
    using Newtonsoft.Json;
    using Polly;

    public class JobRepository : BaseCoreRepository
    {
        public JobRepository(IinRiverPersistance persistance, ApiCaller apiCaller)
            : base(persistance, apiCaller)
        {
        }

        public void UpdateLongRunningJobStateAndMetadata(int id, string state, string metadata) =>
            Policy
                .Handle<Exception>()
                .WaitAndRetry(3, retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)))
                .Execute(() =>
                    this.JobPersistance.UpdateLongRunningJobStateAndMetadata(id, state, metadata));

        public void UpdateSyndicationJobStateAndMetadata(int id, string state, IDictionary<string, object> metadata) =>
            Policy
                .Handle<Exception>()
                .WaitAndRetry(3, retryAttempt => TimeSpan.FromSeconds(Math.Pow(3, retryAttempt)))
                .Execute(() =>
                    this.JobPersistance.UpdateLongRunningJobStateAndMetadata(id, state, JsonConvert.SerializeObject(metadata)));

        public Task UpdateLongRunningJobMetadataAsync(int id, string metadata) =>
            Policy
                .Handle<Exception>()
                .WaitAndRetryAsync(3, retryAttempt => TimeSpan.FromSeconds(Math.Pow(3, retryAttempt)))
                .ExecuteAsync(() => this.JobPersistance.UpdateLongRunningJobMetadataAsync(id, metadata));

        public void UpdateLongRunningJobState(int id, string state)
        {
            this.JobPersistance.UpdateLongRunningJobState(id, state);
        }

        public async Task UpdateLongRunningJobPercentCompletedAsync(int jobId, int percentCompleted)
        {
            await this.JobPersistance.UpdateLongRunningJobPercentCompletedAsync(jobId, percentCompleted);
        }

        public int InsertLongRunningJob(LongRunningJob job)
        {
            return this.JobPersistance.InsertLongRunningJob(job);
        }

        public bool StartedJobExists(string jobType, string identifier = null, string identifierType = null)
        {
            return this.JobPersistance.StartedJobExists(jobType, identifier, identifierType);
        }

        public bool JobIsCancelled(int jobId) => this.JobPersistance.JobIsCancelled(jobId);
    }
}
