namespace inRiver.Core.Models.inRiver.Workflow
{
    using System.Collections.Generic;
    using Newtonsoft.Json;

    public class InspireEntityModel
    {
        [JsonProperty("entityId")]
        public int EntityId { get; set; }

        [JsonProperty("summary")]
        public InspireEntitySummaryModel Summary { get; set; }

        [JsonProperty("fields")]
        public List<InspireEntityFieldsModel> Fields { get; set; }

        [JsonProperty("fieldValues")]
        public List<InspireEntityFieldValuesModel> FieldValues { get; set; }

        [JsonProperty("specification")]
        public List<InspireSpecificationModel> Specification { get; set; }

        [JsonProperty("inbound")]
        public List<InspireLinkedEntityModel> Inbound { get; set; }

        [JsonProperty("outbound")]
        public List<InspireLinkedEntityModel> Outbound { get; set; }
    }

    public class InspireEntitySummaryModel
    {
        [JsonProperty("id")]
        public int Id { get; set; }

        [JsonProperty("entityTypeId")]
        public string EntityTypeId { get; set; }

        [JsonProperty("entityTypeDisplayName")]
        public string EntityTypeDisplayName { get; set; }

        [JsonProperty("displayName")]
        public string DisplayName { get; set; }

        [JsonProperty("displayDescription")]
        public string DisplayDescription { get; set; }

        [JsonProperty("version")]
        public string Version { get; set; }

        [JsonProperty("lockedBy")]
        public string LockedBy { get; set; }

        [JsonProperty("createdBy")]
        public string CreatedBy { get; set; }

        [JsonProperty("createdDate")]
        public string CreatedDate { get; set; }

        [JsonProperty("formattedCreatedDate")]
        public string FormattedCreatedDate { get; set; }

        [JsonProperty("modifiedBy")]
        public string ModifiedBy { get; set; }

        [JsonProperty("modifiedDate")]
        public string ModifiedDate { get; set; }

        [JsonProperty("formattedModifiedDate")]
        public string FormattedModifiedDate { get; set; }

        [JsonProperty("resourceUrl")]
        public string ResourceUrl { get; set; }

        [JsonProperty("completeness")]
        public object Completeness { get; set; }

        [JsonProperty("fieldSetId")]
        public string FieldSetId { get; set; }

        [JsonProperty("fieldSetName")]
        public string FieldSetName { get; set; }

        [JsonProperty("segmentId")]
        public int? SegmentId { get; set; }

        [JsonProperty("segmentName")]
        public string SegmentName { get; set; }
    }

    public class InspireEntityFieldsModel
    {
        [JsonProperty("fieldTypeId")]
        public string FieldTypeId { get; set; }

        [JsonProperty("entityId")]
        public int EntityId { get; set; }

        [JsonProperty("fieldTypeDisplayName")]
        public string FieldTypeDisplayName { get; set; }

        [JsonProperty("fieldTypeDescription")]
        public string FieldTypeDescription { get; set; }

        [JsonProperty("fieldDataType")]
        public string FieldDataType { get; set; }

        [JsonProperty("value")]
        public object Value { get; set; }

        [JsonProperty("displayValue")]
        public object DisplayValue { get; set; }

        [JsonProperty("isMultiValue")]
        public bool IsMultiValue { get; set; }

        [JsonProperty("isHidden")]
        public bool IsHidden { get; set; }

        [JsonProperty("isReadOnly")]
        public bool IsReadOnly { get; set; }

        [JsonProperty("isMandatory")]
        public bool IsMandatory { get; set; }

        [JsonProperty("isUnique")]
        public bool IsUnique { get; set; }

        [JsonProperty("isExcludedFromDefaultView")]
        public bool IsExcludedFromDefaultView { get; set; }

        [JsonProperty("includedInFieldSets")]
        public List<string> IncludedInFieldSets { get; set; }

        [JsonProperty("categoryId")]
        public string CategoryId { get; set; }

        [JsonProperty("categoryName")]
        public string CategoryName { get; set; }

        [JsonProperty("index")]
        public int? Index { get; set; }

        [JsonProperty("revision")]
        public int? Revision { get; set; }

        [JsonProperty("cvlId")]
        public string CvlId { get; set; }

        [JsonProperty("parentCvlId")]
        public string ParentCvlId { get; set; }

        [JsonProperty("settings")]
        public Dictionary<string, string> Settings { get; set; }
    }

    public class InspireEntityFieldValuesModel
    {
        [JsonProperty("fieldTypeId")]
        public string FieldTypeId { get; set; }

        [JsonProperty("value")]
        public object Value { get; set; }
    }

    public class InspireLinkedEntityModel
    {
        [JsonProperty("entityId")]
        public int EntityId { get; set; }

        [JsonProperty("linkIndex")]
        public int LinkIndex { get; set; }

        [JsonProperty("linkTypeId")]
        public string LinkTypeId { get; set; }

        [JsonProperty("summary")]
        public InspireEntitySummaryModel Summary { get; set; }

        [JsonProperty("fields")]
        public List<InspireEntityFieldsModel> Fields { get; set; }

        [JsonProperty("fieldValues")]
        public List<InspireEntityFieldValuesModel> FieldValues { get; set; }

        [JsonProperty("specification")]
        public List<InspireSpecificationModel> Specification { get; set; }
    }

    public class InspireEntityDefinitionModel
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonProperty("fieldTypes")]
        public List<InspireFieldDefinitionsModel> FieldTypes { get; set; }

        [JsonProperty("inboundLinkTypes")]
        public List<string> InboundLinkTypes { get; set; }

        [JsonProperty("outboundLinkTypes")]
        public List<string> OutboundLinkTypes { get; set; }

        [JsonProperty("isLinkEntityType")]
        public bool IsLinkEntityType { get; set; }

        [JsonProperty("fieldSetIds")]
        public List<string> FieldSetIds { get; set; }

        [JsonProperty("displayNameFieldTypeId")]
        public string DisplayNameFieldTypeId { get; set; }

        [JsonProperty("displayDescriptionFieldTypeId")]
        public string DisplayDescriptionFieldTypeId { get; set; }
    }

    public class InspireFieldDefinitionsModel
    {
        [JsonProperty("fieldTypeId")]
        public string FieldTypeId { get; set; }

        [JsonProperty("fieldTypeDisplayName")]
        public string FieldTypeDisplayName { get; set; }

        [JsonProperty("fieldTypeDescription")]
        public string FieldTypeDescription { get; set; }

        [JsonProperty("fieldDataType")]
        public string FieldDataType { get; set; }

        [JsonProperty("isMultiValue")]
        public bool IsMultiValue { get; set; }

        [JsonProperty("isHidden")]
        public bool IsHidden { get; set; }

        [JsonProperty("isReadOnly")]
        public bool IsReadOnly { get; set; }

        [JsonProperty("isMandatory")]
        public bool IsMandatory { get; set; }

        [JsonProperty("isUnique")]
        public bool IsUnique { get; set; }

        [JsonProperty("trackChanges")]
        public bool TrackChanges { get; set; }

        [JsonProperty("defaultValue")]
        public string DefaultValue { get; set; }

        [JsonProperty("isExcludedFromDefaultView")]
        public bool IsExcludedFromDefaultView { get; set; }

        [JsonProperty("includedInFieldSets")]
        public List<string> IncludedInFieldSets { get; set; }

        [JsonProperty("categoryId")]
        public string CategoryId { get; set; }

        [JsonProperty("index")]
        public int? Index { get; set; }

        [JsonProperty("cvlId")]
        public string CvlId { get; set; }

        [JsonProperty("parentCvlId")]
        public string ParentCvlId { get; set; }

        [JsonProperty("settings")]
        public Dictionary<string, string> Settings { get; set; }

        [JsonProperty("expressionSupport")]
        public bool ExpressionSupport { get; set; }
    }

    public class InspireCvlIdValuesModel
    {
        [JsonProperty("label")]
        public string Label { get; set; }

        [JsonProperty("value")]
        public string Value { get; set; }

        [JsonProperty("parentKey")]
        public string ParentKey { get; set; }
    }

    public class InspireCvlModel
    {
        [JsonProperty("id")]
        public string Id { get; set; }

        [JsonProperty("dataType")]
        public string DataType { get; set; }

        [JsonProperty("parentId")]
        public string ParentId { get; set; }

        [JsonProperty("customValueList")]
        public bool CustomValueList { get; set; }

        [JsonProperty("values")]
        public List<InspireCvlIdValuesModel> Values { get; set; }
    }

    public enum LinkDirection
    {
        Inbound,
        Outbound
    }

    public class InspireSpecificationModel
    {
        [JsonProperty("entityId")]
        public int EntityId { get; set; }

        [JsonProperty("additionalData")]
        public string AdditionalData { get; set; }

        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonProperty("categoryId")]
        public string CategoryId { get; set; }

        [JsonProperty("value")]
        public object Value { get; set; }

        [JsonProperty("displayValue")]
        public string DisplayValue { get; set; }

        [JsonProperty("parentCvlId")]
        public string ParentCvlId { get; set; }

        [JsonProperty("cvlId")]
        public string CvlId { get; set; }

        [JsonProperty("isMultiValue")]
        public bool IsMultiValue { get; set; }

        [JsonProperty("index")]
        public int Index { get; set; }

        [JsonProperty("isFormatted")]
        public bool IsFormatted { get; set; }

        [JsonProperty("specificationFieldTypeId")]
        public string SpecificationFieldTypeId { get; set; }

        [JsonProperty("unit")]
        public string Unit { get; set; }

        [JsonProperty("specificationDataType")]
        public string SpecificationDataType { get; set; }

        [JsonProperty("mandatory")]
        public bool Mandatory { get; set; }

        [JsonProperty("isHidden")]
        public bool IsHidden { get; set; }

        [JsonProperty("isReadOnly")]
        public bool IsReadOnly { get; set; }
    }
}
