namespace inRiver.Server.Syndication.Export
{
    using System;
    using inRiver.Server.Syndication.Mapping;

    public class ScriptField
    {
        public ScriptField(bool isResourceExport)
        {
            this.IsResourceExport = isResourceExport;
        }

        public bool IsResourceExport { get; }

        public object[] Args { get; set; }

        public object[] Values { get; set; }

        public InRiverEntity Entity { get; set; }

        public string SkuId { get; set; }

        public string FieldTypeId { get; set; }

        public Guid MapFieldUniqueId { get; set; }

        public string DefaultValue { get; set; }

        public string Script { get; set; }

        public string FunctionName { get; set; }
    }
}
