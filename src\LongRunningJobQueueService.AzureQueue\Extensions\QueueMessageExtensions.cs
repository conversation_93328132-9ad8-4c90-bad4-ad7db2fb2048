namespace LongRunningJobQueueService.AzureQueue.Extensions
{
    using System.Text.Json;
    using Azure.Storage.Queues.Models;
    using Models;

    public static class QueueMessageExtensions
    {
        private static readonly JsonSerializerOptions JsonOptions = new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        };

        public static JobStatusQueueMessage GetMessageObject(this QueueMessage message)
        {
            var deserializedMessage = JsonSerializer.Deserialize<JobStatusQueueMessage>(message.MessageText, JsonOptions);
            if (deserializedMessage != null)
            {
                return deserializedMessage;
            }

            throw new JsonException($"Failed to deserialize message: {message.MessageText}");
        }
    }
}
