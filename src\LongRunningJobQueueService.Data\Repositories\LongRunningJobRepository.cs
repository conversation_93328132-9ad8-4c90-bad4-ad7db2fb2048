namespace LongRunningJobQueueService.Data.Repositories
{
    using System;
    using System.Data.SqlClient;
    using System.Threading.Tasks;
    using Dapper;
    using Serilog;
    using Services;

    public class LongRunningJobRepository : ILongRunningJobRepository
    {
        private readonly IEnvironmentService environmentService;

        public LongRunningJobRepository(IEnvironmentService environmentService)
        {
            this.environmentService = environmentService;
        }

        public async Task UpdateJobStatusAsync(string connectionString, int jobId, string status)
        {
            try
            {
                await using var connection = new SqlConnection(connectionString);

                _ = await connection.ExecuteAsync("UPDATE LongRunningJob SET State = @State WHERE Id = @Id", new { State = status, Id = jobId });
            }
            catch (Exception ex)
            {
                Log.Error(ex, "UpdateJobStatusAsync caught an unexpected exception");
                throw;
            }
        }
    }
}
