<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Dapper" Version="2.1.35" />
    <PackageReference Include="Inriver.StackEssentials" Version="6.0.0" />
    <PackageReference Include="Inriver.StackEssentials.Abstractions" Version="6.0.0" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" Version="6.0.0" />
    <PackageReference Include="Serilog" Version="2.8.0" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.2" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\LongRunningJobQueueService.AzureQueue\LongRunningJobQueueService.AzureQueue.csproj" />
  </ItemGroup>

</Project>
