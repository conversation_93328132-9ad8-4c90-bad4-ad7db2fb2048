namespace inRiver.Server.Syndication.Helpers
{
    using System;
    using System.Collections.Concurrent;
    using System.Collections.Generic;
    using System.Diagnostics;
    using System.Linq;
    using System.Text;
    using inRiver.Remoting.Log;
    using inRiver.Server.Request;

    public class DiagnosticsLogger
    {
        private const string SyndicateEnableDiagnostics = "SYNDICATE_ENABLE_DIAGNOSTICS";

        private readonly ConcurrentDictionary<string, List<long>> executionTimeData = new ConcurrentDictionary<string, List<long>>();

        private readonly ConcurrentDictionary<string, List<long>> devOnlyExecutionTimeData = new ConcurrentDictionary<string, List<long>>();

        private readonly RequestContext requestContext;

        private readonly bool isDiagnosticsEnabled;

        private Stopwatch stopwatch;

        private long readEntityIdsAmount;

        private long readFullEntitiesAmount;

        private long readEntityIdsTime;

        private long readFullEntitiesTime;

        public DiagnosticsLogger(RequestContext requestContext)
        {
            var setting = requestContext?.DataPersistance?.GetServerSetting(SyndicateEnableDiagnostics);
            if (!string.IsNullOrEmpty(setting))
            {
                this.isDiagnosticsEnabled = true;
                this.requestContext = requestContext;
            }
            else
            {
                this.isDiagnosticsEnabled = false;
            }
        }

        public void StartMeasure()
        {
            if (!this.isDiagnosticsEnabled)
            {
                return;
            }

            this.stopwatch = Stopwatch.StartNew();
        }

        public void FinalizeMeasure(string functionName, bool devOnly = false)
        {
            if (!this.isDiagnosticsEnabled)
            {
                return;
            }

            this.stopwatch.Stop();
            this.SaveExecutionTime(functionName, this.stopwatch.ElapsedMilliseconds, devOnly);
        }

        public void IncreaseReadEntityIdsAmount(long amount)
        {
            if (!this.isDiagnosticsEnabled)
            {
                return;
            }

            this.readEntityIdsAmount += amount;
        }

        public void IncreaseReadFullEntitiesAmount(long amount)
        {
            if (!this.isDiagnosticsEnabled)
            {
                return;
            }

            this.readFullEntitiesAmount += amount;
        }

        public void IncreaseReadEntityIdsTime(long time)
        {
            if (!this.isDiagnosticsEnabled)
            {
                return;
            }

            this.readEntityIdsTime += time;
        }

        public void IncreaseReadFullEntitiesTime(long time)
        {
            if (!this.isDiagnosticsEnabled)
            {
                return;
            }

            this.readFullEntitiesTime += time;
        }

        public void LogDiagnostics()
        {
            if (!this.isDiagnosticsEnabled)
            {
                return;
            }

            var userLogMessage = this.GetDiagnosticLogMessage();
            var devOnlyLogMessage = this.GetDiagnosticLogMessage(true);
            this.requestContext?.Log(LogLevel.Information, userLogMessage);
            Serilog.Log.Information(devOnlyLogMessage);
        }

        public void SaveExecutionTime(string functionName, long executionTime, bool devOnly = false)
        {
            var data = devOnly ? this.devOnlyExecutionTimeData : this.executionTimeData;
            if (!data.TryAdd(functionName, new List<long>() { executionTime }))
            {
                data[functionName].Add(executionTime);
            }
        }

        private string GetDiagnosticLogMessage(bool includeAll = false)
        {
            var message = new StringBuilder("Syndicate Diagnostics, function execution times:\n");
            var data = includeAll ? this.devOnlyExecutionTimeData.ToList().Concat(this.executionTimeData) : this.executionTimeData;

            foreach (var functionData in data)
            {
                var nbr = functionData.Value.Count;
                var min = functionData.Value.Min();
                var max = functionData.Value.Max();
                var mean = functionData.Value.Average();
                var stddev = CalculateStandardDeviation(functionData.Value, mean);
                _ = message.AppendLine($"{functionData.Key} (nbr: {nbr}, min: {min:F2}ms, max: {max:F2}ms, mean: {mean:F2}ms, stddev: {stddev:F2}ms)");
            }

            _ = message.AppendLine($"Syndicate Diagnostics, reading {this.readEntityIdsAmount} entity ids took {this.readEntityIdsTime:F2} ms");
            _ = message.AppendLine($"Syndicate Diagnostics, reading {this.readFullEntitiesAmount} full entities took {this.readFullEntitiesTime:F2} ms");

            return message.ToString();
        }

        private static double CalculateStandardDeviation(List<long> values, double mean)
        {
            var sumOfSquares = values.Select(n => Math.Pow(n - mean, 2)).Sum();
            return Math.Sqrt(sumOfSquares / values.Count);
        }
    }
}
