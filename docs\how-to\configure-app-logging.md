# Logging Configuration for LongRunningJobQueueService

## Summary

Successfully configured logging with TelemetryClient for the `LongRunningJobQueueService` project. The implementation follows the same patterns used in other services within the solution.

## Changes Made

### 1. Added NuGet Packages

Updated `LongRunningJobQueueService.csproj` to include:

-   `Microsoft.ApplicationInsights.AspNetCore` (v2.19.0)
-   `Serilog` (v2.8.0)
-   `Serilog.Sinks.ApplicationInsights` (v3.1.0)

### 2. Added Project References

Added references to:

-   `Telemetry` project
-   `Telemetry.Metrics` project

### 3. Created Constants

Created `Constants/LogConstants.cs` with:

```csharp
public const string CloudRoleName = "LongRunningJobQueueService";
```

### 4. Enhanced Util.cs

Added utility methods:

-   `GetInstrumentationKey()` - Retrieves Application Insights instrumentation key
-   `GetLogLevel()` - Retrieves log level from configuration

### 5. Updated Startup.cs

-   Added required using statements for Application Insights and Serilog
-   Configured Application Insights with `TelemetryClient` in `ConfigureServices()`
-   Added `CloudRoleNameTelemetryInitializer` for proper service identification
-   Configured Serilog with Application Insights sink in `Configure()` method
-   Added logging level switch for dynamic log level control

### 6. Enhanced Background Service

Updated `JobStatusQueueBackgroundService.cs` to include:

-   Service startup logging
-   Warning logs for null queue clients
-   Debug logs for non-existent queues
-   Error logging with structured logging for exceptions
-   Error logging for queue message processing failures

## Configuration Requirements

Ensure the following configuration parameters are set in Service Fabric configuration:

```xml
<Section Name="Configuration">
  <Parameter Name="InstrumentationKey" Value="your-app-insights-key" />
  <Parameter Name="LogLevel" Value="Information" />
  <Parameter Name="KeyVaultBaseUrl" Value="your-keyvault-url" />
  <Parameter Name="StackConfigSecretName" Value="your-stack-config-secret" />
</Section>
```

## Usage Examples

### Structured Logging

```csharp
Log.Information("Processing job {JobId} for environment {EnvironmentId}", jobId, environmentId);
Log.Warning("Queue {QueueName} does not exist, skipping", queueName);
Log.Error(ex, "Error processing queue message {MessageId}", messageId);
```

### Telemetry Context

The service is automatically configured with:

-   Cloud role name: "LongRunningJobQueueService"
-   Application Insights telemetry collection
-   Serilog integration with Application Insights
-   Structured logging support

## Features Enabled

1. **Application Insights Integration**: Full telemetry collection including dependencies, performance counters, and custom metrics
2. **Structured Logging**: Serilog with Application Insights sink for rich log data
3. **Cloud Role Identification**: Proper service identification in Application Insights
4. **Dynamic Log Levels**: Configurable log levels without redeployment
5. **Error Tracking**: Exception logging with full context and stack traces
6. **Performance Monitoring**: Built-in performance counter collection

## Next Steps

1. Configure the Service Fabric application parameters with the required values
2. Deploy and verify logs appear in Application Insights
3. Set up Application Insights dashboards and alerts as needed
4. Consider adding custom metrics using the `Telemetry.Metrics` project utilities

## Testing

The configuration has been tested and builds successfully. The service will automatically start logging when deployed with proper configuration values.
