namespace inRiver.Core.Models.inRiver.Workflow
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations;
    using System.Linq;
    using Newtonsoft.Json;

    public class InspireFieldOptionsModel
    {
        [JsonProperty("field_type_ids")]
        public List<string> FieldTypeIds { get; set; }

        [JsonProperty("entity_data")]
        public List<InspireEntityModel> EntityData { get; set; }

        [JsonProperty("pim_entity_type_definitions")]
        public List<InspireEntityDefinitionModel> PimEntityTypeDefinitions { get; set; }

        [JsonProperty("pim_language")]
        public List<InspireLanguageModel> PimLanguage { get; set; }

        // Helper method to parse tone from string
        public static ToneOptions? ParseTone(string toneString)
        {
            if (string.IsNullOrEmpty(toneString))
                return null;

            // Try to parse as enum name first
            if (Enum.TryParse<ToneOptions>(toneString.ToUpper(), out var result))
                return result;

            // Try to match with display name
            foreach (ToneOptions tone in Enum.GetValues(typeof(ToneOptions)))
            {
                var displayAttribute = typeof(ToneOptions)
                    .GetField(tone.ToString())
                    .GetCustomAttributes(typeof(DisplayAttribute), false)
                    .FirstOrDefault() as DisplayAttribute;

                if (displayAttribute != null &&
                    string.Equals(displayAttribute.Name, toneString, StringComparison.OrdinalIgnoreCase))
                {
                    return tone;
                }
            }

            throw new ArgumentException($"Invalid tone option: {toneString}. Valid options are: {string.Join(", ", Enum.GetNames(typeof(ToneOptions)))}");
        }
    }

    public class InputField_v3
    {
        [JsonProperty("field")]
        public string Field { get; set; }

        [JsonProperty("value")]
        public object Value { get; set; }
    }

    public enum ToneOptions
    {
        [Display(Name = "audience-centric")]
        AUDIENCE_CENTRIC,

        [Display(Name = "authentic")]
        AUTHENTIC,

        [Display(Name = "catchy")]
        CATCHY,

        [Display(Name = "clear and concise")]
        CLEAR_AND_CONCISE,

        [Display(Name = "confident")]
        CONFIDENT,

        [Display(Name = "engaging")]
        ENGAGING,

        [Display(Name = "enthusiastic")]
        ENTHUSIASTIC,

        [Display(Name = "factual")]
        FACTUAL,

        [Display(Name = "formal")]
        FORMAL,

        [Display(Name = "friendly")]
        FRIENDLY,

        [Display(Name = "fun and humorous")]
        FUN_AND_HUMOROUS,

        [Display(Name = "informative")]
        INFORMATIVE,

        [Display(Name = "inspiring")]
        INSPIRING,

        [Display(Name = "optimistic")]
        OPTIMISTIC,

        [Display(Name = "passionate")]
        PASSIONATE,

        [Display(Name = "persuasive")]
        PERSUASIVE,

        [Display(Name = "professional")]
        PROFESSIONAL,

        [Display(Name = "salesy")]
        SALESY,

        [Display(Name = "serious")]
        SERIOUS,

        [Display(Name = "warm")]
        WARM
    }

    public class InspireGenerateFieldOptionsModel : InspireFieldOptionsModel
    {
        [JsonProperty("language")]
        public string Language { get; set; } = "en";

        [JsonProperty("cvl_values")]
        public List<InspireCvlParamsModel> CvlValues { get; set; }
    }

    public class InspireTranslateFieldOptionsModel : InspireFieldOptionsModel
    {
        [JsonProperty("source_language")]
        public string SourceLanguage { get; set; } = "en";

        [JsonProperty("to_languages")]
        public List<string> ToLanguages { get; set; }

        [JsonProperty("include_context")]
        public bool IncludeContext { get; set; } = false;

        [JsonProperty("context")]
        public List<StatelessContext>? Context { get; set; }
    }

    public class InspireCvlParamsModel
    {
        [JsonProperty("cvl_values")]
        public List<InspireCvlIdValuesModel> CvlValues { get; set; }

        [JsonProperty("field_type_id")]
        public string FieldTypeId { get; set; }
    }

    public class StatelessContext
    {
        [JsonProperty("field_type_id")]
        public string FieldTypeId { get; set; }

        [JsonProperty("language")]
        public string? Language { get; set; }

        [JsonProperty("glossary")]
        public List<Dictionary<string, object>>? Glossary { get; set; }

        [JsonProperty("input_fields")]
        public List<InputField>? InputFields { get; set; }

        [JsonProperty("specification_fields")]
        public List<InputField>? SpecificationFields { get; set; }

        [JsonProperty("linked_entity_data")]
        public List<LinkedEntityData>? LinkedEntityData { get; set; }

        [JsonProperty("media_data")]
        public List<string>? MediaData { get; set; }
    }

    public class InputField
    {
        [JsonProperty("field")]
        public string Field { get; set; }

        [JsonProperty("value")]
        public object Value { get; set; }
    }

    public class LinkedEntityData
    {
        [JsonProperty("entity")]
        public string Entity { get; set; }

        [JsonProperty("fields")]
        public List<InputField> Fields { get; set; }
    }
}
