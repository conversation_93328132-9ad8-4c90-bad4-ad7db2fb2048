namespace inRiver.Core.Persistance
{
    using System.Linq;
    using inRiver.Core.Models;
    using inRiver.Core.Models.inRiver;
    using inRiver.Core.Persistance.Interfaces;
    using inRiver.Core.Util;
    using inRiver.Log;

    using inRiver.iPMC.Persistance;
    using Field = inRiver.Core.Models.inRiver.Field;
    using System;
    using System.Data.SqlClient;
    using System.Collections.Generic;
    using System.Threading.Tasks;

    public class IPMCPersistanceAdaptor : IinRiverPersistance,
        IJobPersistance
    {
        protected IPersistanceFieldSet _persistanceFieldSet;
        protected IPersistanceEntityType _persistentEntityType;
        protected IPersistanceFieldType _persistentFieldType;
        protected IPersistanceEntity _persistentEntity;
        protected IPersistanceContentSegmentation _persistanceContentSegmentation;

        public int EnvironmentId => _origInRiverPersistance.EnvironmentId;

        // to handle other operations
        protected readonly inRiverPersistance _origInRiverPersistance;

        public IPMCPersistanceAdaptor(inRiverPersistance origInRiverPersistance, string connectionString, ICommonLogging logInstance)
        {
            this._origInRiverPersistance = origInRiverPersistance;

            IContentSegmentPermissionProvider contentSegmentProvider = null;  // TODO:  this needs to be populated based on user's permissions

            _persistentFieldType = new PersistanceFieldType(connectionString, logInstance, contentSegmentProvider);

            _persistanceFieldSet = new PersistanceFieldSet(connectionString, logInstance, contentSegmentProvider);

            _persistentEntityType = new PersistanceEntityType(
                connectionString,
                logInstance,
                _persistentFieldType,
                _persistanceFieldSet,
                contentSegmentProvider);

            _persistanceContentSegmentation = new PersistanceContentSegmentation(connectionString, logInstance, contentSegmentProvider);

            _persistentEntity = new PersistanceEntity(
                connectionString,
                logInstance,
                _persistentEntityType,
                _persistentFieldType,
                _persistanceContentSegmentation,
                contentSegmentProvider
                );
        }

        public ApiCaller GetApiCaller()
        {
            return _origInRiverPersistance.GetApiCaller();
        }

        public virtual Field GetField(int entityId, string fieldTypeId)
        {
            var persistanceEntity = _persistentEntity.GetEntity(entityId);
            var output = _persistentEntity.GetFieldsForEntity(persistanceEntity, fieldTypeId).FirstOrDefault();

            return IPMCPersistanceDataConverter.ConvertTo<Field>(output);
        }

        public CommonLogHelper LogHelper => _origInRiverPersistance.LogHelper;

        public void UpdateLongRunningJobState(int id, string state)
        {
            _origInRiverPersistance.UpdateLongRunningJobState(id, state);
        }

        public void UpdateLongRunningJobStateAndMetadata(int id, string state, string metadata)
        {
            _origInRiverPersistance.UpdateLongRunningJobStateAndMetadata(id, state, metadata);
        }

        public Task UpdateLongRunningJobMetadataAsync(int id, string metadata)
            => this._origInRiverPersistance.UpdateLongRunningJobMetadataAsync(id, metadata);

        public async Task UpdateLongRunningJobPercentCompletedAsync(int jobId, int percentCompleted)
        {
            await _origInRiverPersistance.UpdateLongRunningJobPercentCompletedAsync(jobId, percentCompleted);
        }

        public int InsertLongRunningJob(LongRunningJob job)
        {
            return _origInRiverPersistance.InsertLongRunningJob(job);
        }

        public bool StartedJobExists(string jobType, string identifier = null, string identifierType = null)
        {
            return _origInRiverPersistance.StartedJobExists(jobType, identifier, identifierType);
        }

        public bool JobIsCancelled(int jobId) => _origInRiverPersistance.JobIsCancelled(jobId);

        public virtual Core.Models.inRiver.FieldType AddFieldType(Core.Models.inRiver.FieldType fieldType, bool generateIndex)
            => throw new NotImplementedException();

        public virtual bool DeleteAllFieldTypes()
            => throw new NotImplementedException();

        public virtual bool DeleteFieldType(string id)
            => throw new NotImplementedException();

        public void AddEnvironmentChange(
                        SqlConnection connection,
                        SqlCommand commandToSave,
                        Type typeOfObject,
                        bool keepRecordApiCaller,
                        SqlTransaction sqlTransaction)
            => throw new NotImplementedException();

        public virtual Models.inRiver.EntityType AddEntityType(Models.inRiver.EntityType entityType)
            => throw new NotImplementedException();

        public virtual Models.inRiver.EntityType UpdateEntityType(Models.inRiver.EntityType entityType)
            => throw new NotImplementedException();

        public virtual bool DeleteEntityType(string id)
            => throw new NotImplementedException();

        public virtual bool DeleteAllEntityTypes()
            => throw new NotImplementedException();

        public virtual Models.inRiver.FieldType UpdateFieldType(Models.inRiver.FieldType fieldType)
            => throw new NotImplementedException();

        public virtual Models.inRiver.FieldSet UpdateFieldSet(Models.inRiver.FieldSet fieldSet)
            => throw new NotImplementedException();

        public virtual Models.inRiver.FieldSet AddFieldSet(Models.inRiver.FieldSet fieldSet)
            => throw new NotImplementedException();

        public virtual bool DeleteFieldSet(string id)
            => throw new NotImplementedException();

        public virtual bool DeleteAllFieldSets()
            => throw new NotImplementedException();

        public virtual bool DeleteFieldTypeFromFieldSet(string fieldSetId, string fieldTypeId)
            => throw new NotImplementedException();

        public virtual bool AddFieldTypeToFieldSet(string fieldSetId, string fieldTypeId)
            => throw new NotImplementedException();

        public virtual Models.inRiver.LinkType AddLinkType(Models.inRiver.LinkType linkType)
            => throw new NotImplementedException();

        public virtual bool DeleteAllLinkTypes()
            => throw new NotImplementedException();

        public virtual bool DeleteLinkType(string id)
            => throw new NotImplementedException();

        public virtual Models.inRiver.LinkType UpdateLinkType(Models.inRiver.LinkType linkType)
            => throw new NotImplementedException();

        public virtual bool AddLanguage(string name)
            => throw new NotImplementedException();

        public virtual bool DeleteAllLanguages()
            => throw new NotImplementedException();

        public virtual bool DeleteLanguage(string name)
            => throw new NotImplementedException();

        public virtual List<Field> GetFieldsForEntity(Models.inRiver.Entity entity)
            => throw new NotImplementedException();

        public virtual bool ChannelIsPublished(int channelId)
            => throw new NotImplementedException();

        public virtual bool DeleteCVLValue(int cvlValueId)
            => throw new NotImplementedException();

        public virtual bool DeleteAllCVLValuesForCVL(string cvlId)
            => throw new NotImplementedException();

        public virtual void UpdateUniqueHashOnFields(Models.inRiver.FieldType fieldType)
            => throw new NotImplementedException();
    }
}
