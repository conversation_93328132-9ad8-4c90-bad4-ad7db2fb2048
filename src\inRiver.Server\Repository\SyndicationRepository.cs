namespace inRiver.Server.Repository
{
    using System;
    using System.Collections.Generic;
    using System.Diagnostics;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;
    using inRiver.Remoting.Security;
    using inRiver.Server.DataAccess;
    using inRiver.Server.Error;
    using inRiver.Server.Managers;
    using inRiver.Server.Managers.Interfaces;
    using inRiver.Server.Request;
    using inRiver.Server.Syndication;
    using inRiver.Server.Syndication.Enums;
    using inRiver.Server.Syndication.Exceptions;
    using inRiver.Server.Syndication.Export;
    using inRiver.Server.Syndication.Helpers;
    using inRiver.Server.Syndication.Mapping;
    using inRiver.Server.Syndication.Models;
    using inRiver.Server.Syndication.Service;
    using inRiver.Server.Syndication.Service.Interfaces;
    using Newtonsoft.Json;
    using Newtonsoft.Json.Linq;

    public class SyndicationRepository
    {
        private readonly RequestContext context;
        private readonly IDataPersistance dataContext;
        private readonly SyndicationEntityService entityService;
        private readonly IResourceExportService resourceExportService;
        private readonly IJobMetadataManager jobMetadataManager;
        private readonly CloudBlobManager cloudBlobManager;
        private readonly ISyndicationJobResultService syndicationJobResultService;
        private readonly IEntityResolverService entityResolverService;
        private readonly IExportContainersValidator exportContainersValidator;
        private readonly DiagnosticsLogger diagnosticsLogger;

        public SyndicationRepository(RequestContext context, IResourceExportService resourceExportService, IJobMetadataManager jobMetadataManager, ISyndicationJobResultService syndicationJobResultService, ISyndicationChannelService channelService, DiagnosticsLogger diagnosticsLogger = null)
        {
            if (!context.UserHasPermission(UserPermission.View))
            {
                throw ErrorUtility.GetSecurityException("User does not have permission to view data in inRiver");
            }

            this.dataContext = context.DataPersistance;
            this.context = context;
            this.entityService = new SyndicationEntityService(context, resourceExportService);
            this.resourceExportService = resourceExportService;
            this.jobMetadataManager = jobMetadataManager;
            this.cloudBlobManager = new CloudBlobManager(context, "syndicateContainers");
            this.syndicationJobResultService = syndicationJobResultService;
            this.entityResolverService = new EntityResolverService(this.entityService, new ChannelEntityResolverService(channelService, this.entityService));
            this.exportContainersValidator = new ExportContainersValidator();
            this.diagnosticsLogger = diagnosticsLogger;
        }

        /// <summary>
        /// Gets syndication export result, that will be exported to the syndicate extension.
        /// </summary>
        /// <param name="syndicationModel">Syndication model.</param>
        /// <param name="enableCompression">EnableCompression setting value.</param>
        /// <param name="enableHeader">EnableHeader setting value.</param>
        /// <param name="batchSize">BatchSize setting value.</param>
        /// <param name="environmentGid">Environment Gid.</param>
        /// <param name="cancellationToken">Cancellation token.</param>
        /// <exception cref="SyndicateException">
        /// Throws a <see cref="SyndicateException"/> if no entities were exported correctly or if data wasn't converted to json.
        /// </exception>
        /// <returns>Syndication export result.</returns>
        public async Task<ExportResult> GetExportResultAsync(SyndicationModel syndicationModel, bool enableCompression, bool enableHeader, int batchSize, string environmentGid, CancellationToken cancellationToken)
        {
            var exportContainersCacheFolderName = Guid.NewGuid().ToString();
            try
            {
                await this.GetExportContainersAsync(syndicationModel, batchSize, exportContainersCacheFolderName, environmentGid, cancellationToken);
                this.diagnosticsLogger?.StartMeasure();
                var exportContainers = await this.LoadExportContainersAsync(exportContainersCacheFolderName);
                this.diagnosticsLogger?.FinalizeMeasure(nameof(LoadExportContainersAsync), true);

                var numberOfEntities = exportContainers.Count;
                if (numberOfEntities == 0)
                {
                    throw new SyndicateException("No entities were exported correctly.");
                }

                this.syndicationJobResultService.SetNumberOfEntities(numberOfEntities);
                var jsonString = this.GetJsonString(syndicationModel, exportContainers, enableCompression, enableHeader);
                this.diagnosticsLogger?.StartMeasure();
                var resourceExportData = this.resourceExportService.GetResourceExportModel(syndicationModel.Name, exportContainers);
                this.diagnosticsLogger?.FinalizeMeasure(nameof(resourceExportService.GetResourceExportModel), true);

                return string.IsNullOrEmpty(jsonString)
                    ? throw new SyndicateException("Something went wrong when converting data to json string.")
                    : new ExportResult
                    {
                        Data = jsonString,
                        NumberOfEntities = numberOfEntities,
                        ResourceData = resourceExportData
                    };
            }
            finally
            {
                await this.cloudBlobManager.DeleteBlobsAsync(exportContainersCacheFolderName);
            }
        }

        /// <summary>
        /// Gets and saves review result to blob storage.
        /// </summary>
        /// <param name="syndicationModel">Syndication model.</param>
        /// <param name="batchSize">BatchSize setting value.</param>
        /// <param name="environmentGid">Environment Gid.</param>
        /// <param name="reviewId">Review guid.</param>
        /// <param name="cancellationToken">Cancellation token.</param>
        /// <exception cref="SyndicateException">
        /// Throws a <see cref="SyndicateException"/> if no entities were exported correctly or if data wasn't converted to json.
        /// </exception>
        public async Task GetAndSaveReviewResultAsync(SyndicationModel syndicationModel, int batchSize,
            string environmentGid, Guid reviewId, CancellationToken cancellationToken)
        {
            try
            {
                await this.GetExportContainersAsync(syndicationModel, batchSize, reviewId.ToString(), environmentGid, cancellationToken);
            }
            catch (Exception)
            {
                await this.SetReviewProgressAsFailedAsync(reviewId.ToString());
                throw;
            }
        }

        public async Task SetReviewProgressAsFailedAsync(string reviewId) => await this.cloudBlobManager.UploadTextAsync($"{reviewId}/progress",
            JsonConvert.SerializeObject(this.exportContainersValidator.GetProgressWithError(), Formatting.None));

        private async Task<IList<ExportContainer>> LoadExportContainersAsync(string exportContainersCacheFolderName)
        {
            var blobs = this.cloudBlobManager.GetBlobs(exportContainersCacheFolderName);
            var exportContainers = new List<ExportContainer>();
            foreach (var blob in blobs)
            {
                var blobName = blob.Name.Split('/').LastOrDefault();

                var containers = await this.cloudBlobManager.DownloadTextAsync($"{exportContainersCacheFolderName}/{blobName}");
                exportContainers.AddRange(JsonConvert.DeserializeObject<IList<ExportContainer>>(containers));
            }

            return exportContainers;
        }

        private async Task GetExportContainersAsync(SyndicationModel syndicationModel,
            int batchSize,
            string exportContainersCacheFolderName,
            string environmentGid,
            CancellationToken cancellationToken,
            bool applyDsaMapping = false)
        {
            ExportSerializationSettingsService.ConfigureSettings(syndicationModel, applyDsaMapping);
            var mappingId = MappingResolverService.GetMappingId(syndicationModel, applyDsaMapping);
            var mappingSource = MappingSourceResolverService.GetMappingSource(syndicationModel, applyDsaMapping);
            this.diagnosticsLogger?.StartMeasure();
            var mapManager = new MapManager(this.dataContext, mappingId, this.resourceExportService, mappingSource, environmentGid);
            mapManager.Load();
            this.diagnosticsLogger?.FinalizeMeasure(nameof(mapManager.Load), true);

            var exportManager = new ExportManager(mapManager.MapContainer, this.context, syndicationModel, this.resourceExportService, this.jobMetadataManager, this.diagnosticsLogger);

            var languages = this.dataContext.GetAllLanguages();

            // Sort languages the same way they are sorted in Portal.
            // This is to get the correct order of the languages to match the order in the Portal.
            languages?.Sort((c1, c2) =>
                string.Compare(c1.DisplayName, c2.DisplayName, StringComparison.OrdinalIgnoreCase));

            this.diagnosticsLogger?.StartMeasure();
            var previousExportContainers = await this.GetPreviousExportContainersAsync(exportContainersCacheFolderName, applyDsaMapping);
            this.diagnosticsLogger?.FinalizeMeasure(nameof(GetPreviousExportContainersAsync), true);

            var stopWatch = Stopwatch.StartNew();
            var entityIds = await this.entityResolverService.GetEntityIdsAsync(syndicationModel, mapManager.WorkareaEntityTypeId, previousExportContainers);
            stopWatch.Stop();
            this.diagnosticsLogger?.IncreaseReadEntityIdsAmount(entityIds.Count());
            this.diagnosticsLogger?.IncreaseReadEntityIdsTime(stopWatch.ElapsedMilliseconds);

            for (var i = 0; i < entityIds.Count; i += batchSize)
            {
                stopWatch = Stopwatch.StartNew();
                var currentBatch = (i / batchSize) + 1;
                var entityIdsToProcess = entityIds.Skip(i).Take(batchSize);
                var inRiverEntities = applyDsaMapping
                    ? this.entityService.ConvertExportContainersToEntityModels(previousExportContainers)
                    : this.entityService.GetInRiverEntities(entityIdsToProcess, mapManager, cancellationToken);
                stopWatch.Stop();
                this.diagnosticsLogger?.IncreaseReadFullEntitiesAmount(entityIdsToProcess.Count());
                this.diagnosticsLogger?.IncreaseReadFullEntitiesTime(stopWatch.ElapsedMilliseconds);

                if (!inRiverEntities.Any())
                {
                    throw new SyndicateException("Something went wrong when getting inriver entities.");
                }

                var exportContainers = exportManager.GetExportContainers(inRiverEntities, languages, cancellationToken);
                var serializedExportContainers = exportContainers.ToJson();
                if (syndicationModel.RunPreview && (!syndicationModel.RunDsaSyndication || applyDsaMapping))
                {
                    var isDynamicFormatFile = syndicationModel.MappingSource == SyndicationMappingSource.OutputAdapter;

                    this.diagnosticsLogger?.StartMeasure();
                    var validationResult = this.ValidateExportContainers(mapManager.MapContainer.MapFields, exportContainers, isDynamicFormatFile);
                    this.diagnosticsLogger?.FinalizeMeasure(nameof(ValidateExportContainers), true);

                    var progress = this.exportContainersValidator.GetUpdatedProgress(batchSize, currentBatch, entityIds.Count, inRiverEntities.Count);

                    this.diagnosticsLogger?.StartMeasure();
                    await this.UploadReviewResultToBlobStorageAsync(exportContainersCacheFolderName, currentBatch, serializedExportContainers, validationResult, progress);
                    this.diagnosticsLogger?.FinalizeMeasure(nameof(UploadReviewResultToBlobStorageAsync), true);
                }
                else
                {
                    this.diagnosticsLogger?.StartMeasure();
                    await this.UploadSyndicationResultToBlobStorageAsync(exportContainersCacheFolderName, i, batchSize, serializedExportContainers);
                    this.diagnosticsLogger?.FinalizeMeasure(nameof(UploadSyndicationResultToBlobStorageAsync), true);
                }

                this.syndicationJobResultService.UpdateNumberOfFunctions(inRiverEntities.Count, mapManager.MapContainer);
            }

            if (syndicationModel.DsaMappingId.HasValue && syndicationModel.RunDsaSyndication && !applyDsaMapping)
            {
                await this.GetExportContainersAsync(syndicationModel, batchSize, exportContainersCacheFolderName, environmentGid, cancellationToken, applyDsaMapping: true);
            }
        }

        private ValidationResult ValidateExportContainers(IList<MapField> formatFileFields, IList<ExportContainer> exportContainers, bool isDynamicFormatFile)
            => this.exportContainersValidator.Validate(formatFileFields, exportContainers, isDynamicFormatFile);

        private async Task<IList<ExportContainer>> GetPreviousExportContainersAsync(string exportContainersCacheFolderName, bool applyDsaMapping)
        {
            if (!applyDsaMapping)
            {
                return null;
            }

            var containers = await this.LoadExportContainersAsync(exportContainersCacheFolderName);
            await this.cloudBlobManager.DeleteBlobsAsync(exportContainersCacheFolderName);

            return containers;
        }

        private async Task UploadReviewResultToBlobStorageAsync(string exportContainersCacheFolderName,
            int currentBatch,
            string serializedExportContainers,
            ValidationResult validationResult,
            ValidationProgress progress)
        {
            await this.cloudBlobManager.UploadTextAsync($"{exportContainersCacheFolderName}/data-{currentBatch}", serializedExportContainers);
            await this.cloudBlobManager.UploadTextAsync($"{exportContainersCacheFolderName}/validation-{currentBatch}", JsonConvert.SerializeObject(validationResult, Formatting.None));
            await this.cloudBlobManager.UploadTextAsync($"{exportContainersCacheFolderName}/progress", JsonConvert.SerializeObject(progress, Formatting.None));
        }

        private async Task UploadSyndicationResultToBlobStorageAsync(string exportContainersCacheFolderName, int i, int batchSize, string serializedExportContainers)
            => await this.cloudBlobManager.UploadTextAsync($"{exportContainersCacheFolderName}/{i}-{i + batchSize}", serializedExportContainers);

        private string GetJsonString(SyndicationModel syndicationModel, IList<ExportContainer> exportContainers, bool enableCompression, bool enableHeader)
        {
            var jsonString = !enableHeader && enableCompression
                ? exportContainers.ToCompressedJson(this.context)
                : exportContainers.ToJson();

            if (!enableHeader)
            {
                return jsonString;
            }

            var modelJson = JObject.FromObject(syndicationModel);
            modelJson.Add("ExportedData", JArray.Parse(jsonString));
            jsonString = enableCompression ? modelJson.ToCompressedJson(this.context) : modelJson.ToString();

            return jsonString;
        }
    }
}
