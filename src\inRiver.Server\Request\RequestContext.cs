namespace inRiver.Server.Request
{
    using System;
    using System.Collections.Generic;
    using System.Globalization;
    using System.Runtime.CompilerServices;
    using System.Runtime.Serialization;

    using inRiver.Log;
    using inRiver.Server.DataAccess;
    using inRiver.Server.Managers;
    using Newtonsoft.Json;
    using Telemetry.Logging;

    [JsonObject(MemberSerialization.OptIn)]
    public class RequestContext : IRequestContext
    {
        private ICommonLogging logging;
        private IDataPersistance dataPersistance;
        private ExtensionManager extensionManager;

        public RequestContext(string configurationConnectionString, string readOnlyConfigurationConnectionString)
        {
            this.ConfigurationConnectionString = configurationConnectionString;
            this.ReadOnlyConfigDatabaseConnectionString = readOnlyConfigurationConnectionString;
        }

        [JsonConstructor]
        public RequestContext()
        {

        }

        public RequestContext(EnvironmentContextData environmentContextData)
        {
            this.EnvironmentId = environmentContextData.EnvironmentId;
            this.EnvironmentSafeName = environmentContextData.EnvironmentSafeName;
            this.EnvironmentFullName = environmentContextData.EnvironmentFullName;
            this.EnvironmentLocation = environmentContextData.EnvironmentLocation;
            this.CustomerSafeName = environmentContextData.CustomerSafeName;
            this.ConnectionString = environmentContextData.ConnectionString;
            this.CustomerId = environmentContextData.CustomerId;
            this.LogConnectionString = environmentContextData.LogConnectionString;
            this.LogTable = environmentContextData.LogTable;
            this.logging = new CommonLogging(this.LogTable, this.LogConnectionString);
            this.AssetServiceUrl = environmentContextData.AssetServiceInternalUrl;
            this.JobServiceUrl = environmentContextData.JobServiceUrl;
            this.EntityModel = environmentContextData.EntityModel;
            this.DataPersistance = IPMCServerPersistanceFactory.GetInstance(this);
        }

        public bool RequiredHttps { get; set; }

        [JsonProperty(Required = Required.AllowNull)]
        public string Module { get; set; }

        [JsonProperty(Required = Required.Always)]
        public string ConnectionString { get; set; }

        [JsonProperty(Required = Required.Always)]
        public string LogConnectionString { get; set; }

        [JsonProperty(Required = Required.Always)]
        public string ConfigurationConnectionString { get; set; }

        [JsonProperty(Required = Required.Always)]
        public string ReadOnlyConfigDatabaseConnectionString { get; set; }

        [JsonProperty(Required = Required.Always)]
        public string LogTable { get; set; }

        [JsonProperty(Required = Required.Always)]
        public string CustomerSafeName { get; set; }

        [JsonProperty(Required = Required.Always)]
        public string EnvironmentSafeName { get; set; }

        [JsonProperty(Required = Required.Always)]
        public string EnvironmentFullName { get; set; }

        [JsonProperty(Required = Required.Always)]
        public string EnvironmentLocation { get; set; }

        [JsonProperty(Required = Required.Always)]
        public int CustomerId { get; set; }

        [JsonProperty(Required = Required.Always)]
        public int EnvironmentId { get; set; }

        [JsonProperty(Required = Required.Always)]
        public string Username { get; set; }

        [JsonProperty(Required = Required.Always)]
        public string AssetServiceUrl { get; set; }

        [JsonProperty(Required = Required.Always)]
        public string JobServiceUrl { get; set; }

        [JsonProperty(Required = Required.Always)]
        public string TokenServiceUrl { get; set; }

        [JsonProperty(Required = Required.Always)]
        public string StatisticServiceUrl { get; set; }

        [JsonProperty(Required = Required.Always)]
        public List<string> Permissions { get; set; }

        [JsonProperty(Required = Required.Always)]
        public List<string> Roles { get; set; }

        [JsonProperty(Required = Required.Always)]
        public CultureInfo ModelLanguage { get; set; }

        [JsonProperty(Required = Required.Always)]
        public CultureInfo DataLanguage { get; set; }

        [IgnoreDataMember]
        public ICommonLogging Logging
        {
            get => this.logging ?? (this.logging = new CommonLogging(this.LogTable, this.LogConnectionString));
            set => logging = value;
        }

        [IgnoreDataMember]
        public ICommonLogging SystemLogging => SerilogCommonLogger.Instance;

        [IgnoreDataMember]
        public IDataPersistance DataPersistance
        {
            get => this.dataPersistance ?? (this.dataPersistance = IPMCServerPersistanceFactory.GetInstance(this));
            set => dataPersistance = value;
        }

        [IgnoreDataMember]
        public ExtensionManager ExtensionManager
        {
            get => this.extensionManager ?? (this.extensionManager = new ExtensionManager(this));
            set => this.extensionManager = value;
        }

        [JsonProperty(Required = Required.Always)]
        public string SendGridApiKey { get; set; }

        [JsonProperty(Required = Required.Always)]
        public string SmtpSendUser { get; set; }

        [JsonProperty(Required = Required.Always)]
        public string SmtpSendUserName { get; set; }

        [JsonProperty(Required = Required.Always)]
        public string LongRunningJobServiceUrl { get; set; }

        [JsonProperty(Required = Required.Always)]
        public int EntityModel { get; set; }

        // lean structure to lookup user's permissions related to the segmentId 
        // key: Content Segment Id, value: list of Permissions (permission name)
        [JsonProperty(Required = Required.AllowNull)]   // Will be null if the environment doesn't have multiple segments configured
        public Dictionary<int, List<string>> ContentSegmentIdToPermissions { get; set; }

        public bool UserHasPermission(string permissionName, int? contentSegmentId = null)
        {
            if (contentSegmentId != null && ContentSegmentIdToPermissions != null)
            {
                List<string> contentSegmentPermissions = null;

                if (ContentSegmentIdToPermissions.TryGetValue(contentSegmentId.Value, out contentSegmentPermissions)
                    && contentSegmentPermissions != null)
                {
                    return contentSegmentPermissions.Contains(permissionName);
                }

                return false;
            }

            // TODO (in a near future): we should discontinue treating username "system" as SUPER USER, this could be a security issue
            var retVal = (this.Username.Equals("system", StringComparison.InvariantCultureIgnoreCase))
                || (this.Username.Equals("Inspire", StringComparison.InvariantCultureIgnoreCase)) // Workaround to differentiate between Inspire and system user
                || (this.Roles != null && this.Roles.Contains("Administrator"))
                || (this.Permissions != null && this.Permissions.Contains(permissionName));

            return retVal;
        }

        public void Log(Remoting.Log.LogLevel logLevel, string message, Exception exception, [CallerMemberName] string memberName = "", [CallerFilePath] string filePath = "", [CallerLineNumber] int lineNo = 0, LogOrigin origin = LogOrigin.Undefined)
        {
            this.Logging.Log(this.GetLogLevel(logLevel), message, this.Module, exception.Message, exception.StackTrace, this.Username, memberName, filePath, lineNo, origin);
        }

        public void Log(Remoting.Log.LogLevel logLevel, string message, [CallerMemberName] string memberName = "", [CallerFilePath] string filePath = "", [CallerLineNumber] int lineNo = 0, LogOrigin origin = LogOrigin.Undefined)
        {
            this.Logging.Log(this.GetLogLevel(logLevel), message, this.Module, string.Empty, string.Empty, this.Username, memberName, filePath, lineNo, origin);
        }

        public void LogNear(inRiver.Log.LogLevel logLevel, string message, Exception exception, [CallerMemberName] string memberName = "", [CallerFilePath] string filePath = "", [CallerLineNumber] int lineNo = 0, LogOrigin origin = LogOrigin.Undefined)
        {
            this.Logging.Log(logLevel, message, this.Module, exception.Message, exception.StackTrace, this.Username, memberName, filePath, lineNo, origin);
        }

        public void LogNear(inRiver.Log.LogLevel logLevel, string message, [CallerMemberName] string memberName = "", [CallerFilePath] string filePath = "", [CallerLineNumber] int lineNo = 0, LogOrigin origin = LogOrigin.Undefined)
        {
            this.Logging.Log(logLevel, message, this.Module, string.Empty, string.Empty, this.Username, memberName, filePath, lineNo, origin);
        }

        private LogLevel GetLogLevel(Remoting.Log.LogLevel logLevel)
        {
            switch (logLevel)
            {
                case Remoting.Log.LogLevel.Error:
                    return LogLevel.Error;
                case Remoting.Log.LogLevel.Warning:
                    return LogLevel.Warning;
                case Remoting.Log.LogLevel.Information:
                    return LogLevel.Information;
                case Remoting.Log.LogLevel.Debug:
                    return LogLevel.Debug;
                default:
                    return LogLevel.Verbose;
            }
        }
    }
}
