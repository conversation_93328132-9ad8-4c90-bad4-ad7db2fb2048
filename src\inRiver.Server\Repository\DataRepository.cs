namespace inRiver.Server.Repository
{
    using System;
    using System.Collections.Generic;
    using System.Data;
    using System.Data.SqlClient;
    using System.Diagnostics;
    using System.Globalization;
    using System.Linq;
    using System.Net.Http;
    using System.Text;
    using System.Threading;
    using System.Threading.Tasks;
    using Core.Util;
    using inriver.Expressions.Client.Constants;
    using inRiver.Core.Access;
    using inRiver.Remoting.Dto;
    using inRiver.Remoting.Extension;
    using inRiver.Remoting.Objects;
    using inRiver.Remoting.Query;
    using inRiver.Remoting.Security;
    using inRiver.Remoting.Util;
    using inRiver.Server.DataAccess;
    using inRiver.Server.DataAccess.ExpressionUtil;
    using inRiver.Server.DataAccess.ThirdDataLayer;
    using inRiver.Server.Error;
    using inRiver.Server.EventPublishing;
    using inRiver.Server.Extension;
    using inRiver.Server.Managers;
    using inRiver.Server.MassUpdate;
    using inRiver.Server.Request;
    using inRiver.Server.Util;
    using inRiver.Server.Util.IllegalCharacters;
    using Inriver.Expressions.Dto;
    using Newtonsoft.Json;
    using Remoting.Settings;
    using Serilog;
    using Telemetry.Metrics;
    using LogLevel = inRiver.Remoting.Log.LogLevel;

    public partial class DataRepository
    {
        private readonly RequestContext context;

        private readonly IDataPersistance dataContext;

        private delegate void AsyncUpdateLinkCompleteness(DtoLink link);

        private const char Delimiter = ';';

        public DataRepository(RequestContext context)
        {
            if (!context.UserHasPermission(UserPermission.View))
            {
                throw ErrorUtility.GetSecurityException("User does not have permission to view data in inRiver");
            }

            this.dataContext = context.DataPersistance;
            this.context = context;
        }

        public DtoEntity GetEntity(int id, LoadLevel level)
        {
            DtoEntity entity = this.dataContext.GetEntity(id);

            if (entity == null)
            {
                return null;
            }

            if (level == LoadLevel.Shallow)
            {
                entity.LoadLevel = LoadLevel.Shallow;
                return entity;
            }

            entity.Fields = dataContext.GetFieldsForEntity(entity);
            entity.LoadLevel = LoadLevel.DataOnly;

            if (level == LoadLevel.DataOnly)
            {
                return entity;
            }

            entity.Links = this.dataContext.GetLinksForEntity(entity.Id, CancellationToken.None);
            entity.LoadLevel = LoadLevel.DataAndLinks;

            return entity;
        }

        public Entity GetFullEntity(int id, LoadLevel level)
        {
            DtoEntity dtoEntity = this.dataContext.GetEntity(id);

            if (dtoEntity == null)
            {
                return null;
            }

            Entity entity = this.dataContext.GetFullEntity(id, null);

            if (level == LoadLevel.Shallow)
            {
                return entity;
            }

            entity.Fields = this.dataContext.GetFullFieldsForEntity(entity);
            entity.LoadLevel = LoadLevel.DataOnly;

            if (level == LoadLevel.DataOnly)
            {
                return entity;
            }

            entity.Links = this.dataContext.GetFullLinksForEntity(entity.Id);
            entity.LoadLevel = LoadLevel.DataAndLinks;

            return entity;
        }

        public List<Entity> GetEntities(List<int> idList, LoadLevel level, CancellationToken cancellationToken)
        {
            List<DtoEntity> dtoEntities = this.GetDtoEntities(idList, level, cancellationToken);

            if (dtoEntities == null)
            {
                return new List<Entity>();
            }

            return DtoFactory.EntitiesFromDtos(dtoEntities, this.dataContext.GetAllEntityTypes(), this.dataContext.GetAllLinkTypes());
        }

        public async Task<List<Entity>> GetEntitiesAsync(List<int> idList, LoadLevel level, CancellationToken cancellationToken)
        {
            List<DtoEntity> dtoEntities = await this.GetDtoEntitiesAsync(idList, level, cancellationToken);

            if (dtoEntities == null)
            {
                return new List<Entity>();
            }

            return DtoFactory.EntitiesFromDtos(dtoEntities, this.dataContext.GetAllEntityTypes(), this.dataContext.GetAllLinkTypes());
        }

        public DataTable GetEntitiesAsDataTable(List<int> idList, string entityType, List<FieldType> fields, inRiver.iPMC.Persistance.ContentSegmentationEnum segmentationOption, CancellationToken cancellationToken)
        {
            return dataContext.GetEntitiesAsTable(idList, entityType, fields, segmentationOption, cancellationToken);
        }

        public List<DtoEntity> GetDtoEntities(List<int> list, LoadLevel level)
            => GetDtoEntities(list, level, CancellationToken.None);

        public List<DtoEntity> GetDtoEntities(List<int> list, LoadLevel level, CancellationToken cancellationToken)
        {
            if (list == null || list.Count() == 0)
            {
                return new List<DtoEntity>();
            }

            if (level == LoadLevel.Shallow)
            {
                return this.GetShallowEntities(list, cancellationToken);
            }

            Stopwatch stopwatch = Stopwatch.StartNew();

            List<DtoEntity> entities = this.dataContext.GetEntitiesWithData(list, cancellationToken);

            if (level == LoadLevel.DataAndLinks)
            {
                foreach (var entity in entities)
                {
                    cancellationToken.ThrowIfCancellationRequested();
                    entity.Links = this.dataContext.GetLinksForEntity(entity.Id, cancellationToken);
                    entity.LoadLevel = LoadLevel.DataAndLinks;
                }
            }

            stopwatch.Stop();
            context.Log(LogLevel.Verbose, "GetEntities (LoadLevel: " + level + ", total entities: " + entities.Count + ") took " + stopwatch.Elapsed.TotalMilliseconds.ToString("F02") + " ms");

            if (level == LoadLevel.DataOnly)
            {
                return entities;
            }

            return entities;
        }

        public async Task<List<DtoEntity>> GetDtoEntitiesAsync(List<int> list, LoadLevel level, CancellationToken cancellationToken)
        {
            if (list == null || list.Count() == 0)
            {
                return new List<DtoEntity>();
            }

            if (level == LoadLevel.Shallow)
            {
                return await this.GetShallowEntitiesAsync(list, cancellationToken);
            }

            Stopwatch stopwatch = Stopwatch.StartNew();

            List<DtoEntity> entities = await this.dataContext.GetEntitiesWithDataAsync(list, cancellationToken);

            if (level == LoadLevel.DataAndLinks)
            {
                foreach (var entity in entities)
                {
                    cancellationToken.ThrowIfCancellationRequested();
                    entity.Links = await this.dataContext.GetLinksForEntityAsync(entity.Id, cancellationToken);
                    entity.LoadLevel = LoadLevel.DataAndLinks;
                }
            }

            stopwatch.Stop();
            context.Log(LogLevel.Verbose, "GetEntities (LoadLevel: " + level + ", total entities: " + entities.Count + ") took " + stopwatch.Elapsed.TotalMilliseconds.ToString("F02") + " ms");

            return entities;
        }

        private List<DtoEntity> GetShallowEntities(List<int> list, CancellationToken cancellationToken)
        {
            Stopwatch stopwatch = Stopwatch.StartNew();

            List<DtoEntity> entities = new List<DtoEntity>();
            foreach (var ids in list.OrderBy(x => x).Batch(2000))
            {
                cancellationToken.ThrowIfCancellationRequested();
                List<DtoEntity> batchEntities = this.dataContext.GetEntities(ids.ToList());
                entities.AddRange(batchEntities);
            }

            stopwatch.Stop();
            this.context.Log(LogLevel.Verbose, "GetEntities (LoadLevel: Shallow, total entities: " + entities.Count + ") took " + stopwatch.Elapsed.TotalMilliseconds.ToString("F02") + " ms");

            return entities;

        }

        private async Task<List<DtoEntity>> GetShallowEntitiesAsync(List<int> list, CancellationToken cancellationToken)
        {
            Stopwatch stopwatch = Stopwatch.StartNew();

            List<DtoEntity> entities = new List<DtoEntity>();
            foreach (var ids in list.OrderBy(x => x).Batch(2000))
            {
                cancellationToken.ThrowIfCancellationRequested();
                var batchEntities = await this.dataContext.GetEntitiesAsync(ids.ToList());
                entities.AddRange(batchEntities);
            }

            stopwatch.Stop();
            this.context.Log(LogLevel.Verbose, "GetEntities (LoadLevel: Shallow, total entities: " + entities.Count + ") took " + stopwatch.Elapsed.TotalMilliseconds.ToString("F02") + " ms");

            return entities;
        }

        public List<FieldRevision> GetFieldRevisions(int entityId, string fieldTypeId, int maxNumberOfRevisions)
        {
            if (string.IsNullOrWhiteSpace(fieldTypeId))
            {
                return null;
            }

            return this.dataContext.GetFieldRevisions(entityId, fieldTypeId, maxNumberOfRevisions);
        }

        public List<int> GetAllEntityIdsForEntityType(string entityTypeId) => this.dataContext.GetAllEntityIdsForEntityType(entityTypeId);

        public DtoEntity AddEntity(Entity entity)
        {
            if (this.context.EntityModel == 2)
            {
                return ((IPMCServer3DLPersistanceAdapter)this.dataContext).AddEntity(entity);
            }

            if (!(this.context.UserHasPermission(UserPermission.AddEntity) || this.context.UserHasPermission(UserPermission.CopyEntity)))
            {
                throw ErrorUtility.GetSecurityException("User does not have permission to add entities to inRiver");
            }

            if (entity == null)
            {
                context.Log(LogLevel.Warning, "Trying to add null Entity");
                throw ErrorUtility.GetArgumentException("AddEntity", "Entity", "Trying to add null Entity");
            }

            // Run extensions before validation so we don't get crap into the system
            entity = this.CallServerExtensions(ExtensionEvent.OnAdd, entity, null);

            if (!string.IsNullOrWhiteSpace(entity.FieldSetId))
            {
                FieldSet fieldSet = this.dataContext.GetFieldSet(entity.FieldSetId);

                if (fieldSet == null)
                {
                    context.Log(LogLevel.Warning, "No fieldset exists with id " + entity.FieldSetId);
                    throw ErrorUtility.GetArgumentException("AddEntity", "fieldSetId", "No fieldset exists with id " + entity.FieldSetId);
                }

                if (!entity.EntityType.Id.Equals(fieldSet.EntityTypeId, StringComparison.InvariantCultureIgnoreCase))
                {
                    context.Log(LogLevel.Warning, "The supplied fieldset id belongs to other entity type than entity");
                    throw ErrorUtility.GetArgumentException("AddEntity", "fieldSetId", "The supplied fieldset id belongs to other entity type than entity");
                }
            }

            this.ValidateAddEntityFields(entity);

            var expressionWrapper = new ExpressionWrapper(this.dataContext, this.context);
            var expressions = expressionWrapper.CreateDefaultExpressionsAndEvaluate(entity, entity.Fields.Select(x => x.FieldType));

            entity.DisplayName = entity.Fields.FirstOrDefault(f => f.FieldType.IsDisplayName);
            entity.DisplayDescription = entity.Fields.FirstOrDefault(f => f.FieldType.IsDisplayDescription);

            entity.CreatedBy = this.context.Username;
            entity.ModifiedBy = this.context.Username;

            DtoEntity createdEntity = this.dataContext.AddEntityWithFields(entity);

            expressionWrapper.SaveExpressionsForEntity(createdEntity.Id, expressions);

            int createdId = createdEntity.Id;

            if (entity.EntityType.Id == "Resource")
            {
                this.dataContext.ReCalculateEntityMainPicture(createdId, "Resource");
            }

            var revisions = new List<Field>();
            revisions.AddRange(entity.Fields);
            revisions.AddRange(expressionWrapper.Revisions);
            this.dataContext.SaveEntityFieldRevisionHistorySynchronous(createdId, revisions, true);

            createdEntity.Fields = this.dataContext.GetFieldsForEntity(createdEntity);

            createdEntity.LoadLevel = LoadLevel.DataOnly;

            string displayNameFieldType = entity.EntityType.FieldTypes.FirstOrDefault(ft => ft.IsDisplayName)?.Id;
            createdEntity.DisplayName = createdEntity.Fields.FirstOrDefault(f => f.FieldTypeId == displayNameFieldType);

            string displayDescriptionFieldType = entity.EntityType.FieldTypes.FirstOrDefault(ft => ft.IsDisplayDescription)?.Id;
            createdEntity.DisplayDescription = createdEntity.Fields.FirstOrDefault(f => f.FieldTypeId == displayDescriptionFieldType);

            if (entity.EntityType.Id.Equals("Channel") || entity.EntityType.Id.Equals("Publication"))
            {
                this.dataContext.ReloadChannel(createdId);
            }

            createdEntity = new CompletenessRepository(this.context).SetEntityCompleteness(createdEntity);

            EventPublisher.NotifyEntityAdded(this.context, entity);

            new SearchManager(this.context).AddToSearchIndex(createdEntity);

            // Update Supplier Editing/Contribute
            CreateContributeEntity(new int[] { createdEntity.Id });

            if (entity.EntityType.Id != "Resource")
            {
                return createdEntity;
            }

            return createdEntity;
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Reliability", "CA2000:Dispose objects before losing scope", Justification = "Http clients should not be disposed.")]
        public bool CreateContributeEntity(int[] entityIds)
        {
            try
            {
                var httpClient = StaticHttpClientFactory.CreateHttpClient(this.context.JobServiceUrl);
                httpClient.DefaultRequestHeaders.Accept.Clear();
                string serializedData = JsonConvert.SerializeObject(entityIds);

                var response = httpClient.PostAsJsonAsync($"api/Entity/CreateEntities/{this.context.CustomerSafeName}/{this.context.EnvironmentSafeName}", serializedData);

                if (response.Result.IsSuccessStatusCode)
                {
                    return true;
                }
            }
            catch (Exception exception)
            {
                Log.Error(exception, $"Failed to create contribute entity for ids {entityIds}... environment: {this.context.CustomerSafeName}/{this.context.EnvironmentSafeName}");
            }


            return false;
        }

        [System.Diagnostics.CodeAnalysis.SuppressMessage("Reliability", "CA2000:Dispose objects before losing scope", Justification = "Http clients should not be disposed.")]
        private void UpdateContributeEntity(int[] entityIds)
        {
            try
            {
                var httpClient = StaticHttpClientFactory.CreateHttpClient(this.context.JobServiceUrl);
                httpClient.DefaultRequestHeaders.Accept.Clear();
                string serializedData = JsonConvert.SerializeObject(entityIds);

                httpClient.PostAsJsonAsync($"api/Entity/UpdateEntities/{this.context.CustomerSafeName}/{this.context.EnvironmentSafeName}", serializedData);
            }
            catch (Exception exception)
            {
                Log.Error(exception, $"Failed to update contribute entity for ids {entityIds}... environment: {this.context.CustomerSafeName}/{this.context.EnvironmentSafeName}");
            }
        }

        public DtoEntity UpdateEntity(Entity entity, Stopwatch upsertEntityDurationMetricStopwatch = null)
        {
            if (this.context.EntityModel == 2)
            {
                return ((IPMCServer3DLPersistanceAdapter)this.dataContext).UpdateEntity(entity);
            }

            if (!this.context.UserHasPermission(UserPermission.UpdateEntity))
            {
                throw ErrorUtility.GetSecurityException("User does not have permission to update entities in inRiver");
            }

            if (entity == null)
            {
                context.Log(LogLevel.Warning, "Trying to update null Entity");
                throw ErrorUtility.GetArgumentException("UpdateEntity", "Entity", "Trying to update null Entity");
            }

            upsertEntityDurationMetricStopwatch = upsertEntityDurationMetricStopwatch.CreateOrReset();

            if (entity.LoadLevel == LoadLevel.Shallow)
            {
                upsertEntityDurationMetricStopwatch.Start();

                var returnEntity = this.dataContext.GetEntity(entity.Id);
                returnEntity.Fields = this.dataContext.GetFieldsForEntity(returnEntity);
                returnEntity.LoadLevel = LoadLevel.DataOnly;

                upsertEntityDurationMetricStopwatch.Stop();
                UpsertEntityDurationMetric<DataRepository>.TrackValue(
                    upsertEntityDurationMetricStopwatch.ElapsedMilliseconds,
                    entity,
                    this.context.CustomerSafeName,
                    this.context.EnvironmentSafeName,
                    this.context.EntityModel,
                    nameof(UpdateEntity));
                return returnEntity;
            }

            upsertEntityDurationMetricStopwatch.Start();

            Entity persistedEntity = this.dataContext.GetFullEntity(entity.Id, entity.EntityType, false);

            if (persistedEntity == null)
            {
                context.Log(LogLevel.Warning, "Trying to update Entity that does not exist");
                throw ErrorUtility.GetArgumentException("UpdateEntity", "Entity", "Trying to update Entity that does not exist");
            }

            // check if user has permission to update entity based on the segment permission
            int? existingSegmentId = persistedEntity.Segment?.Id;
            if (existingSegmentId == null || !this.context.UserHasPermission(UserPermission.UpdateEntity, existingSegmentId))
            {
                throw ErrorUtility.GetSecurityException($"shared.misc.inriverutil.server_unauth_segment_failed_action");
            }

            entity = this.CopyEntityValuesAndResetLinks(entity, persistedEntity);

            if (!string.IsNullOrWhiteSpace(persistedEntity.Locked))
            {
                if (persistedEntity.Locked.ToLower() != this.context.Username.ToLower())
                {
                    context.Log(LogLevel.Warning, "Trying to update Entity that is locked by another user");
                    throw new InvalidOperationException($"Trying to update Entity that is locked by user {persistedEntity.Locked}");
                }
            }

            entity.FieldSetId = persistedEntity.FieldSetId;

            persistedEntity.Fields = this.dataContext.GetFullFieldsForEntity(entity);
            persistedEntity.LoadLevel = LoadLevel.DataOnly;

            // Add missing fields to the entity (UpdateFieldsForEntity). Need to make sure we get a clone,
            // otherwise extension updates won't get detected
            foreach (Field field in persistedEntity.Fields)
            {
                if (!entity.Fields.Exists(f => f.FieldType.Id == field.FieldType.Id))
                {
                    entity.Fields.Add((Field)field.Clone());
                }
            }

            upsertEntityDurationMetricStopwatch.Stop();

            // Run extensions before validation so we don't get crap into the system
            entity = this.CallServerExtensions(ExtensionEvent.OnUpdate, entity, null);

            upsertEntityDurationMetricStopwatch.Start();

            this.ValidateUpdateEntityFields(entity, persistedEntity);

            var expressionDtoEntity = DtoFactory.DtoFromEntity(entity);
            var expressionWrapper = new ExpressionWrapper(this.dataContext, this.context);
            expressionWrapper.EvaluateExpressionsForEntity(expressionDtoEntity, DtoFactory.DtoFromEntity(persistedEntity), entity.Fields.Select(x => x.FieldType));
            var fieldTypeDictionary = entity.Fields.ToDictionary(f => f.FieldType.Id, f => f.FieldType);

            var updatedFields = new List<Field>();
            var fieldsToAdd = new List<Field>();
            var fieldsToUpdate = new List<Field>();
            var cvlFieldsToUpdate = new List<Field>();
            List<CultureInfo> serverLanguages = null;

            foreach (var dtoField in expressionDtoEntity.Fields)
            {
                var field = DtoFactory.FieldFromDto(dtoField, fieldTypeDictionary[dtoField.FieldTypeId]);
                var persistedField = persistedEntity.Fields.Find(f => f.FieldType.Id == field.FieldType.Id);

                if (field.IsEmpty() && persistedField.Revision == 0)
                {
                    continue;
                }

                field.Revision = persistedField.Revision + 1;

                if (field.FieldType.DataType == DataType.LocaleString)
                {
                    serverLanguages ??= this.dataContext.GetAllLanguages();
                    field.Data = this.EnsureLocaleStringHasAllLanguages((LocaleString)field.Data, (LocaleString)persistedField.Data, serverLanguages);
                }

                IllegalCharacters.CheckField(field, "UpdateEntity");

                if (persistedField.Revision == 0 && !field.IsEmpty())
                {
                    fieldsToAdd.Add(field);

                    continue;
                }

                if (field.ValueHasBeenModified(persistedEntity.GetField(field.FieldType.Id).Data))
                {
                    var currentFieldVersion = this.GetFieldRevisions(field.EntityId, field.FieldType.Id, 1);

                    if (currentFieldVersion is null || !currentFieldVersion.Any())
                    {
                        field.Revision = 1;
                    }
                    else
                    {
                        field.Revision = currentFieldVersion.First().Revision + 1;
                    }

                    if (field.FieldType.DataType == "CVL")
                    {
                        cvlFieldsToUpdate.Add(field);
                    }
                    else
                    {
                        fieldsToUpdate.Add(field);
                    }
                }
            }

            if (cvlFieldsToUpdate.Count > 0)
            {
                var childCvlFieldsToReset = ChildCVLFieldsResetHelper.GetChildCVLFieldsToReset(cvlFieldsToUpdate, persistedEntity, this.context);
                fieldsToUpdate.AddRange(cvlFieldsToUpdate);
                fieldsToUpdate.AddRange(childCvlFieldsToReset);
            }

            if (fieldsToAdd.Count > 0)
            {
                this.dataContext.AddFields(fieldsToAdd);
                updatedFields.AddRange(fieldsToAdd);
            }

            if (fieldsToUpdate.Count > 0)
            {
                this.dataContext.UpdateFields(fieldsToUpdate);

                updatedFields.AddRange(fieldsToUpdate);
            }

            upsertEntityDurationMetricStopwatch.Stop();

            if (updatedFields.Count == 0)
            {
                upsertEntityDurationMetricStopwatch.Start();
                var resultEntity = this.dataContext.GetEntityWithData(entity.Id);
                upsertEntityDurationMetricStopwatch.Stop();
                UpsertEntityDurationMetric<DataRepository>.TrackValue(
                    upsertEntityDurationMetricStopwatch.ElapsedMilliseconds,
                    entity,
                    this.context.CustomerSafeName,
                    this.context.EnvironmentSafeName,
                    this.context.EntityModel,
                    nameof(UpdateEntity));
                return resultEntity;
            }

            var revisions = new List<Field>();
            revisions.AddRange(updatedFields);
            revisions.AddRange(expressionWrapper.Revisions);
            this.dataContext.SaveEntityFieldRevisionHistory(entity.Id, revisions, false, this.context.Username);
            this.dataContext.UpdateEntityChangeSet(entity.Id, updatedFields);

            List<string> updatedFieldIds = (from f in updatedFields select f.FieldType.Id).ToList();

            if (updatedFieldIds.Contains("ResourceFileId"))
            {
                this.dataContext.ReCalculateEntityMainPicture(entity.Id, "Resource");

                var links = this.dataContext.GetInboundLinksForEntity(entity.Id);

                foreach (var link in links.Where(link => link.Index == 0))
                {
                    this.dataContext.ReCalculateEntityMainPicture(link.Source.Id, link.Source.EntityTypeId);
                }
            }

            if (updatedFields.Exists(f => f.FieldType.IsDisplayName || f.FieldType.IsDisplayDescription))
            {
                this.dataContext.ReCalculateDisplayValuesForEntity(entity.Id, updatedFields);
            }

            upsertEntityDurationMetricStopwatch.Start();
            var dtoEntity = this.dataContext.GetEntity(entity.Id);
            upsertEntityDurationMetricStopwatch.Stop();

            if (updatedFields.Exists(f => f.FieldType.IsDisplayName || f.FieldType.IsDisplayDescription))
            {
                new SearchManager(this.context).UpdateSearchIndex(dtoEntity);
            }

            dtoEntity.Fields = this.dataContext.GetFieldsForEntity(dtoEntity);
            dtoEntity.LoadLevel = LoadLevel.DataOnly;

            dtoEntity = new CompletenessRepository(this.context).SetEntityCompleteness(dtoEntity);

            EventPublisher.NotifyEntityUpdated(this.context, entity.Id, entity.EntityType.Id, string.Join(",", updatedFieldIds.ToArray()));

            new ChannelRepository(this.context).UpdateEntity(entity.Id, updatedFields);

            // Update Contribute
            UpdateContributeEntity(new int[] { entity.Id });

            UpsertEntityDurationMetric<DataRepository>.TrackValue(
                upsertEntityDurationMetricStopwatch.ElapsedMilliseconds,
                entity,
                this.context.CustomerSafeName,
                this.context.EnvironmentSafeName,
                this.context.EntityModel,
                nameof(UpdateEntity));
            return dtoEntity;
        }

        #region Link

        public DtoLink AddLink(Link link)
        {
            if (!this.context.UserHasPermission(UserPermission.AddLink))
            {
                throw ErrorUtility.GetSecurityException("User does not have permission to add links to inRiver");
            }

            this.ValidateLink(link, "AddLink", false);

            this.CallServerExtensions(ExtensionEvent.OnLink, null, link);

            DtoLink dtoLink = this.dataContext.AddLink(link);

            if (dtoLink.Target.EntityTypeId.Equals("Resource") && !dtoLink.Source.EntityTypeId.Equals("Resource") && link.Index == 0)
            {
                this.dataContext.ReCalculateEntityMainPicture(dtoLink.Source.Id, dtoLink.Source.EntityTypeId);
            }

            this.LinkSpecification(dtoLink);

            new ChannelRepository(this.context).AddLink(dtoLink);

            AsyncUpdateLinkCompleteness caller = this.UpdateLinkCompleteness;
            Task.Run(() => caller.Invoke(dtoLink));

            EventPublisher.NotifyLinkAdded(this.context, dtoLink);

            return dtoLink;
        }

        public List<DtoLink> GetLinksForEntity(int entityId, CancellationToken cancellationToken)
        {
            return this.dataContext.GetLinksForEntity(entityId, cancellationToken);
        }

        public bool DeleteLink(int linkId, bool force)
        {
            var link = this.dataContext.GetFullLink(linkId);

            if (link == null)
            {
                return false;
            }

            if (!force && this.dataContext.HasLinkRuleDefinition(link.Source.Id, link.LinkType.Id))
            {
                this.context.Log(LogLevel.Warning, "Not allowed to manually delete links when link rule exist");
                throw ErrorUtility.GetArgumentFault("DeleteLink", "linkId", "Not allowed to manually delete links when link rule exist");
            }

            this.CallServerExtensions(ExtensionEvent.OnUnlink, null, link);

            var result = this.dataContext.DeleteLink(linkId);

            if (this.ShouldUpdateSortOrder(link.LinkType.Id))
            {
                this.dataContext.UpdateLinkSortOrderOnSourceEntity(link.Source.Id, link.Index, link.LinkType.Id);
            }

            if (link.Index == 0 && link.Target.EntityType.Id == "Resource")
            {
                this.dataContext.ReCalculateEntityMainPicture(link.Source.Id, link.Source.EntityType.Id);
            }

            var dtoLink = DtoFactory.DtoFromLink(link);
            this.UnlinkSpecification(dtoLink);

            new ChannelRepository(this.context).DeleteLink(dtoLink);

            AsyncUpdateLinkCompleteness caller = this.UpdateLinkCompleteness;
            Task.Run(() => caller.Invoke(dtoLink));

            EventPublisher.NotifyLinkDeleted(this.context, dtoLink);

            return result;
        }

        public bool DeleteAllLinksForLinkType(int entityId, string linkTypeId, LinkDirection linkDirection, Func<int, Task> onProgressChanged = null)
        {
            var linkIds = this.dataContext.GetAllLinkIdsForEntityAndLinkType(entityId, linkTypeId, linkDirection);
            if (linkIds == null || linkIds.Count == 0)
            {
                return false;
            }

            linkIds = linkIds.OrderByDescending(id => id).ToList();
            var jobProgressUpdater = new JobProgressUpdater(linkIds.Count, onProgressChanged);
            foreach (var linkId in linkIds)
            {
                _ = this.DeleteLink(linkId, true);
                jobProgressUpdater.Increment();
            }

            jobProgressUpdater.EnsureFinalUpdateAsync().Wait(CancellationToken.None);
            return true;
        }

        #endregion

        private bool ShouldUpdateSortOrder(string linkTypeId)
        {
            var setting = this.dataContext.GetServerSetting(ServerConstants.LINKTYPE_IGNORE_SORTORDER_UPDATE_ON_REMOVE);

            if (string.IsNullOrEmpty(setting))
            {
                return true;
            }

            var linkTypeIds = setting.Split(Delimiter);

            return !linkTypeIds.Contains(linkTypeId);
        }

        private void UnlinkSpecification(DtoLink link)
        {
            if (link?.Source?.EntityTypeId == null)
            {
                return;
            }

            if (link.Target?.EntityTypeId is "Specification")
            {
                this.dataContext.UnlinkSpecification(link);
            }
        }

        #region Search

        public List<DtoEntity> Search(ComplexQuery query, LoadLevel level)
        {
            Stopwatch stopwatch = Stopwatch.StartNew();

            List<int> result = Search(query);

            stopwatch.Stop();
            context.Log(LogLevel.Debug, $" Performance - Search {stopwatch.Elapsed.TotalMilliseconds.ToString("F02")}ms");
            context.Log(LogLevel.Verbose, "Complex Search took " + stopwatch.Elapsed.TotalMilliseconds.ToString("F02") + " ms");

            List<DtoEntity> entities = this.GetDtoEntities(result, level, CancellationToken.None);

            return entities;
        }

        #endregion

        #region Private Methods

        private void UpdateLinkCompleteness(DtoLink link)
        {
            var completenessRepository = new CompletenessRepository(this.context);
            link.Source = completenessRepository.SetEntityCompleteness(link.Source);
            link.Target = completenessRepository.SetEntityCompleteness(link.Target);
        }

        private List<int> SearchQuery(Query query)
        {
            List<int> searchHits = new List<int>();

            bool firstIteration = true;

            foreach (Criteria criteria in query.Criteria)
            {
                List<int> ids = Search(criteria);

                if (firstIteration)
                {
                    searchHits.AddRange(ids);
                    firstIteration = false;
                    continue;
                }

                if (query.Join == Join.And)
                {
                    searchHits = searchHits.Intersect(ids).ToList();
                }

                if (query.Join == Join.Or)
                {
                    searchHits = searchHits.Union(ids).ToList();
                }
            }

            if (query.SubQuery == null)
            {
                return searchHits.Distinct().ToList();
            }

            List<int> subQueryResult = this.SearchQuery(query.SubQuery);

            if (query.SubQuery.Join == Join.And)
            {
                return searchHits.Intersect(subQueryResult).Distinct().ToList();
            }

            if (query.SubQuery.Join == Join.Or)
            {
                return searchHits.Union(subQueryResult).Distinct().ToList();
            }

            return searchHits;
        }

        private List<string> SplitCriteraValues(string value)
        {
            if (string.IsNullOrEmpty(value))
            {
                return new List<string>();
            }

            return value.Split(new[] { ';' }, StringSplitOptions.RemoveEmptyEntries).ToList();
        }

        private void ValidateLink(Link link, string callingMethod, bool force)
        {
            if (link == null)
            {
                context.Log(LogLevel.Warning, "Link cannot be null");
                throw ErrorUtility.GetArgumentException(callingMethod, "Link", "Link cannot be null");
            }

            if (link.LinkType == null)
            {
                context.Log(LogLevel.Warning, "Link Type cannot be null");
                throw ErrorUtility.GetArgumentException(callingMethod, "LinkType", "Link Type cannot be null");
            }

            if (!string.IsNullOrEmpty(link.LinkType.LinkEntityTypeId) && link.LinkEntity != null)
            {
                this.ValidateLinkWithLinkEntity(link, callingMethod, force);
            }
            else
            {
                DtoLink validationLink = this.dataContext.GetValidationLink(link);

                var linkInfo = $"link.LinkType.Id: {link.LinkType?.Id} " +
                            $"link.Source.Id: {link.Source?.Id} " +
                            $"link.Target.Id: {link.Target?.Id} " +
                            $"link.Id: {validationLink?.Id}";

                if (validationLink == null)
                {
                    context.Log(LogLevel.Error, $"GetValidationLink failed to create a valid link. {linkInfo}");
                    throw ErrorUtility.GetArgumentException(callingMethod, "Link", "GetValidationLink failed to create a valid link.");
                }

                if (link.Source.Id == link.Target.Id)
                {
                    context.Log(LogLevel.Error, $"Cannot add Link entity to itself. {linkInfo}");
                    throw ErrorUtility.GetArgumentException(callingMethod, "TargetEntityId", $"Cannot add Link entity to itself.");
                }

                if (validationLink.Id > 0)
                {
                    context.Log(LogLevel.Error, $"Link already exists: {linkInfo}");
                    throw ErrorUtility.GetArgumentException(callingMethod, "Link", $"Link already exists.");
                }

                if (validationLink.Target.EntityTypeId == "Specification"
                    && validationLink.Source.EntityTypeId != "Specification")
                {
                    if (this.dataContext.GetLinkCountForOutboundLinkType(link.LinkType.Id, validationLink.Source.Id) > 0)
                    {
                        context.Log(LogLevel.Error, $"Only one Specification link is allowed: {linkInfo}");
                        throw ErrorUtility.GetOperationCanceledException("Only one Specification link is allowed. To change specification please remove the existing link.");
                    }
                }

                if (validationLink.Target.EntityTypeId == "Specification"
                    && validationLink.Source.EntityTypeId == "Specification")
                {
                    if (
                        this.dataContext.GetInboundLinksForEntityAndLinkType(validationLink.Source.Id, link.LinkType.Id)
                            .Count > 0)
                    {
                        context.Log(LogLevel.Error, $"Specification inheritance is only allowed for one level. {linkInfo}");
                        throw ErrorUtility.GetOperationCanceledException("Specification inheritance is only allowed for one level.");
                    }

                    if (this.dataContext.GetLinkCountForOutboundLinkType(link.LinkType.Id, validationLink.Target.Id) > 0)
                    {
                        context.Log(LogLevel.Error, $"Specification inheritance is only allowed for one level. {linkInfo}");
                        throw ErrorUtility.GetOperationCanceledException("Specification inheritance is only allowed for one level.");
                    }
                }

                if (!force && this.dataContext.HasLinkRuleDefinition(link.Source.Id, link.LinkType.Id))
                {
                    context.Log(LogLevel.Error, $"Not allowed to manually add links when rule links exist. {linkInfo}");
                    throw ErrorUtility.GetArgumentException(callingMethod, "Link", "Not allowed to manually add links when rule links exist");
                }
            }
        }

        private void ValidateLinkWithLinkEntity(Link link, string callingMethod, bool force)
        {
            LinkType linkType = this.dataContext.GetLinkType(link.LinkType.Id);

            if (linkType == null)
            {
                context.Log(LogLevel.Warning, "Link Type does not exist");
                throw ErrorUtility.GetArgumentException(callingMethod, "LinkType", "Link Type does not exist");
            }

            if (link.Source == null)
            {
                context.Log(LogLevel.Warning, "Link source entity cannot be null");
                throw ErrorUtility.GetArgumentException(callingMethod, "Source", "Link source entity cannot be null");
            }

            DtoEntity sourceEntity = this.dataContext.GetEntity(link.Source.Id);

            if (sourceEntity == null)
            {
                context.Log(LogLevel.Warning, "Link source entity does not exist");
                throw ErrorUtility.GetArgumentException(callingMethod, "SourceEntityId", "Link source entity does not exist");
            }

            if (sourceEntity.EntityTypeId != linkType.SourceEntityTypeId)
            {
                context.Log(LogLevel.Warning, "Link source entity type does not match source entity for link type");
                throw ErrorUtility.GetArgumentException(callingMethod, "SourceEntityId", "Link source entity type does not match source entity for link type");
            }

            if (link.Target == null)
            {
                context.Log(LogLevel.Warning, "Link target entity cannot be null");
                throw ErrorUtility.GetArgumentException(callingMethod, "Target", "Link target entity cannot be null");
            }

            DtoEntity targetEntity = this.dataContext.GetEntity(link.Target.Id);

            if (targetEntity == null)
            {
                context.Log(LogLevel.Warning, "Link target entity does not exist");
                throw ErrorUtility.GetArgumentException(callingMethod, "TargetEntityId", "Link target entity does not exist");
            }

            if (targetEntity.EntityTypeId != linkType.TargetEntityTypeId)
            {
                context.Log(LogLevel.Warning, "Link target entity type does not match target entity for link type");
                throw ErrorUtility.GetArgumentException(callingMethod, "TargetEntityId", "Link target entity type does not match target entity for link type");
            }

            if (link.LinkEntity == null)
            {
                context.Log(LogLevel.Warning, "Link entity is null");
                throw ErrorUtility.GetArgumentException(callingMethod, "LinkEntity", "Link entity is null");
            }

            DtoEntity linkEntity = this.dataContext.GetEntity(link.LinkEntity.Id);

            if (linkEntity == null)
            {
                context.Log(LogLevel.Warning, "Link entity does not exist");
                throw ErrorUtility.GetArgumentException(callingMethod, "LinkEntityId", "Link entity does not exist");
            }

            if (linkEntity.EntityTypeId != linkType.LinkEntityTypeId)
            {
                context.Log(LogLevel.Warning, "Link entity entity type does not macth id entity for link entity type");
                throw ErrorUtility.GetArgumentException(callingMethod, "LinkEntityTypeId", "Link entity entity type does not macth id entity for link entity type");
            }


            if (link.Source.Id == link.Target.Id)
            {
                context.Log(LogLevel.Warning, "Cannot add Link entity to itself");
                throw ErrorUtility.GetArgumentException(callingMethod, "TargetEntityId", "Cannot add Link entity to itself");
            }

            if (this.dataContext.LinkAlreadyExists(link.Source.Id, link.Target.Id, link.LinkEntity.Id, link.LinkType.Id))
            {
                context.Log(LogLevel.Warning, "Link already exists");
                throw ErrorUtility.GetArgumentException(callingMethod, "Link", "Link already exists");
            }

            if (targetEntity.EntityTypeId == "Specification" && sourceEntity.EntityTypeId != "Specification")
            {
                if (this.dataContext.GetLinkCountForOutboundLinkType(link.LinkType.Id, sourceEntity.Id) > 0)
                {
                    throw ErrorUtility.GetOperationCanceledException("Only one Specification link is allowed. To change specification please remove the existing link.");
                }
            }

            if (targetEntity.EntityTypeId == "Specification" && sourceEntity.EntityTypeId == "Specification")
            {
                if (this.dataContext.GetInboundLinksForEntityAndLinkType(sourceEntity.Id, link.LinkType.Id).Count > 0)
                {
                    context.Log(LogLevel.Warning, "Specification inheritance is only allowed for one level.");
                    throw ErrorUtility.GetOperationCanceledException("Specification inheritance is only allowed for one level.");
                }

                if (this.dataContext.GetLinkCountForOutboundLinkType(link.LinkType.Id, targetEntity.Id) > 0)
                {
                    context.Log(LogLevel.Warning, "Specification inheritance is only allowed for one level.");
                    throw ErrorUtility.GetOperationCanceledException("Specification inheritance is only allowed for one level.");
                }
            }

            if (!force && this.dataContext.HasLinkRuleDefinition(link.Source.Id, link.LinkType.Id))
            {
                context.Log(LogLevel.Warning, "Not allowed to manually add links when rule links exist");
                throw ErrorUtility.GetArgumentException(callingMethod, "Link", "Not allowed to manually add links when rule links exist");
            }
        }

        private void ValidateAddEntityFields(Entity entity)
        {
            var expressionWrapper = new ExpressionWrapper(this.dataContext, this.context);
            if (!expressionWrapper.ParseExpressions(entity.Fields, out var errorPair))
            {
                throw ErrorUtility.GetArgumentFault(
                    "AddEntity",
                    errorPair.Key,
                    $"Trying to add entity with invalid expression for field {errorPair.Key} ({errorPair.Value})");
            }

            var cachedKeys = new Dictionary<string, CVLValue>();
            List<CultureInfo> serverLanguages = null;

            foreach (Field field in entity.Fields)
            {
                if (field.FieldType == null)
                {
                    context.Log(LogLevel.Warning, "Trying to add entity with field type missing");
                    throw ErrorUtility.GetArgumentException("AddEntity", "Field.FieldType", "Trying to add entity with field type missing");
                }

                if (string.IsNullOrEmpty(field.FieldType.Id))
                {
                    context.Log(LogLevel.Warning, "Trying to add entity with field type id missing");
                    throw ErrorUtility.GetArgumentException("AddEntity", "Field.FieldType.Id", "Trying to add entity with field type id missing");
                }

                if (!field.ValidateMandatory())
                {
                    context.Log(LogLevel.Warning, "Trying to add entity with mandatory field not set");
                    throw ErrorUtility.GetArgumentException("AddEntity", field.FieldType.Id, "Trying to add entity with mandatory field not set");
                }

                if (field.IsEmpty())
                {
                    continue;
                }

                if (!Remoting.Util.Utility.ValueMatchesDataType(field.FieldType.DataType, field.Data, field.FieldType.ExpressionSupport))
                {
                    context.Log(LogLevel.Warning, "Trying to add entity with invalid value for field " + field.FieldType.Id);
                    throw ErrorUtility.GetArgumentException("AddEntity", field.FieldType.Id, "Trying to add entity with invalid value for field " + field.FieldType.Id);
                }

                if (Utility.StringIsInriverExpression(field.FieldType.ExpressionSupport, field.Data as string))
                {
                    continue;
                }

                if (field.FieldType.DataType == DataType.LocaleString)
                {
                    serverLanguages ??= this.dataContext.GetAllLanguages();
                    field.Data = this.EnsureLocaleStringHasAllLanguages((LocaleString)field.Data, null, serverLanguages);
                }

                if (field.FieldType.Unique)
                {
                    if (this.dataContext.FieldValueAlreadyExistsForFieldType(field.FieldType.Id, field.Data))
                    {
                        context.Log(LogLevel.Warning, "Trying to add entity with unique field where the data already exists");
                        throw ErrorUtility.GetArgumentException("AddEntity", field.FieldType.Id, "Trying to add entity with unique field where the data already exists");
                    }
                }

                this.ValidateSearchHints(field, "AddEntity", "Trying to add entity with a unique search hint where the data already exists");

                if (field.FieldType.DataType == DataType.CVL)
                {
                    string keyData = field.Data.ToString();
                    List<string> persistedKeys = new List<string>();

                    if (string.IsNullOrEmpty(keyData))
                    {
                        continue;
                    }

                    List<string> keys = new List<string>();

                    if (field.FieldType.Multivalue)
                    {
                        foreach (string key in keyData.Split(new[] { ';' }, StringSplitOptions.RemoveEmptyEntries))
                        {
                            string trimmedKey = key.Trim();

                            string cacheKey = GetCVLValueCacheKey(field.FieldType.CVLId, trimmedKey);

                            CVLValue cachedKey;
                            if (!cachedKeys.TryGetValue(cacheKey, out cachedKey))
                            {
                                if (!keys.Exists(k => k.Equals(trimmedKey)))
                                {
                                    keys.Add(trimmedKey);
                                }
                            }
                            else
                            {
                                persistedKeys.Add(cachedKey.Key);
                            }
                        }
                    }
                    else
                    {
                        string cacheKey = GetCVLValueCacheKey(field.FieldType.CVLId, keyData);

                        CVLValue cachedKey;
                        if (!cachedKeys.TryGetValue(cacheKey, out cachedKey))
                        {
                            keys.Add(keyData);
                        }
                        else
                        {
                            field.Data = cachedKey.Key;
                        }
                    }

                    if (keys.Count == 0)
                    {
                        if (persistedKeys.Any())
                        {
                            field.Data = String.Join(";", persistedKeys);
                        }

                        continue;
                    }

                    // Make sure we validate cvl values and set the correct casing

                    CVL cvl = this.dataContext.GetCVL(field.FieldType.CVLId);

                    if (cvl.CustomValueList)
                    {
                        ExtensionManager extensionManager = new ExtensionManager(this.context);

                        foreach (string key in keys)
                        {
                            CVLValue customValue = extensionManager.GetCustomCVLValueByKey(cvl.Id, key, cvl);
                            if (customValue == null)
                            {
                                context.Log(LogLevel.Warning, "Trying to add entity with cvl key that does not exist");
                                throw ErrorUtility.GetArgumentException("AddEntity", field.FieldType.Id, "Trying to add entity with cvl key that does not exist");
                            }

                            if (field.FieldType.Multivalue)
                            {
                                persistedKeys.Add(customValue.Key);
                            }
                            else
                            {
                                field.Data = customValue.Key;
                            }
                        }
                    }
                    else
                    {
                        List<CVLValue> existingKeys = this.dataContext.GetExistingCVLKeysByKeyList(keys, cvl);

                        foreach (string key in keys)
                        {
                            CVLValue value = existingKeys.Find(x => x.CVLId == cvl.Id && x.Key.Equals(key, StringComparison.InvariantCultureIgnoreCase));

                            if (value == null)
                            {
                                context.Log(LogLevel.Warning, "Trying to add entity with cvl key (" + key + ") that does not exist");
                                throw ErrorUtility.GetArgumentException("AddEntity", field.FieldType.Id, "Trying to add entity with cvl key (" + key + ") that does not exist");
                            }

                            string cacheKey = GetCVLValueCacheKey(cvl.Id, value.Key);
                            if (!cachedKeys.ContainsKey(cacheKey))
                            {
                                cachedKeys.Add(cacheKey, value);
                            }

                            if (field.FieldType.Multivalue)
                            {
                                persistedKeys.Add(value.Key);
                            }
                            else
                            {
                                field.Data = value.Key;
                            }
                        }
                    }

                    if (persistedKeys.Any())
                    {
                        field.Data = String.Join(";", persistedKeys);
                    }
                }

                IllegalCharacters.CheckField(field, "AddEntity");
            }
        }

        private string GetCVLValueCacheKey(string cvlId, string key)
        {
            return $"{cvlId}|{key.ToLower()}";
        }

        private void ValidateUpdateEntityFields(Entity entity, Entity persistedEntity)
        {
            var expressionWrapper = new ExpressionWrapper(this.dataContext, this.context);
            if (!expressionWrapper.ParseExpressions(entity.Fields, out var errorPair))
            {
                throw ErrorUtility.GetArgumentFault(
                    "UpdateEntity",
                    errorPair.Key,
                    $"Trying to update entity (id={entity.Id}) with invalid expression for field {errorPair.Key} ({errorPair.Value})");
            }

            var cachedKeys = new Dictionary<string, CVLValue>();
            foreach (Field field in entity.Fields)
            {
                if (!Remoting.Util.Utility.ValueMatchesDataType(field.FieldType.DataType, field.Data, field.FieldType.ExpressionSupport))
                {
                    string msg = $"Trying to update entity (id={entity.Id}) with invalid value for field";
                    context.Log(LogLevel.Warning, msg);
                    throw ErrorUtility.GetArgumentException("UpdateEntity", field.FieldType.Id, msg);
                }

                if (Utility.StringIsInriverExpression(field.FieldType.ExpressionSupport, field.Data as string))
                {
                    continue;
                }

                if (!field.ValueHasBeenModified(persistedEntity.GetField(field.FieldType.Id).Data))
                {
                    continue;
                }

                if (!field.ValidateMandatory())
                {
                    string msg = $"Trying to update entity (id={entity.Id} with mandatory field not set";
                    context.Log(LogLevel.Warning, msg);
                    throw ErrorUtility.GetArgumentException("UpdateEntity", field.FieldType.Id, msg);
                }

                if (field.IsEmpty())
                {
                    continue;
                }

                if (field.FieldType.Unique)
                {
                    if (this.dataContext.FieldValueAlreadyExistsForFieldType(field.FieldType.Id, field.Data))
                    {
                        string msg = $"Trying to update entity (id={entity.Id}) with unique field where the data already exists";
                        context.Log(LogLevel.Warning, msg);
                        throw ErrorUtility.GetArgumentException("UpdateEntity", field.FieldType.Id, msg);
                    }
                }

                this.ValidateSearchHints(field, "UpdateEntity", $"Trying to update entity (id={entity.Id}) with unique search hint where the data already exists");

                // Only check for field.Revision if database model is not new data layer (NDL)
                if (field.Revision != 0 && (this.context.EntityModel != 1))
                {
                    if (persistedEntity.GetField(field.FieldType.Id).Revision != field.Revision)
                    {
                        var expectedRevision = persistedEntity.GetField(field.FieldType.Id).Revision;
                        string msg = $"Revision does not match stored revision on field with fieldTypeId={field.FieldType.Id} for entity={entity.Id}. Received field.Revision{ field.Revision} but expected {expectedRevision}";
                        context.Log(LogLevel.Warning, msg);
                        throw new InvalidOperationException(msg);
                    }
                }
                else
                {
                    field.Revision = persistedEntity.GetField(field.FieldType.Id).Revision;
                }

                if (field.FieldType.DataType == DataType.CVL)
                {
                    string keyData = (string)field.Data;
                    List<string> persistedKeys = new List<string>();

                    if (string.IsNullOrEmpty(keyData))
                    {
                        continue;
                    }

                    List<string> keys = new List<string>();

                    if (field.FieldType.Multivalue)
                    {
                        foreach (string key in keyData.Split(new[] { ';' }, StringSplitOptions.RemoveEmptyEntries))
                        {
                            string trimmedKey = key.Trim();

                            string cacheKey = GetCVLValueCacheKey(field.FieldType.CVLId, trimmedKey);

                            CVLValue cachedKey;
                            if (!cachedKeys.TryGetValue(cacheKey, out cachedKey))
                            {
                                if (!keys.Exists(k => k.Equals(trimmedKey)))
                                {
                                    keys.Add(trimmedKey);
                                }
                            }
                            else
                            {
                                persistedKeys.Add(cachedKey.Key);
                            }
                        }
                    }
                    else
                    {
                        string cacheKey = GetCVLValueCacheKey(field.FieldType.CVLId, keyData);

                        CVLValue cachedKey;
                        if (!cachedKeys.TryGetValue(cacheKey, out cachedKey))
                        {
                            keys.Add(keyData);
                        }
                        else
                        {
                            field.Data = cachedKey.Key;
                        }
                    }

                    if (keys.Count == 0)
                    {
                        if (persistedKeys.Any())
                        {
                            field.Data = String.Join(";", persistedKeys);
                        }

                        continue;
                    }

                    // Make sure we validate cvl values and set the correct casing
                    CVL cvl = this.dataContext.GetCVL(field.FieldType.CVLId);

                    if (cvl.CustomValueList)
                    {
                        ExtensionManager extensionManager = new ExtensionManager(this.context);

                        foreach (string key in keys)
                        {
                            CVLValue customValue = extensionManager.GetCustomCVLValueByKey(cvl.Id, key, cvl);
                            if (customValue == null)
                            {
                                string msg = $"Trying to update entity (id={entity.Id}) with custom cvl key that does not exist: {key}";
                                context.Log(LogLevel.Warning, msg);
                                throw ErrorUtility.GetArgumentException("UpdateEntity", field.FieldType.Id, msg);
                            }

                            if (field.FieldType.Multivalue)
                            {
                                persistedKeys.Add(customValue.Key);
                            }
                            else
                            {
                                field.Data = customValue.Key;
                            }
                        }
                    }
                    else
                    {
                        List<CVLValue> existingKeys = this.dataContext.GetExistingCVLKeysByKeyList(keys, cvl);

                        foreach (string key in keys)
                        {
                            CVLValue value = existingKeys.Find(x => x.CVLId == cvl.Id && x.Key.Equals(key, StringComparison.InvariantCultureIgnoreCase));

                            if (value == null)
                            {
                                string msg = $"Trying to update entity (id={entity.Id}) with cvl key that does not exist: {key}";
                                context.Log(LogLevel.Warning, msg);
                                throw ErrorUtility.GetArgumentException("UpdateEntity", field.FieldType.Id, msg);
                            }

                            string cacheKey = GetCVLValueCacheKey(cvl.Id, value.Key);
                            if (!cachedKeys.ContainsKey(cacheKey))
                            {
                                cachedKeys.Add(cacheKey, value);
                            }

                            if (field.FieldType.Multivalue)
                            {
                                persistedKeys.Add(value.Key);
                            }
                            else
                            {
                                field.Data = value.Key;
                            }
                        }
                    }

                    if (persistedKeys.Any())
                    {
                        field.Data = String.Join(";", persistedKeys);
                    }
                }
            }
        }

        private LocaleString EnsureLocaleStringHasAllLanguages(LocaleString fieldLocaleString, LocaleString persistedLocaleString, List<CultureInfo> serverLanguages)
        {
            fieldLocaleString ??= new LocaleString();

            // Remove non-server languages
            foreach (var ci in fieldLocaleString.Languages)
            {
                if (!serverLanguages.Contains(ci))
                {
                    fieldLocaleString.RemoveCulture(ci);
                }
            }

            // Ensure all server languages are present in the LocaleString
            foreach (var serverLanguage in serverLanguages)
            {
                if (!fieldLocaleString.ContainsCulture(serverLanguage))
                {
                    // For update: use persisted value if available, otherwise empty string
                    // For add: always use empty string
                    var valueToUse = string.Empty;
                    if (persistedLocaleString != null && !LocaleString.IsNullOrEmpty(persistedLocaleString) && persistedLocaleString[serverLanguage] != null)
                    {
                        valueToUse = persistedLocaleString[serverLanguage];
                    }

                    fieldLocaleString[serverLanguage] = valueToUse;
                }
            }

            return fieldLocaleString;
        }

        private void ValidateSearchHints(Field field, string methodName, string errorMessage)
        {
            var searchHintFieldTypes = this.dataContext.GetSearchHintFieldTypesByEntityId(field.FieldType.Id);
            if (searchHintFieldTypes.Any(x => x == field.FieldType.Id))
            {
                if (this.dataContext.FieldValueAlreadyExistsForFieldType(field.FieldType.Id, field.Data))
                {
                    this.context.Log(LogLevel.Warning, errorMessage);
                    throw ErrorUtility.GetArgumentException(methodName, field.FieldType.Id, errorMessage);
                }
            }
        }

        private Entity CallServerExtensions(string extensionEvent, Entity entity, Link link)
        {
            Stopwatch stopwatch = Stopwatch.StartNew();

            this.context.Log(LogLevel.Debug, "Calling entity extensions for event " + extensionEvent);

            CancelUpdateArgument arg = new CancelUpdateArgument();
            int serverExtensionCount = 0;

            try
            {
                ExtensionManager manager = new ExtensionManager(this.context);

                if (extensionEvent == ExtensionEvent.OnLink)
                {
                    link = manager.CallServerExtensionsForLink(link, extensionEvent, arg, out serverExtensionCount);
                }

                if (extensionEvent == ExtensionEvent.OnAdd)
                {
                    entity = manager.CallServerExtensionsForEntity(entity, extensionEvent, arg, out serverExtensionCount);
                }

                if (extensionEvent == ExtensionEvent.OnDelete)
                {
                    entity = manager.CallServerExtensionsForEntity(entity, extensionEvent, arg, out serverExtensionCount);
                }

                if (extensionEvent == ExtensionEvent.OnLinkUpdate)
                {
                    link = manager.CallServerExtensionsForLink(link, extensionEvent, arg, out serverExtensionCount);
                }

                if (extensionEvent == ExtensionEvent.OnUnlink)
                {
                    link = manager.CallServerExtensionsForLink(link, extensionEvent, arg, out serverExtensionCount);
                }

                if (extensionEvent == ExtensionEvent.OnUpdate)
                {
                    entity = manager.CallServerExtensionsForEntity(entity, extensionEvent, arg, out serverExtensionCount);
                }

                if (extensionEvent == ExtensionEvent.OnCreateVersion)
                {
                    entity = manager.CallServerExtensionsForEntity(entity, extensionEvent, arg, out serverExtensionCount);
                }

                if (extensionEvent == ExtensionEvent.OnLock)
                {
                    entity = manager.CallServerExtensionsForEntity(entity, extensionEvent, arg, out serverExtensionCount);
                }

                if (extensionEvent == ExtensionEvent.OnUnlock)
                {
                    entity = manager.CallServerExtensionsForEntity(entity, extensionEvent, arg, out serverExtensionCount);
                }
            }
            catch (Exception ex)
            {
                this.context.Log(LogLevel.Warning, "An unhandled error occurred when executing extension events " + ex);
            }

            stopwatch.Stop();

            if (serverExtensionCount > 0)
            {
                Serilog.Log.ForContext($"{extensionEvent}:CallServerExtensions", (int)stopwatch.Elapsed.TotalMilliseconds).Information($"Ran CallServerExtensionsForEntityEvent ({extensionEvent})");
            }
            this.context.Log(LogLevel.Verbose, "Extension execution time " + stopwatch.Elapsed.TotalMilliseconds.ToString("F02") + " ms");

            if (arg.Cancel)
            {
                if (string.IsNullOrEmpty(arg.Message))
                {
                    context.Log(LogLevel.Verbose, "Event was cancelled by the extension. No reason specified.");
                }
                else
                {
                    context.Log(LogLevel.Verbose, "Event was cancelled by the extension. " + arg.Message);
                }

                throw ErrorUtility.GetOperationCanceledException(arg.Message);
            }

            return entity;
        }

        private bool FieldTypeIsMultivalue(string fieldTypeId)
        {
            if (string.IsNullOrEmpty(fieldTypeId))
            {
                return false;
            }

            FieldType fieldType = this.dataContext.GetFieldType(fieldTypeId);

            if (fieldType == null)
            {
                return false;
            }

            return fieldType.Multivalue;
        }

        private bool FieldTypeIsCVL(string fieldTypeId)
        {
            if (string.IsNullOrEmpty(fieldTypeId))
            {
                return false;
            }

            FieldType fieldType = this.dataContext.GetFieldType(fieldTypeId);

            if (fieldType == null)
            {
                return false;
            }

            return !string.IsNullOrEmpty(fieldType.CVLId);
        }

        private void LinkSpecification(DtoLink link)
        {
            if (link?.Source?.EntityTypeId == null)
            {
                return;
            }

            if (link.Target?.EntityTypeId == null)
            {
                return;
            }

            if (link.Target.EntityTypeId == "Specification")
            {
                this.dataContext.LinkSpecification(link);
            }
        }

        private Entity CopyEntityValuesAndResetLinks(Entity original, Entity persistedEntity)
        {
            original.Id = persistedEntity.Id;
            original.ChangeSet = persistedEntity.ChangeSet;
            original.Completeness = persistedEntity.Completeness;
            original.CreatedBy = persistedEntity.CreatedBy;
            original.DateCreated = persistedEntity.DateCreated;
            original.DisplayDescription = persistedEntity.DisplayDescription;
            original.DisplayName = persistedEntity.DisplayName;
            original.EntityType = persistedEntity.EntityType;
            original.FieldSetId = persistedEntity.FieldSetId;
            original.Locked = persistedEntity.Locked;
            original.LastModified = persistedEntity.LastModified;
            original.MainPictureId = persistedEntity.MainPictureId;
            original.ModifiedBy = persistedEntity.ModifiedBy;
            original.Version = persistedEntity.Version;

            original.Links = new List<Link>();
            original.LoadLevel = LoadLevel.DataOnly;

            return original;
        }

        #endregion

        public Dictionary<int, List<int>> GetInboundParentsForEntitiesAndLinkType(List<int> entityIds, string linkTypeId, CancellationToken cancellationToken)
        {
            return this.dataContext.GetInboundParentsForEntitiesAndLinkType(entityIds, linkTypeId, cancellationToken);
        }

        public Dictionary<int, List<int>> GetOutboundChildrenForEntitiesAndLinkType(List<int> entityIds, string linkTypeId, CancellationToken cancellationToken)
        {
            return this.dataContext.GetOutboundChildrenForEntitiesAndLinkType(entityIds, linkTypeId, cancellationToken);
        }

        public bool EntityHasExpression(int entityId, string target, string targetType) => this.dataContext.EntityHasExpression(entityId, target, targetType);

        public List<int> GetEntityIdsForEntityTypeWithExpressions(string entityTypeId, string target, string targetType) => this.dataContext.GetEntityIdsForEntityTypeWithExpressions(entityTypeId, target, targetType);

        public DtoEntity SetEntityFieldSet(int entityId, string fieldSetId)
        {
            if (this.context.EntityModel == 2)
            {
                var result = ((IPMCServer3DLPersistanceAdapter)this.dataContext).SetEntityFieldSetFull(entityId, fieldSetId);
                return result;
            }

            if (!this.context.UserHasPermission(UserPermission.UpdateEntity) || !this.context.UserHasPermission(UserPermission.ChangeFieldSet))
            {
                throw ErrorUtility.GetSecurityException("User does not have permission to update entities in inRiver");
            }

            DtoEntity entity = this.dataContext.GetEntity(entityId);

            if (entity == null)
            {
                context.Log(LogLevel.Warning, "No entity exists with id " + entityId);
                throw ErrorUtility.GetArgumentFault("SetEntityFieldSet", "entityId", "No entity exists with id " + entityId);
            }

            if (string.IsNullOrWhiteSpace(fieldSetId))
            {
                context.Log(LogLevel.Warning, "No fieldSetId supplied");
                throw ErrorUtility.GetArgumentFault("SetEntityFieldSet", "fieldSetId", "No fieldSetId supplied");
            }

            var expressionWrapper = new ExpressionWrapper(this.dataContext, this.context);
            fieldSetId = expressionWrapper.EvaluateFieldSetIdExpression(entity, fieldSetId);

            FieldSet fieldSet = this.dataContext.GetFieldSet(fieldSetId);

            if (fieldSet == null)
            {
                context.Log(LogLevel.Warning, "No fieldset exists with id " + fieldSetId);
                throw ErrorUtility.GetArgumentFault("SetEntityFieldSet", "fieldSetId", "No fieldset exists with id " + fieldSetId);
            }

            if (fieldSet.Id.Equals(entity.FieldSetId))
            {
                return entity;
            }

            if (!entity.EntityTypeId.Equals(fieldSet.EntityTypeId, StringComparison.InvariantCultureIgnoreCase))
            {
                context.Log(LogLevel.Warning, "The supplied fieldset id belongs to other entity type than entity");
                throw ErrorUtility.GetArgumentFault("SetEntityFieldSet", "fieldSetId", "The supplied fieldset id belongs to other entity type than entity");
            }

            DtoEntity updateEntity = this.dataContext.SetEntityFieldSet(entityId, fieldSetId);

            updateEntity = new CompletenessRepository(this.context).SetEntityCompleteness(updateEntity);
            EventPublisher.NotifyFieldSetUpdated(this.context, entityId, entity.EntityTypeId, fieldSetId);

            new ChannelRepository(this.context).UpdateFieldsetForEntity(entityId, fieldSetId);

            return updateEntity;
        }

        public void SetSegmentExpressionForEntities(List<int> entityIds, string expression, HashSet<int> availableSegmentIds)
        {
            if (entityIds.Count == 0)
            {
                this.context.Log(LogLevel.Warning, "The Entity list for set segmentation does not contain an Entity");
                throw ErrorUtility.GetArgumentFault("SetEntityContentSegmentation", "Entity", "Trying to set segmentation for empty Entity List");
            }

            var entities = this.dataContext.GetEntities(entityIds);
            var previousSegmentIds = new Dictionary<int, int>();
            var segmentToEntities = new Dictionary<int, List<int>>();
            var expressionsToUpsert = new List<DtoExpression>();

            var expressionWrapper = new ExpressionWrapper(this.dataContext, this.context);
            foreach (var entity in entities)
            {
                previousSegmentIds.Add(entity.Id, entity.Segment.Id);

                var result = expressionWrapper.EvaluateExpression(
                    entity,
                    new DtoExpression
                    {
                        Data = expression,
                        EntityId = entity.Id,
                        Target = ExpressionTarget.SEGMENT,
                        TargetType = ExpressionTargetType.ENTITYMETADATA,
                    });

                if (result.Success)
                {
                    if (result.ExpressionResult != null && result.Expression.Status == true)
                    {
                        int? segmentId = null;
                        try
                        {
                            segmentId = Convert.ToInt32(result.ExpressionResult, CultureInfo.InvariantCulture);
                        }
                        catch { }

                        if (segmentId.HasValue && availableSegmentIds.Contains(segmentId.Value))
                        {
                            if (segmentToEntities.ContainsKey(segmentId.Value))
                            {
                                segmentToEntities[segmentId.Value].Add(entity.Id);
                            }
                            else
                            {
                                segmentToEntities.Add(segmentId.Value, new List<int> { entity.Id });
                            }
                        }
                        else
                        {
                            result.Expression.Status = false;
                            result.Expression.StatusMessage = $"Failed to set evaluated segment with id '{result.ExpressionResult}'";
                        }
                    }

                    expressionsToUpsert.Add(result.Expression);
                }
            }

            this.dataContext.UpsertExpressions(expressionsToUpsert);
            foreach (var key in segmentToEntities.Keys)
            {
                var status = this.dataContext.SetSegmentForEntities(segmentToEntities[key], key);

                foreach (var entityId in entityIds)
                {
                    if (key != previousSegmentIds[entityId])
                    {
                        EventPublisher.NotifySegmentationChanged(this.context, entityId, previousSegmentIds[entityId]);
                    }
                }
            }
        }

        public string GetFormattedValue(SpecificationFieldType specificationFieldType, int entityId)
            => this.GetFormattedValue(specificationFieldType, entityId, null);

        public string GetFormattedValue(SpecificationFieldType specificationFieldType, int entityId, CultureInfo language)
        {
            if (string.IsNullOrEmpty(specificationFieldType.Format))
            {
                return string.Empty;
            }

            var sb = new StringBuilder(specificationFieldType.Format);

            var previousBooleanValue = string.Empty;
            while (sb.ToString().IndexOf("<<", StringComparison.Ordinal) != -1)
            {
                try
                {
                    var startindex = sb.ToString().IndexOf("<<", StringComparison.Ordinal);
                    var endindex = sb.ToString().IndexOf(">>", startindex + 2, StringComparison.Ordinal);
                    if (endindex == -1)
                    {
                        continue;
                    }

                    var booleanString = sb.ToString().Substring(startindex + 2, endindex - startindex - 2);
                    var originalBooleanString = booleanString;
                    var fieldstart = booleanString.IndexOf("[[", StringComparison.Ordinal);
                    if (fieldstart == -1)
                    {
                        continue;
                    }

                    var fieldend = booleanString.IndexOf("]]", fieldstart + 2, StringComparison.Ordinal);
                    if (fieldend == -1)
                    {
                        continue;
                    }

                    var fieldId = booleanString.Substring(fieldstart + 2, fieldend - fieldstart - 2);
                    var specfield = this.GetSpecificationField(entityId, fieldId);

                    if (specfield.SpecificationFieldType.DataType == DataType.Boolean)
                    {
                        var data = (bool?)specfield.Data;

                        if (data.HasValue && data.Value)
                        {
                            var displayValue = specfield.SpecificationFieldType.Id;

                            var ci = language;

                            if (language == null)
                            {
                                ci = new CultureInfo(this.dataContext.GetServerSetting(ServerConstants.MASTER_LANGUAGE));
                            }

                            if (!string.IsNullOrEmpty(specfield.SpecificationFieldType.Name[ci]))
                            {
                                displayValue = specfield.SpecificationFieldType.Name[language];
                            }

                            booleanString = booleanString.Replace(string.Format("[[{0}]]", fieldId), displayValue);

                            sb = sb.Replace(string.Format("<<{0}>>", originalBooleanString), booleanString);
                            previousBooleanValue = booleanString;
                        }
                        else
                        {
                            sb = sb.Replace(string.Format("<<{0}>>", originalBooleanString), string.Empty);

                            // Extras, remove any unwanted characters...
                            var endSeparator = sb.ToString().IndexOf("<<[[", startindex, StringComparison.Ordinal);
                            if (endSeparator == -1)
                            {
                                endSeparator = sb.ToString().IndexOf("[[", startindex, StringComparison.Ordinal);
                            }
                            else
                            {
                                if (endSeparator > sb.ToString().IndexOf("[[", startindex, StringComparison.Ordinal))
                                {
                                    endSeparator = sb.ToString().IndexOf("[[", startindex, StringComparison.Ordinal);
                                }
                            }

                            if (endSeparator > -1)
                            {
                                sb.Remove(startindex, endSeparator - startindex);
                            }
                        }
                    }
                    else
                    {
                        sb = sb.Replace(string.Format("<<{0}>>", originalBooleanString), string.Empty);

                        // Extras, remove any unwanted characters...
                        var endSeparator = sb.ToString().IndexOf("<<[[", startindex, StringComparison.Ordinal);
                        if (endSeparator == -1)
                        {
                            endSeparator = sb.ToString().IndexOf("[[", startindex, StringComparison.Ordinal);
                        }
                        else
                        {
                            if (endSeparator > sb.ToString().IndexOf("[[", startindex, StringComparison.Ordinal))
                            {
                                endSeparator = sb.ToString().IndexOf("[[", startindex, StringComparison.Ordinal);
                            }
                        }

                        if (endSeparator > -1)
                        {
                            sb.Remove(startindex, endSeparator - startindex);
                        }
                    }
                }
                catch (Exception)
                {
                    break;
                }
            }

            if (!string.IsNullOrEmpty(previousBooleanValue))
            {
                // Last seperator has to be remove.
                var lastValueIndex = sb.ToString().IndexOf(previousBooleanValue, StringComparison.Ordinal);
                var endBufferIndex = lastValueIndex + previousBooleanValue.Count();
                var existingDataIndex = sb.ToString().IndexOf("[[", lastValueIndex, StringComparison.Ordinal);
                if (endBufferIndex > existingDataIndex)
                {
                    sb.Remove(endBufferIndex, sb.Length - endBufferIndex);
                }
            }

            var previousOtherValue = string.Empty;
            var hasNullValue = false;
            while (sb.ToString().IndexOf("[[", StringComparison.Ordinal) != -1)
            {
                try
                {
                    var startindex = sb.ToString().IndexOf("[[", StringComparison.Ordinal);
                    var endindex = sb.ToString().IndexOf("]]", startindex + 2, StringComparison.Ordinal);
                    if (endindex == -1)
                    {
                        continue;
                    }

                    var fieldId = sb.ToString().Substring(startindex + 2, endindex - startindex - 2);
                    var specfield = this.GetSpecificationField(entityId, fieldId);

                    var dataString = string.Empty;
                    if (specfield == null)
                    {
                        dataString = string.Empty;
                        hasNullValue = true;
                    }

                    if (specfield.Data == null)
                    {
                        hasNullValue = true;
                    }

                    if (specfield.SpecificationFieldType.DataType == DataType.Boolean)
                    {
                        var data = (bool?)specfield.Data;

                        if (data.HasValue && data.Value)
                        {
                            dataString = "true";
                        }
                        else
                        {
                            dataString = "false";
                        }
                    }
                    else if (specfield.SpecificationFieldType.DataType == DataType.String)
                    {
                        dataString = (string)specfield.Data;
                    }
                    else if (specfield.SpecificationFieldType.DataType == DataType.Integer)
                    {
                        if (specfield.Data != null)
                        {
                            dataString = ((int)specfield.Data).ToString(CultureInfo.InvariantCulture);
                        }
                    }
                    else if (specfield.SpecificationFieldType.DataType == DataType.LocaleString)
                    {
                        var locale = (LocaleString)specfield.Data;
                        if (language != null)
                        {
                            dataString = locale[language];
                        }
                        else
                        {
                            dataString = locale.ToString();
                        }
                    }
                    else if (specfield.SpecificationFieldType.DataType == DataType.CVL)
                    {
                        if (specfield.Data == null)
                        {
                            sb = sb.Replace(string.Format("[[{0}]]", fieldId), dataString);
                            previousOtherValue = dataString;
                            continue;
                        }

                        var cvlKey = specfield.Data as string;
                        var cvlValue = this.dataContext.GetCVLValueByKey(cvlKey, specfield.SpecificationFieldType.CVLId);
                        if (language != null && cvlValue.Value is LocaleString)
                        {
                            dataString = (cvlValue.Value as LocaleString)[language];
                        }
                        else
                        {
                            dataString = cvlValue.Value.ToString();
                        }
                    }
                    else if (specfield.SpecificationFieldType.DataType == DataType.Double)
                    {
                        if (specfield.Data != null)
                        {
                            dataString = ((double)specfield.Data).ToString(CultureInfo.InvariantCulture);
                        }
                    }
                    else if (specfield.SpecificationFieldType.DataType == DataType.DateTime)
                    {
                        if (specfield.Data != null)
                        {
                            dataString = ((DateTime)specfield.Data).ToString(CultureInfo.InvariantCulture);
                        }
                    }

                    sb = sb.Replace(string.Format("[[{0}]]", fieldId), dataString);
                    previousOtherValue = dataString;
                }
                catch (Exception)
                {
                    break;
                }
            }

            if (hasNullValue)
            {
                if (sb != null && !string.IsNullOrEmpty(previousOtherValue)
                    && (string.IsNullOrEmpty(previousBooleanValue)
                        || sb.ToString().IndexOf(previousBooleanValue, StringComparison.Ordinal)
                        < sb.ToString().IndexOf(previousOtherValue, StringComparison.Ordinal)))
                {
                    // Last seperator has to be remove.
                    var lastValueIndex = sb.ToString().IndexOf(previousOtherValue, StringComparison.Ordinal);
                    var endBufferIndex = lastValueIndex + previousOtherValue.Count();
                    sb.Remove(endBufferIndex, sb.Length - endBufferIndex);
                }
            }

            var result = sb.ToString();
            return result;
        }

        public SpecificationField GetSpecificationField(int entityId, string specificationFieldTypeId)
        {
            if (string.IsNullOrEmpty(specificationFieldTypeId))
            {
                return null;
            }

            return this.dataContext.GetSpecificationField(entityId, specificationFieldTypeId);
        }

        public List<SpecificationField> GetSpecificationFieldsForEntity(int entityId) => this.dataContext.GetSpecificationFieldsForEntity(entityId);

        public Comment AddCommentWithResult(Comment comment)
        {
            if (!this.context.UserHasPermission(UserPermission.AddComments))
            {
                throw ErrorUtility.GetSecurityFault("User does not have permission to add entity comments to inRiver");
            }

            if (!string.IsNullOrEmpty(comment.Author))
            {
                if (comment.Author.Length > 128)
                {
                    this.context.Log(LogLevel.Warning, "Length for author cannot exceed 128 characters");
                    throw ErrorUtility.GetArgumentFault("AddComment", "comment.Author", "Length for author cannot exceed 128 characters");
                }
            }

            var entity = this.dataContext.GetEntity(comment.EntityId);
            if (entity == null)
            {
                this.context.Log(LogLevel.Warning, "Trying to add comment for entity that does not exist");
                throw ErrorUtility.GetArgumentFault("AddComment", "comment.EntityId", "Trying to add comment for entity that does not exist");
            }

            if (string.IsNullOrEmpty(comment.Text))
            {
                return new Comment();
            }

            comment = this.dataContext.AddComment(comment);

            EventPublisher.NotifyEntityCommentAdded(this.context, comment.EntityId, entity.EntityTypeId, comment.Id);

            return comment;
        }

        public DtoEntity UpdateFieldsForEntity(List<Field> fields)
        {
            if (!this.context.UserHasPermission(UserPermission.UpdateEntity))
            {
                throw ErrorUtility.GetSecurityFault("User does not have permission to update entities in inRiver");
            }

            if (fields == null)
            {
                this.context.Log(LogLevel.Warning, "Fields collection cannot be null");
                throw ErrorUtility.GetArgumentFault("UpdateFieldsForEntity", "fields", "Fields collection cannot be null");
            }

            if (fields.Count == 0)
            {
                this.context.Log(LogLevel.Warning, "Fields collection cannot be empty");
                throw ErrorUtility.GetArgumentFault("UpdateFieldsForEntity", "fields", "Fields collection cannot be empty");
            }

            var entityId = fields[0].EntityId;

            if (!this.dataContext.EntityExists(entityId))
            {
                this.context.Log(LogLevel.Warning, "Entity for Fields does not exist");
                throw ErrorUtility.GetArgumentFault("UpdateFieldsForEntity", "EntityId", "Entity for Fields does not exist");
            }

            foreach (var field in fields)
            {
                if (field.FieldType == null)
                {
                    this.context.Log(LogLevel.Warning, "Trying to update field with FieldType missing");
                    throw ErrorUtility.GetArgumentFault("UpdateFieldsForEntity", "FieldType", "Trying to update field with FieldType missing");
                }

                if (string.IsNullOrWhiteSpace(field.FieldType.Id))
                {
                    this.context.Log(LogLevel.Warning, "Trying to update field with FieldTypeId missing");
                    throw ErrorUtility.GetArgumentFault("UpdateFieldsForEntity", "FieldTypeId", "Trying to update field with FieldTypeId missing");
                }

                if (field.EntityId != entityId)
                {
                    this.context.Log(LogLevel.Warning, "Trying to update fields from different entities");
                    throw ErrorUtility.GetArgumentFault("UpdateFieldsForEntity", "EntityId", "Trying to update fields from different entities");
                }
            }

            var entity = new Entity
            {
                Id = entityId,
                Fields = fields,
                EntityType = null,
                LoadLevel = LoadLevel.DataOnly
            };

            var result = this.UpdateEntity(entity);
            return result;
        }
    }
}
