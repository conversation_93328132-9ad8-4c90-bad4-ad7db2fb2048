namespace LongRunningJobQueueService.AzureQueue.Services
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading;
    using System.Threading.Tasks;
    using Azure.Storage.Queues;
    using Azure.Storage.Queues.Models;
    using Interfaces;
    using Microsoft.Extensions.Logging;

    public class JobStatusQueueService : IJobStatusQueueService
    {
        private readonly QueueClient queueClient;
        private readonly ILogger<JobStatusQueueService> logger;

        public JobStatusQueueService(QueueClient queueClient, ILogger<JobStatusQueueService> logger)
        {
            this.queueClient = queueClient;
            this.logger = logger;
        }

        public async Task<bool> ExistsAsync(CancellationToken cancellationToken) => await this.queueClient.ExistsAsync(cancellationToken);

        public async Task<IEnumerable<QueueMessage>> ReceiveMessagesAsync(int maxMessages, TimeSpan visibilityTimeout, CancellationToken cancellationToken)
            => (await this.queueClient.ReceiveMessagesAsync(maxMessages, visibilityTimeout, cancellationToken))?.Value ?? Enumerable.Empty<QueueMessage>();

        public async Task DeleteMessageAsync(string messageId, string popReceipt,
            CancellationToken cancellationToken)
        {
            try
            {
                await this.queueClient.DeleteMessageAsync(messageId, popReceipt, cancellationToken);
            }
            catch (Exception ex)
            {
                this.logger.LogError(ex, "Failed to delete message: {MessageId}", messageId);
            }
        }
    }
}
