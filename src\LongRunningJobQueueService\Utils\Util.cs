namespace LongRunningJobQueueService.Utils
{
    using System.Fabric;

    public class Util
    {
        public static string StackConfigSecretName => GetConfigParameter("StackConfigSecretName");

        public static string KeyVaultBaseUrl => GetConfigParameter("KeyVaultBaseUrl");

        public static string InstrumentationKey => GetConfigParameter("InstrumentationKey");
        
        public static string LogLevel => GetConfigParameter("LogLevel");

        private static string GetConfigParameter(string input)
        {
            var configurationPackage = FabricRuntime.GetActivationContext().GetConfigurationPackageObject("Config");
            var parameter = configurationPackage.Settings.Sections["Configuration"].Parameters[input];

            return parameter.Value;
        }
    }
}
