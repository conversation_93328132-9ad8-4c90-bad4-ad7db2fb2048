<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netstandard2.1</TargetFramework>
    <Platforms>x64</Platforms>
    <Configurations>Debug;Release</Configurations>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Identity" Version="1.10.4" />
    <PackageReference Include="Azure.Security.KeyVault.Keys" Version="4.2.0" />
    <PackageReference Include="Dapper" Version="2.1.35" />
    <PackageReference Include="inRiver.Api.Data.Client" Version="2.12.0" />
    <PackageReference Include="inRiver.Log" Version="2.1.1" />
    <PackageReference Include="inRiver.StackEssentials" Version="6.0.0" />
    <PackageReference Include="Microsoft.AspNet.WebApi.Client" Version="5.2.4" />
    <PackageReference Include="Microsoft.CodeAnalysis.NetAnalyzers" Version="5.0.3">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="2.1.0" />
    <PackageReference Include="Microsoft.Extensions.Http" Version="6.0.0" />
    <PackageReference Include="Newtonsoft.Json" Version="12.0.2" />
    <PackageReference Include="Polly" Version="7.2.3" />
    <PackageReference Include="Serilog" Version="2.8.0" />
    <PackageReference Include="StyleCop.Analyzers" Version="1.2.0-beta.376">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="System.ComponentModel.Annotations" Version="5.0.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\inRiver.iPMC.Persistance\inRiver.iPMC.Persistance.csproj" />
    <ProjectReference Include="..\inRiver.Validation\inRiver.Validation.csproj" />
    <ProjectReference Include="..\Telemetry\Telemetry.csproj" />
  </ItemGroup>
</Project>
