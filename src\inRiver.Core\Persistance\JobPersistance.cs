namespace inRiver.Core.Persistance
{
    using System;
    using System.Data.SqlClient;
    using System.Linq;
    using System.Threading.Tasks;
    using Constants.LongRunningJobs;
    using Dapper;
    using inRiver.Core.Models.inRiver;

    public partial class inRiverPersistance
    {
        public void UpdateLongRunningJobState(int id, string state)
        {
            using (var connection = new SqlConnection(this.connectionString))
            {
                try
                {
                    _ = connection.Execute(
                        @"UPDATE [LongRunningJob]
                           SET [State] = @State, [DateModified] = GETUTCDATE()
                           WHERE Id = @Id",
                        new { Id = id, State = state });
                }
                catch (Exception exception)
                {
                    this.LogHelper.UnexpectedError(exception);
                    throw;
                }
            }
        }

        public void UpdateLongRunningJobStateAndMetadata(int id, string state, string metadata)
        {
            using (var connection = new SqlConnection(this.connectionString))
            {
                try
                {
                    _ = connection.Execute(
                        @"UPDATE [LongRunningJob]
                           SET [State] = @State, [Metadata] = @Metadata, [DateModified] = GETUTCDATE()
                           WHERE Id = @Id",
                        new { Id = id, State = state, Metadata = metadata });
                }
                catch (Exception exception)
                {
                    this.LogHelper.UnexpectedError(exception);
                    throw;
                }
            }
        }

        public async Task UpdateLongRunningJobMetadataAsync(int id, string metadata)
        {
            using var connection = new SqlConnection(this.connectionString);
            try
            {
                _ = await connection.ExecuteAsync(
                    "UPDATE LongRunningJob SET [Metadata] = @Metadata, DateModified = GETUTCDATE() WHERE Id = @Id",
                    new { Id = id, Metadata = metadata });
            }
            catch (Exception exception)
            {
                this.LogHelper.UnexpectedError(exception);
                throw;
            }
        }

        public async Task UpdateLongRunningJobPercentCompletedAsync(int jobId, int percentCompleted)
        {
            using (var connection = new SqlConnection(this.connectionString))
            {
                try
                {
                    _ = await connection.ExecuteAsync(
                        @"UPDATE [LongRunningJob]
                           SET [PercentCompleted] = @PercentCompleted, [DateModified] = GETUTCDATE()
                           WHERE Id = @Id",
                        new { Id = jobId, PercentCompleted = percentCompleted });
                }
                catch (Exception exception)
                {
                    this.LogHelper.UnexpectedError(exception);
                    throw;
                }
            }
        }

        public int InsertLongRunningJob(LongRunningJob job)
        {
            using (var connection = new SqlConnection(this.connectionString))
            {
                try
                {
                    if (job.IdentifierType is null)
                    {
                        //We separate this code because it might be that identifierType has not been added to all DBs.
                        //We'll consolidate the code once we know that the identifierType column is present in all DBs in all stacks.
                        job.Id = connection.Query<int>(@"DECLARE @UserId INT = (SELECT Id FROM dbo.[User] WHERE Username = @StartedBy) 
                        INSERT INTO LongRunningJob ([JobType], [State], [Date], [PercentCompleted], [Identifier], [Metadata], [Scope], [UserId], [DateModified]) 
                        VALUES (@JobType, @State, GETUTCDATE(), NULL, @Identifier, @Metadata, @Scope, @UserId, NULL) 
                        SELECT CAST(SCOPE_IDENTITY() as int)",
                            new { JobType = job.JobType, State = job.State, Identifier = job.Identifier, Metadata = job.Metadata, Scope = job.Scope, StartedBy = job.StartedBy }).Single();

                        return job.Id;
                    }
                    else
                    {
                        job.Id = connection.Query<int>(
                            @"DECLARE @UserId INT = (SELECT Id FROM dbo.[User] WHERE Username = @StartedBy) 
                        INSERT INTO LongRunningJob ([JobType], [State], [Date], [PercentCompleted], [Identifier], [IdentifierType], [Metadata], [Scope], [UserId], [DateModified]) 
                        VALUES (@JobType, @State, GETUTCDATE(), NULL, @Identifier, @IdentifierType, @Metadata, @Scope, @UserId, NULL) 
                        SELECT CAST(SCOPE_IDENTITY() as int)",
                            new
                            {
                                JobType = job.JobType, State = job.State, Identifier = job.Identifier,
                                IdentifierType = job.IdentifierType, Metadata = job.Metadata, Scope = job.Scope,
                                StartedBy = job.StartedBy
                            }).Single();

                        return job.Id;
                    }
                }
                catch (Exception exception)
                {
                    LogHelper.UnexpectedError(exception);
                    throw;
                }
            }
        }

        public bool StartedJobExists(string jobType, string identifier = null, string identifierType = null)
        {
            using (var connection = new SqlConnection(this.connectionString))
            {
                try
                {
                    if (identifierType is null)
                    {
                        //We separate this code because it might be that identifierType has not been added to all DBs.
                        //We'll consolidate the code once we know that the identifierType column is present in all DBs in all stacks.
                        var result = connection.Query<int>(@"SELECT 1                       
                        FROM LongRunningJob 
                        WHERE JobType = @JobType 
                        AND (Identifier = @Identifier OR @Identifier IS NULL)
                        AND ([State] = 'Running' OR [State] = 'Queued')",
                            new { JobType = jobType, Identifier = identifier });
                        return result.FirstOrDefault() == 1;
                    }
                    else
                    {
                        var result = connection.Query<int>(@"SELECT 1                       
                        FROM LongRunningJob 
                        WHERE JobType = @JobType 
                        AND (Identifier = @Identifier OR @Identifier IS NULL)
                        AND (IdentifierType = @IdentifierType OR @IdentifierType IS NULL)
                        AND ([State] = 'Running' OR [State] = 'Queued')",
                            new { JobType = jobType, Identifier = identifier, IdentifierType = identifierType });
                        return result.FirstOrDefault() == 1;
                    }
                }
                catch (Exception exception)
                {
                    LogHelper.UnexpectedError(exception);
                    throw;
                }
            }
        }

        public bool JobIsCancelled(int jobId)
        {
            using var connection = new SqlConnection(this.ConnectionString);
            var result = connection.Query<int>(
                "SELECT 1 FROM LongRunningJob WHERE Id = @JobId AND [State] = @State",
                new {
                    JobId = jobId,
                    State = LongRunningJobsStatus.Cancelled
                });
            return result.FirstOrDefault() == 1;
        }
    }
}
