namespace LongRunningJobQueueService.Data.Services
{
    using System.Threading.Tasks;
    using Exceptions;
    using Repositories;

    public class LongRunningJobService : ILongRunningJobService
    {
        private readonly ILongRunningJobRepository longRunningJobRepository;
        private readonly IEnvironmentService environmentService;

        public LongRunningJobService(ILongRunningJobRepository longRunningJobRepository, IEnvironmentService environmentService)
        {
            this.longRunningJobRepository = longRunningJobRepository;
            this.environmentService = environmentService;
        }

        public async Task UpdateJobStatusAsync(string customerSafeName, string environmentSaveName, int jobId, string status)
        {
            var environmentConfiguration = await this.environmentService.GetEnvironmentConfigurationAsync(customerSafeName, environmentSaveName);
            if (environmentConfiguration == null)
            {
                throw new EnvironmentConfigurationNotFoundException(customerSafeName, environmentSaveName);
            }

            await this.longRunningJobRepository.UpdateJobStatusAsync(environmentConfiguration.DbConnectionString, jobId, status);
        }
    }
}
