namespace LongRunningJobQueueService.Data.Services
{
    using System.Collections.Generic;
    using System.Threading.Tasks;
    using Models;
    using Repositories;

    public class EnvironmentService : IEnvironmentService
    {
        private readonly IDictionary<string, EnvironmentConfiguration> cachedEnvironmentConfigurations = new Dictionary<string, EnvironmentConfiguration>();
        private readonly IEnvironmentConfigurationRepository environmentConfigurationRepository;

        public EnvironmentService(IEnvironmentConfigurationRepository environmentConfigurationRepository)
        {
            this.environmentConfigurationRepository = environmentConfigurationRepository;
        }

        public async Task<EnvironmentConfiguration> GetEnvironmentConfigurationAsync(string customerSafeName, string environmentSaveName)
        {
            if (this.cachedEnvironmentConfigurations.TryGetValue(GetCacheKey(customerSafeName, environmentSaveName), out var cachedEnvironmentConfiguration))
            {
                return cachedEnvironmentConfiguration;
            }

            var environmentConfiguration = await this.environmentConfigurationRepository.GetEnvironmentConfigurationAsync(customerSafeName, environmentSaveName);
            if (environmentConfiguration != null)
            {
                this.cachedEnvironmentConfigurations[GetCacheKey(customerSafeName, environmentSaveName)] = environmentConfiguration;
            }

            return environmentConfiguration;
        }

        private static string GetCacheKey(string customerSafeName, string environmentSaveName) => $"{customerSafeName}-{environmentSaveName}";
    }
}
