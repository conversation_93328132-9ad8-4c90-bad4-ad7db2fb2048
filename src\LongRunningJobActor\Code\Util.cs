namespace LongRunningJobActor.Code
{
    using System;
    using System.Fabric;
    using System.Threading;
    using System.Threading.Tasks;
    using Microsoft.ServiceFabric.Services.Client;
    using Newtonsoft.Json.Linq;

    public static class Util
    {
        public static string StackConfigSecretName => GetConfigParameter("StackConfigSecretName");

        public static string KeyVaultBaseUrl => GetConfigParameter("KeyVaultBaseUrl");

        public static string DataApiUrl => GetConfigParameter("DataApiUrl");

        public static string DataJobServiceUrl => GetConfigParameter("DataJobServiceUrl");

        public static string ExpressionWorkerServiceUrl => GetConfigParameter("ExpressionWorkerServiceUrl");

        public static string MessagingServiceUrl => GetConfigParameter("MessagingServiceUrl");

        public static string GetAuth0Domain => GetConfigParameter("Auth0Domain");

        public static string GetOutputAdapterApiBaseAddress => GetConfigParameter("OutputAdapter:ApiBaseAddress");

        public static string GetInstrumentationKey() => GetConfigParameter("InstrumentationKey");

        public static string InspireBackendUrl => GetConfigParameter("InspireBackendUrl");

        private static string GetConfigParameter(string input)
        {
            var configurationPackage = FabricRuntime.GetActivationContext().GetConfigurationPackageObject("Config");
            var parameter = configurationPackage.Settings.Sections["Configuration"].Parameters[input];
            return parameter.Value;
        }

        public static string CheckIfFabricUrlAndParse(string serviceUrl)
        {
            if (serviceUrl.Contains("+"))
            {
                serviceUrl = serviceUrl.Replace("+", FabricRuntime.GetNodeContext().IPAddressOrFQDN);
            }

            return serviceUrl;
        }

        internal static string TokenServiceAddress() => GetConfigParameter("TokenServiceAddress");

        internal static string SMTPSendUser() => GetConfigParameter("SMTP_SEND_USER");

        internal static string SMTPSendUserName() => GetConfigParameter("SMTP_SEND_USER_NAME");

        internal static string GetKeyVaultServerSettingsEncryptionKeyName() => GetConfigParameter("KeyVaultServerSettingsEncryptionKeyName");

        internal static bool GetRequiredHttps()
        {
            var parameter = GetConfigParameter("RequiredHttps");
            if (!bool.TryParse(parameter, out var result))
            {
                result = true;
            }

            return result;
        }

        internal static string GetChannelServiceUrl() =>
            GetConfigParameter("ChannelServiceUrl");

        internal static string GetConnectServiceUrl() =>
            GetConfigParameter("ConnectServiceUrl");

        internal static string GetAugmentaBaseUrl() =>
            GetConfigParameter("Augmenta:ApiBaseAddress");

        internal static string GetOAuthAudience() =>
            GetConfigParameter("OAuth:Audience");

        public static string GetCompressionServiceUrl()
            => GetConfigParameter("CompressionServiceUrl");

        public static string GetStackGroup()
            => GetConfigParameter("StackGroup");

        public static string GetLogLevel()
        {
            return GetConfigParameter("LogLevel");
        }
    }
}
