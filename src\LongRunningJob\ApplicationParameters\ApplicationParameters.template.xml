<?xml version="1.0" encoding="utf-8"?>
<Application xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" Name="fabric:/LongRunningJob" xmlns="http://schemas.microsoft.com/2011/01/fabric">
  <Parameters>
    <!-- Urls -->
    <Parameter Name="BackOfficeUrl" Value="[[Shared.InternalUrl.BackOffice]]" />
    <Parameter Name="BaseServerAddress" Value="[[Shared.InternalUrl.RemotingApi]]" />
    <Parameter Name="KeyVaultBaseUrl" Value="[[Shared.KeyVault.BaseUrl]]" />
    <Parameter Name="PublicAssetServiceUrl" Value="[[Shared.PublicUrl.AssetService]]" />
    <Parameter Name="DataApiUrl" Value="[[Shared.InternalUrlFromFabricService.DataApiService]]/" />
    <Parameter Name="DataJobServiceUrl" Value="[[Shared.InternalUrlFromFabricService.DataJobService]]/" />
    <Parameter Name="InspireBackendUrl" Value="[[Shared.InternalUrl.InspireBackendUrl]]" />
    <Parameter Name="MessagingServiceUrl" Value="[[Shared.InternalUrlFromFabricService.MessagingService]]/" />
    <Parameter Name="AugmentaApiBaseAddress" Value="[[Shared.AugmentaApiBaseAddress]]" />
    <Parameter Name="OutputAdapterApiBaseAddress" Value="[[Shared.OutputAdapterApiBaseAddress]]" />
    <Parameter Name="Auth0Domain" Value="[[SF.Token.Auth0Domain]]" />
    
    <!-- Service fabric configuration -->
    <Parameter Name="LongRunningJobActorService_MinReplicaSetSize" Value="[[SF.LongRunningJob.Actor.MinReplicaSetSize]]" />
    <Parameter Name="LongRunningJobActorService_PartitionCount" Value="[[SF.LongRunningJob.LongRunningJobActorService.PartitionCount]]" />
    <Parameter Name="LongRunningJobActorService_TargetReplicaSetSize" Value="[[SF.LongRunningJob.LongRunningJobActorService.TargetReplicaSetSize]]" />
    <Parameter Name="LongRunningJobWorkerService_CpuCores" Value="[[SF.LongRunning.LongRunningJobWorkerService.CpuCores]]" />
    <Parameter Name="LongRunningJobService_InstanceCount" Value="[[SF.LongRunning.LongRunningJobService.InstanceCount]]" />
    <Parameter Name="ServicePlacementConstraints" Value="[[Shared.ServicePlacementConstraints.Primary]]" />
    <Parameter Name="ServicePlacementConstraintsNT3" Value="[[Shared.ServicePlacementConstraints.Worker]]" />
    <Parameter Name="LongRunningJobWorkerService_ServicePlacementConstraints" Value="[[Shared.ServicePlacementConstraints.Worker]]"/>

    <!-- Other -->
    <Parameter Name="StackConfigSecretName" Value="[[Shared.KeyVault.Secret.StackConfig]]" />
    <Parameter Name="SMTP_SEND_USER" Value="[[Shared.SendGrid.FromEmail]]" />
    <Parameter Name="SMTP_SEND_USER_NAME" Value="[[Shared.SendGrid.FromName]]" />
    <Parameter Name="ServiceLocation" Value="[[Shared.StackNameShort]]" />
    <Parameter Name="InstrumentationKey" Value="[[Shared.Key.Instrumentation]]" />
    <Parameter Name="GeoLocation" Value="[[Shared.StackNameShort]]" />
    <Parameter Name="TimerIntervallInMinutes" Value="[[Sf.LongRunningJob.TimerIntervallInMinutes]]" />
    <Parameter Name="PeriodInHours" Value="[[Sf.LongRunningJob.PeriodInHours]]" />
    <Parameter Name="Stack" Value="[[Shared.Stack]]" />
    <Parameter Name="StackGroup" Value="[[Shared.StackGroup]]" />
    <Parameter Name="LogLevel" Value="[[Shared.Defaults.SystemLogLevel]]" />
    <Parameter Name="KeyVaultServerSettingsEncryptionKeyName" Value="[[Shared.KeyVault.Key.ServerSettingsEncryptionKeyName]]" />
    <Parameter Name="InriverApiOAuthAudience" Value="[[Shared.InriverApiOAuthAudience]]" />
  </Parameters>
</Application>
