<?xml version="1.0" encoding="utf-8"?>
<Application xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" Name="fabric:/LongRunningJob" xmlns="http://schemas.microsoft.com/2011/01/fabric">
  <Parameters>
    <Parameter Name="ASPNETCORE_ENVIRONMENT" Value="Development" />
    <Parameter Name="KeyVaultBaseUrl" Value="https://pmcuselocalkv01.vault.azure.net/" />
    <Parameter Name="KeyVaultServerSettingsEncryptionKeyName" Value="serversettings-encryption-key" />
    <Parameter Name="StackConfigSecretName" Value="LocalStackConfig" />
    <Parameter Name="RequiredHttps" Value="false" />
    <Parameter Name="DataApiUrl" Value="http://localhost:8485/" />
    <Parameter Name="SMTP_SEND_USER" Value="<EMAIL>" />
    <Parameter Name="SMTP_SEND_USER_NAME" Value="inRiver" />
    <Parameter Name="ServicePlacementConstraints" Value="" />
    <Parameter Name="ServicePlacementConstraintsNT3" Value="" />
    <Parameter Name="InstrumentationKey" Value="728f45b3-246b-4a76-8f95-11f8325b01a2" />
    <Parameter Name="GeoLocation" Value="localhost" />
    <Parameter Name="Stack" Value="localhost" />
    <Parameter Name="LongRunningJobActorService_PartitionCount" Value="1" />
    <Parameter Name="LongRunningJobActorService_TargetReplicaSetSize" Value="1" />
    <Parameter Name="LongRunningJobActorService_MinReplicaSetSize" Value="1" />
    <Parameter Name="LongRunningJobService_InstanceCount" Value="1" />
    <Parameter Name="AzureServicesAuthConnectionString" Value="RunAs=App;AppId=ec7e9f6c-a6eb-4b0d-b2b8-956f85ce71d5;TenantId=5c732d90-72ba-4e70-9173-a254edfcd1fb;AppKey=****************************************" />
    <Parameter Name="AZURE_CLIENT_ID" Value="ec7e9f6c-a6eb-4b0d-b2b8-956f85ce71d5" />
    <Parameter Name="AZURE_TENANT_ID" Value="5c732d90-72ba-4e70-9173-a254edfcd1fb" />
    <Parameter Name="AZURE_CLIENT_SECRET" Value="****************************************" />
    <Parameter Name="LogLevel" Value="Verbose" />
    <Parameter Name="LongRunningJobWorkerService_ServicePlacementConstraints" Value="" />
  </Parameters>
</Application>
