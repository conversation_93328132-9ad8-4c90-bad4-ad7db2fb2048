namespace inRiver.Core.Models.inRiver.Workflow
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations;
    using System.Text;
    using Newtonsoft.Json;

    public class ProductDataSchema
    {
        [JsonProperty("type")]
        public string Type { get; set; } = string.Empty;

        [JsonProperty("group")]
        public string Group { get; set; } = string.Empty;

        [JsonProperty("field")]
        public string Field { get; set; } = string.Empty;
    }

    public class LinkedEntityDataSchema
    {
        [JsonProperty("link")]
        public string Link { get; set; } = string.Empty;

        [JsonProperty("single_entity_data")]
        public bool SingleEntityData { get; set; }

        [JsonProperty("fields")]
        public List<ProductDataSchema> Fields { get; set; } = new List<ProductDataSchema>();
    }

    public class LanguageServiceProvider
    {
        [JsonProperty("language")]
        public string Language { get; set; } = string.Empty;

        [JsonProperty("serviceProvider")]
        public string ServiceProvider { get; set; }
    }

    public class InputFieldV3
    {
        [JsonProperty("field")]
        public string? Field { get; set; }

        [JsonProperty("value")]
        public object? Value { get; set; }
    }

    public class MediaSettingConfig
    {
        [JsonProperty("setting")]
        public string Setting { get; set; } = string.Empty;

        [JsonProperty("size")]
        public string Size { get; set; } = string.Empty;

        [JsonProperty("advanceSettingMode")]
        public string? AdvanceSettingMode { get; set; }

        [JsonProperty("count")]
        public int? Count { get; set; }

        [JsonProperty("resourceLink")]
        public string? ResourceLink { get; set; }

        [JsonProperty("imageUrlField")]
        public object? ImageUrlField { get; set; }

        [JsonProperty("filterField")]
        public InputFieldV3 FilterField { get; set; } = new InputFieldV3();

        [JsonProperty("isCSVField")]
        public bool? IsCSVField { get; set; }
    }

    public class SubConfigCriteria
    {
        [JsonProperty("fieldId")]
        public string FieldId { get; set; } = string.Empty;

        [JsonProperty("value")]
        public object Value { get; set; } = new object();
    }

    public class SubOutputFieldConfig
    {
        [JsonProperty("prompt_name")]
        public string? PromptName { get; set; }

        [JsonProperty("prompt_system")]
        public string? PromptSystem { get; set; }

        [JsonProperty("prompt_text")]
        public string? PromptText { get; set; }

        [JsonProperty("temperature")]
        public double? Temperature { get; set; }

        [JsonProperty("max_number_of_words")]
        public int? MaxNumberOfWords { get; set; }

        [JsonProperty("number_of_results")]
        public int? NumberOfResults { get; set; }

        [JsonProperty("tone")]
        public string? Tone { get; set; }

        [JsonProperty("target_group")]
        public string? TargetGroup { get; set; }

        [JsonProperty("language_model")]
        public string? LanguageModel { get; set; }

        [JsonProperty("description_field")]
        public string? DescriptionField { get; set; }

        [JsonProperty("target_group_field")]
        public string? TargetGroupField { get; set; }

        [JsonProperty("media_data")]
        public MediaSettingConfig? MediaData { get; set; }

        [JsonProperty("specification_data")]
        public bool? SpecificationData { get; set; }

        [JsonProperty("product_data")]
        public List<ProductDataSchema>? ProductData { get; set; }

        [JsonProperty("parent_entity_data")]
        public List<LinkedEntityDataSchema>? ParentEntityData { get; set; }

        [JsonProperty("child_entity_data")]
        public List<LinkedEntityDataSchema>? ChildEntityData { get; set; }

        [JsonProperty("field_to_update_on_save")]
        public string? FieldToUpdateOnSave { get; set; }

        [JsonProperty("field_to_update_on_save_value")]
        public object? FieldToUpdateOnSaveValue { get; set; }

        [JsonProperty("field_to_update_on_save_value_append")]
        public bool? FieldToUpdateOnSaveValueAppend { get; set; }
    }

    public class OutputFieldSubConfig
    {
        [JsonProperty("key")]
        public string Key { get; set; } = string.Empty;

        [JsonProperty("default")]
        public bool Default { get; set; }

        [JsonProperty("criteria")]
        public List<SubConfigCriteria>? Criteria { get; set; }

        [JsonProperty("configuration")]
        public SubOutputFieldConfig Configuration { get; set; } = new SubOutputFieldConfig();
    }

    public class OutputFieldSchema
    {
        [JsonProperty("field_type_id")]
        public string FieldTypeId { get; set; } = string.Empty;

        [JsonProperty("sub_config")]
        public List<OutputFieldSubConfig>? SubConfig { get; set; }

        // Backward compatibility
        [JsonProperty("prompt_name")]
        public string? PromptName { get; set; }

        [JsonProperty("prompt_system")]
        public string? PromptSystem { get; set; }

        [JsonProperty("prompt_text")]
        public string? PromptText { get; set; }

        [JsonProperty("temperature")]
        public double? Temperature { get; set; }

        [JsonProperty("max_number_of_words")]
        public int? MaxNumberOfWords { get; set; }

        [JsonProperty("number_of_results")]
        public int? NumberOfResults { get; set; }

        [JsonProperty("tone")]
        public string? Tone { get; set; }

        [JsonProperty("target_group")]
        public string? TargetGroup { get; set; }

        [JsonProperty("language_model")]
        public string? LanguageModel { get; set; }

        [JsonProperty("description_field")]
        public string? DescriptionField { get; set; }

        [JsonProperty("target_group_field")]
        public string? TargetGroupField { get; set; }

        [JsonProperty("specification_data")]
        public bool? SpecificationData { get; set; }

        [JsonProperty("product_data")]
        public List<ProductDataSchema>? ProductData { get; set; }

        [JsonProperty("parent_entity_data")]
        public List<LinkedEntityDataSchema>? ParentEntityData { get; set; }

        [JsonProperty("child_entity_data")]
        public List<LinkedEntityDataSchema>? ChildEntityData { get; set; }

        [JsonProperty("field_to_update_on_save")]
        public string? FieldToUpdateOnSave { get; set; }

        [JsonProperty("field_to_update_on_save_value")]
        public object? FieldToUpdateOnSaveValue { get; set; }

        [JsonProperty("field_to_update_on_save_value_append")]
        public bool? FieldToUpdateOnSaveValueAppend { get; set; }
    }

    public class EntityLevelConfig
    {
        [JsonProperty("entityId")]
        public string EntityId { get; set; } = string.Empty;

        [JsonProperty("outputFields")]
        public List<OutputFieldSchema>? OutputFields { get; set; }

        [JsonProperty("generateTextFields")]
        public List<string>? GenerateTextFields { get; set; }

        [JsonProperty("categorizationFields")]
        public List<string>? CategorizationFields { get; set; }
    }

    public class BrandVoice
    {
        [JsonProperty("name")]
        public string Name { get; set; } = string.Empty;

        [JsonProperty("brandVoiceText")]
        public string BrandVoiceText { get; set; } = string.Empty;

        [JsonProperty("segment")]
        public string? Segment { get; set; }
    }

    public class SegmentGlossaryMappingConfig : GlossaryObject
    {
        [JsonProperty("segment")]
        public int Segment { get; set; }

        [JsonProperty("fields")]
        public List<FieldGlossaryMappingConfig>? Fields { get; set; }
    }

    public class GlossaryObject
    {
        [JsonProperty("glossaryId")]
        public int GlossaryId { get; set; }

        [JsonProperty("glossaryType")]
        public string GlossaryType { get; set; }

        [JsonProperty("negativeList")]
        public List<string>? NegativeList { get; set; }

        [JsonProperty("positiveList")]
        public List<string>? PositiveList { get; set; }
    }

    public class FieldGlossaryMappingConfig : GlossaryObject
    {
        [JsonProperty("fieldTypeId")]
        public string FieldTypeId { get; set; }

        [JsonProperty("criteria")]
        public List<FieldGlossaryMappingCriteria>? Criteria { get; set; }
    }

    public class FieldGlossaryMappingCriteria
    {
        [JsonProperty("fieldId")]
        public string FieldTypeId { get; set; }

        [JsonProperty("value")]
        public object Value { get; set; } // Any becomes object
    }

    public class CustomerConfig
    {
        [JsonProperty("id")]
        public string? Id { get; set; }

        [Required]
        [JsonProperty("customerName")]
        public string CustomerName { get; set; } = string.Empty;

        [Required]
        [JsonProperty("environmentSafeName")]
        public string EnvironmentSafeName { get; set; } = string.Empty;

        [Required]
        [JsonProperty("languages")]
        public List<string> Languages { get; set; } = new List<string>();

        [JsonProperty("entities")]
        public List<EntityLevelConfig>? Entities { get; set; } = new List<EntityLevelConfig>();

        [JsonProperty("translationContext")]
        public bool? TranslationContext { get; set; }

        [JsonProperty("translationServiceProviders")]
        public List<LanguageServiceProvider>? TranslationServiceProviders { get; set; } = new List<LanguageServiceProvider>();

        [JsonProperty("translationGlossaryConfig")]
        public List<SegmentGlossaryMappingConfig>? TranslationGlossaryConfig { get; set; }

        [JsonProperty("customTargetGroups")]
        public List<string>? CustomTargetGroups { get; set; } = new List<string>();

        [JsonProperty("customFieldMappings")]
        public List<Dictionary<string, object>>? CustomFieldMappings { get; set; }

        [JsonProperty("brand_voice")]
        public List<BrandVoice>? BrandVoice { get; set; }

        [JsonProperty("createDate")]
        public DateTime? CreateDate { get; set; }

        [JsonProperty("modifiedDate")]
        public DateTime ModifiedDate { get; set; } = DateTime.Now;

        // Backward compatibility
        [JsonProperty("presets")]
        public List<Dictionary<string, object>>? Presets { get; set; }

        [JsonProperty("creativityIndex")]
        public double? CreativityIndex { get; set; }

        [JsonProperty("maxNumOfWords")]
        public int? MaxNumOfWords { get; set; }

        [JsonProperty("textGenerationCount")]
        public int? TextGenerationCount { get; set; }

        [JsonProperty("textGenerationTone")]
        public string? TextGenerationTone { get; set; }

        [JsonProperty("textGenerationModel")]
        public string? TextGenerationModel { get; set; }
    }
}
