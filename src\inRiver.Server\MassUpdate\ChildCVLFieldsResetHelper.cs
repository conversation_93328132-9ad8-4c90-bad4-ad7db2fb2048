namespace inRiver.Server.MassUpdate
{
    using System;
    using System.Collections.Concurrent;
    using System.Collections.Generic;
    using System.Linq;
    using inRiver.Remoting.Log;
    using inRiver.Remoting.Objects;
    using inRiver.Server.Error;
    using inRiver.Server.Request;

    public static class ChildCVLFieldsResetHelper
    {
        public static ConcurrentDictionary<string, CVL> AllCVLs { get; set; } = new ConcurrentDictionary<string, CVL>();

        public static ConcurrentDictionary<string, IList<CVLValue>> CVLValues { get; set; } = new ConcurrentDictionary<string, IList<CVLValue>>();

        /// <summary>
        /// Resets values of child CVL fields if Parent CVL was changed.
        /// </summary>
        /// <param name="modifiedCVLFields">Array of modified CVL fields.</param>
        /// <param name="persistedEntity">Entity that should be updated during mass update.</param>
        /// <param name="context">inriver Request Context.</param>
        /// <remarks>
        /// If child CVL was changed together with Parent, new value of child CVL should be applied instead of being reset.
        /// If Parent is multivalue CVL, only those values of child CVL that are not related to a new value of Parent should be reset.
        /// </remarks>
        /// <exception cref="ArgumentException">Child CVL is mandatory and cannot be reset.</exception>
        /// <returns>Array of child CVL fields that should be reset after Parent CVL was changed.</returns>
        public static IEnumerable<Field> GetChildCVLFieldsToReset(
            IEnumerable<Field> modifiedCVLFields,
            Entity persistedEntity,
            RequestContext context)
        {
            var fieldsToReset = new List<Field>();
            var allCVLs = GetAllCVLs(context);
            var persistedEntityFields = new List<Field>(persistedEntity?.Fields);

            foreach (var modifiedCVLField in modifiedCVLFields)
            {
                if (!allCVLs.TryGetValue(modifiedCVLField.FieldType.CVLId, out var modifiedCVL))
                {
                    continue;
                }

                var childCVLIds = GetAllLevelChildCVLsForParent(allCVLs, modifiedCVL).Select(c => c.Id);
                var notModifiedChildCVLFields = GetNotModifiedChildCVLFields(persistedEntityFields, childCVLIds, modifiedCVLFields);

                foreach (var childCVLField in notModifiedChildCVLFields)
                {
                    if (childCVLField != null && !fieldsToReset.Any(f => f.FieldType.Id == childCVLField.FieldType.Id))
                    {
                        var childCVLId = childCVLField.FieldType.CVLId;
                        var childCvlValues = CVLValues.GetOrAdd(childCVLId, context.DataPersistance.GetCVLValuesForCVL);
                        var hasParentRelationship = childCvlValues.Any(v => !string.IsNullOrWhiteSpace(v.ParentKey));
                        if (!hasParentRelationship)
                        {
                            continue;
                        }
                        
                        var parentCVLKeys = GetParentCVLKeys(childCVLId, modifiedCVLFields, fieldsToReset, persistedEntityFields, allCVLs);

                        var allowedCVLKeys = childCvlValues
                            .Select(v => parentCVLKeys.Contains(v.ParentKey) ? v.Key : null)
                            .Where(c => c != null);

                        var childCVLData = childCVLField.Data?.ToString() ?? string.Empty;

                        if (childCVLField.FieldType.Multivalue)
                        {
                            var data = string.Join(";", childCVLData
                                .Split(';', StringSplitOptions.RemoveEmptyEntries)
                                .Where(key => allowedCVLKeys.Contains(key)));

                            if (!string.Equals(data, childCVLData, StringComparison.Ordinal))
                            {
                                childCVLField.SetCVLValue(data, context);
                                fieldsToReset.Add(childCVLField);
                            }
                        }
                        else if (!allowedCVLKeys.Contains(childCVLData) && childCVLData != null)
                        {
                            childCVLField.SetCVLValue(null, context);
                            fieldsToReset.Add(childCVLField);
                        }
                    }
                }
            }

            return fieldsToReset;
        }

        public static void ResetCacheValues()
        {
            AllCVLs?.Clear();
            CVLValues?.Clear();
        }

        private static string[] GetParentCVLKeys(
            string cvlId,
            IEnumerable<Field> modifiedFields,
            IEnumerable<Field> resetFields,
            IEnumerable<Field> persistentFields,
            ConcurrentDictionary<string, CVL> allCVLs)
        {
            var parentId = allCVLs.GetValueOrDefault(cvlId)?.ParentId;
            if (parentId is null)
            {
                return Array.Empty<string>();
            }

            if (TryGetKeys(modifiedFields, parentId, out var keys))
            {
                return keys;
            }

            if (TryGetKeys(resetFields, parentId, out keys))
            {
                return keys;
            }

            return TryGetKeys(persistentFields, parentId, out keys)
                ? keys
                : Array.Empty<string>();
        }

        private static bool TryGetKeys(IEnumerable<Field> fields, string parentId, out string[] keys)
        {
            keys = Array.Empty<string>();
            var parent = fields.FirstOrDefault(f => f.FieldType.CVLId == parentId);
            if (parent is null)
            {
                return false;
            }

            if (parent.Data != null)
            {
                keys = parent.Data.ToString().Split(';', StringSplitOptions.RemoveEmptyEntries);
            }

            return true;
        }

        private static ConcurrentDictionary<string, CVL> GetAllCVLs(RequestContext context)
        {
            if (AllCVLs is null || !AllCVLs.Any())
            {
                AllCVLs = new ConcurrentDictionary<string, CVL>(context.DataPersistance.GetAllCVLs().ToDictionary(x => x.Id, x => x));
            }

            return AllCVLs;
        }

        private static void SetCVLValue(this Field childCVLField, object data, RequestContext context)
        {
            if (childCVLField.FieldType.Mandatory && string.IsNullOrWhiteSpace(data?.ToString()))
            {
                var msg = $"Trying to reset mandatory child CVL value when parent CVL was updated";
                context.Log(LogLevel.Warning, msg);
                throw ErrorUtility.GetArgumentException("SetCVLValue", childCVLField.FieldType.Id, msg);
            }

            childCVLField.Data = data;
            childCVLField.Revision += 1;
        }

        private static IEnumerable<Field> GetNotModifiedChildCVLFields(
            IEnumerable<Field> persistedEntityFields,
            IEnumerable<string> childCVLIds,
            IEnumerable<Field> modifiedCVLFields)
        {
            return persistedEntityFields.Where(field =>
                childCVLIds.Contains(field.FieldType.CVLId)
                && !modifiedCVLFields.Any(f => f.FieldType.Id == field.FieldType.Id));
        }

        private static IEnumerable<CVL> GetAllLevelChildCVLsForParent(IDictionary<string, CVL> allCVLs, CVL parent)
        {
            var cvls = new List<CVL>();
            var childCvls = allCVLs.Values.Where(cvl => cvl.ParentId == parent.Id).ToList();

            if (!childCvls.Any())
            {
                return cvls;
            }

            foreach (var childCvl in childCvls)
            {
                if (!cvls.Any(cvl => cvl.Id == childCvl.Id))
                {
                    cvls.Add(childCvl);
                    cvls.AddRange(GetAllLevelChildCVLsForParent(allCVLs, childCvl));
                }
            }

            return cvls;
        }
    }
}
