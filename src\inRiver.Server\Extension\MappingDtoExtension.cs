namespace inRiver.Server.Extension
{
    using System.Collections.Generic;
    using System.Linq;
    using System.Text.Json;
    using inRiver.Core.Models;
    using inRiver.Server.Syndication;

    public static class MappingDtoExtension
    {
        public static SyndicationMapping ConvertToSyndicationMapping(this DsaMappingDto mappingDto) => Convert(mappingDto.Data);

        public static SyndicationMapping ConvertToSyndicationMapping(this MappingDto mappingDto) => Convert(mappingDto.Data);

        private static SyndicationMapping Convert(object data)
        {
            var dataAsDictionary = JsonSerializer.Deserialize<Dictionary<string, object>>(data.ToString());
            var mappingModelList =
                JsonSerializer.Deserialize<object[]>(dataAsDictionary["MappingModelList"].ToString());

            var syndicationMappingFields = new List<SyndicationMappingField>();

            var mappingId = JsonSerializer.Deserialize<int>(dataAsDictionary["MappingId"]?.ToString());
            var mappingName = dataAsDictionary["MappingName"].ToString();
            var workareaEntityTypeId = dataAsDictionary["WorkareaEntityTypeId"].ToString();
            var firstRelatedEntityTypeId = dataAsDictionary["FirstRelatedEntityTypeId"]?.ToString();
            var secondRelatedEntityTypeId = dataAsDictionary["SecondRelatedEntityTypeId"]?.ToString();
            var enableSKU = GetBoolean((JsonElement)dataAsDictionary["EnableSKU"]);
            var formatId = JsonSerializer.Deserialize<int>(dataAsDictionary["FormatId"].ToString());
            var imageUrl = dataAsDictionary["ImageUrl"]?.ToString();

            foreach (var mappingModel in mappingModelList)
            {
                var mappingModelDictionary = JsonSerializer.Deserialize<Dictionary<string, object>>(mappingModel.ToString());

                syndicationMappingFields.Add(new SyndicationMappingField
                {
                    MappingFormatId = formatId,
                    MappingId = mappingId,
                    MappingName = mappingName,
                    MappingFormatImageUrl = imageUrl,
                    WorkareaEntityTypeId = workareaEntityTypeId,
                    FirstRelatedEntityTypeId = firstRelatedEntityTypeId,
                    SecondRelatedEntityTypeId = secondRelatedEntityTypeId,
                    MappingFormatFieldId = TryGetFormatFieldId(mappingModelDictionary),
                    MappingFormatName = mappingModelDictionary["Category"].ToString(),
                    EntityTypeId = TryGetStringValue(mappingModelDictionary, "inRiverEntityTypeId"),
                    FieldTypeId = TryGetStringValue(mappingModelDictionary, "inRiverFieldTypeId"),
                    FieldDataType = TryGetStringValue(mappingModelDictionary, "inRiverDataType"),
                    DataType = mappingModelDictionary["FormatDataType"].ToString(),
                    MapFieldTypeId = mappingModelDictionary["FormatField"].ToString(),
                    MapPath = TryGetStringValue(mappingModelDictionary, "Path"),
                    Enumerations = GetEnumerations(mappingModelDictionary),
                    UnitType = TryGetStringValue(mappingModelDictionary, "UnitType"),
                    UnitCvl = TryGetStringValue(mappingModelDictionary, "UnitCvl"),
                    UnitDefaultValue = TryGetStringValue(mappingModelDictionary, "UnitDefaultValue"),
                    ConverterId = JsonSerializer.Deserialize<int>(mappingModelDictionary["ConverterId"]?.ToString() ?? "0"),
                    Converter = TryGetStringValue(mappingModelDictionary, "ConverterClass"),
                    Args = TryGetStringValue(mappingModelDictionary, "ConverterArgs"),
                    Mandatory = GetBoolean((JsonElement)mappingModelDictionary["Mandatory"]),
                    Unique = GetBoolean((JsonElement)mappingModelDictionary["Unique"]),
                    Recommended = GetBoolean((JsonElement)mappingModelDictionary["Recommended"]),
                    DefaultValue = TryGetStringValue(mappingModelDictionary, "DefaultValue"),
                    MaxLength = JsonSerializer.Deserialize<int>(mappingModelDictionary["MaxLength"]?.ToString() ?? "0"),
                    MinLength = JsonSerializer.Deserialize<int>(mappingModelDictionary["MinLength"]?.ToString() ?? "0"),
                    EnableSKU = enableSKU,
                    Description = TryGetStringValue(mappingModelDictionary, "Description"),
                });
            }

            var syndicateMapping = new SyndicationMapping
            {
                MappingId = mappingId,
                MappingName = mappingName,
                OutputEntityTypeId = dataAsDictionary["OutputEntityTypeId"]?.ToString(),
                WorkareaEntityTypeId = workareaEntityTypeId,
                FirstRelatedEntityTypeId = firstRelatedEntityTypeId,
                FirstLinkEntityTypeId = dataAsDictionary["FirstLinkEntityTypeId"]?.ToString(),
                SecondRelatedEntityTypeId = secondRelatedEntityTypeId,
                SecondLinkEntityTypeId = dataAsDictionary["SecondLinkEntityTypeId"]?.ToString(),
                EnableSKU = enableSKU,
                FormatId = formatId,
                DefaultLanguage = dataAsDictionary["DefaultLanguage"]?.ToString(),
                SyndicationMappingFields = syndicationMappingFields
            };
            return syndicateMapping;
        }

        public static List<MapEnumeration> GetEnumerations(IDictionary<string, object> mappingModelDictionary)
        {
            if (!mappingModelDictionary.TryGetValue("Enumerations", out var enumerations) || enumerations == null)
            {
                return new List<MapEnumeration>();
            }

            if (enumerations is JsonElement jsonElement && jsonElement.ValueKind == JsonValueKind.Array)
            {
                try
                {
                    var parsedEnumerations = JsonSerializer.Deserialize<List<MapEnumeration>>(jsonElement.GetRawText());
                    var hasValidData = parsedEnumerations.Any(x => !string.IsNullOrEmpty(x.FieldValue));

                    return hasValidData
                        ? parsedEnumerations
                        : new List<MapEnumeration>();
                }
                catch
                {
                    return new List<MapEnumeration>();
                }
            }

            return new List<MapEnumeration>();
        }

        public static int TryGetFormatFieldId(IDictionary<string, object> mappingModelDictionary)
        {
            const int defaultFormatFieldId = -1;
            if (mappingModelDictionary == null)
            {
                return defaultFormatFieldId;
            }

            return mappingModelDictionary.TryGetValue("FormatFieldId", out var formatFieldIdString)
                ? int.TryParse(formatFieldIdString?.ToString(), out var formatFieldId) ? formatFieldId : defaultFormatFieldId
                : defaultFormatFieldId;
        }

        private static bool GetBoolean(JsonElement element)
        {
            return element.ValueKind switch
            {
                JsonValueKind.True => true,
                JsonValueKind.False => false,
                _ => false,
            };
        }

        private static string TryGetStringValue(IDictionary<string, object> mappingModelDictionary, string key)
            => mappingModelDictionary.TryGetValue(key, out var value) ? value?.ToString() : null;
    }
}
